﻿using Contracts;
using Microsoft.AspNetCore.Mvc;
using MemberServiceBusinessLayer;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Identity.Web.Resource;

namespace MemberServiceApi.Controllers
{
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes")]
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class UpToDateController : ControllerBase
    {
        private readonly IUpToDateQueryHandler<UpToDate> _upToDateQueryHandler;
        private readonly IUpToDateCommandHandler<UpToDate> _upToDateCommandHandler;
        private readonly ILogger<UpToDateController> _logger;
        private readonly IStringLocalizer<UpToDateController> _localizer;

        public UpToDateController(
            IUpToDateQueryHandler<UpToDate> upToDateQ<PERSON>y<PERSON>and<PERSON>,
            IUpToDateCommandHandler<UpToDate> upToDateCommandHand<PERSON>,
            ILogger<UpToDateController> logger,
            IStringLocalizer<UpToDateController> localizer)
        {
            _upToDateQueryHandler = upToDateQueryHandler ?? throw new ArgumentNullException(nameof(upToDateQueryHandler));
            _upToDateCommandHandler = upToDateCommandHandler ?? throw new ArgumentNullException(nameof(upToDateCommandHandler));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _localizer = localizer ?? throw new ArgumentNullException(nameof(localizer));
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetUpToDateById(Guid id)
        {
            IActionResult response;
            _logger.LogInformation(_localizer["FetchingUpToDateWithID"], id);
            var upToDate = await _upToDateQueryHandler.GetUpToDateByIdAsync(id);
            if (upToDate == null)
            {
                _logger.LogWarning(_localizer["UpToDateNotFound"], id);
                response = NotFound(_localizer["UpToDateNotFoundMessage"]);
            }
            else
            {
                _logger.LogInformation(_localizer["UpToDateFetchedSuccessfully"], id);
                response = Ok(upToDate);
            }
            return response;
        }

        [HttpPost]
        public async Task<IActionResult> AddUpToDate([FromBody] UpToDate upToDate)
        {
            IActionResult response;
            if (upToDate == null)
            {
                _logger.LogWarning(_localizer["InvalidUpToDateData"]);
                response = BadRequest(_localizer["UpToDateInvalidMessage"]);
            }
            else
            {
                try
                {
                    _logger.LogInformation(_localizer["AddingNewUpToDate"]);
                    await _upToDateCommandHandler.AddUpToDateAsync(new List<UpToDate> { upToDate });
                    _logger.LogInformation(_localizer["UpToDateAddedSuccessfully"], upToDate.Id);
                    response = CreatedAtAction(nameof(GetUpToDateById), new { id = upToDate.Id }, upToDate);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["ErrorAddingUpToDate"]);
                    response = StatusCode(500, _localizer["InternalServerErrorMessage"]);
                }
            }
            return response;
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateUpToDate(Guid id, [FromBody] UpToDate upToDate)
        {
            IActionResult response;
            if (upToDate == null || id != upToDate.Id)
            {
                _logger.LogWarning(_localizer["InvalidDataForUpdate"], id);
                response = BadRequest(_localizer["UpToDateInvalidMessage"]);
            }
            else
            {
                try
                {
                    _logger.LogInformation(_localizer["UpdatingUpToDateWithID"], id);
                    await _upToDateCommandHandler.UpdateUpToDateAsync(upToDate);
                    _logger.LogInformation(_localizer["UpToDateUpdatedSuccessfully"], id);
                    response = NoContent();
                }
                catch (KeyNotFoundException)
                {
                    _logger.LogWarning(_localizer["UpToDateNotFoundForUpdate"], id);
                    response = NotFound(_localizer["UpToDateNotFoundMessage"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["ErrorUpdatingUpToDate"]);
                    response = StatusCode(500, _localizer["InternalServerErrorMessage"]);
                }
            }
            return response;
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteUpToDate(Guid id)
        {
            IActionResult response;
            try
            {
                _logger.LogInformation(_localizer["DeletingUpToDateWithID"], id);
                await _upToDateCommandHandler.DeleteUpToDateAsync(id);
                _logger.LogInformation(_localizer["UpToDateDeletedSuccessfully"], id);
                response = NoContent();
            }
            catch (KeyNotFoundException)
            {
                _logger.LogWarning(_localizer["UpToDateNotFoundForDeletion"], id);
                response = NotFound(_localizer["UpToDateNotFoundMessage"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorDeletingUpToDate"]);
                response = StatusCode(500, _localizer["InternalServerErrorMessage"]);
            }
            return response;
        }

        [HttpGet]
        public async Task<IActionResult> GetAllUpToDates()
        {
            IActionResult response;
            _logger.LogInformation(_localizer["FetchingAllUpToDates"]);
            try
            {
                var upToDates = await _upToDateQueryHandler.GetAllUpToDatesAsync();

                if (upToDates == null || upToDates.Count == 0)
                {
                    _logger.LogWarning(_localizer["NoUpToDatesFound"]);
                    response = NotFound(_localizer["UpToDatesNotFoundMessage"]);
                }
                else
                {
                    _logger.LogInformation(_localizer["AllUpToDatesFetchedSuccessfully"]);
                    response = Ok(upToDates);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingUpToDates"]);
                response = StatusCode(500, _localizer["InternalServerErrorMessage"]);
            }
            return response;
        }
    }
}