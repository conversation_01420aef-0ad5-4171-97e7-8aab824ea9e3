﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using PracticeContracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using PracticeDataAccessLayer.PracticeDataAccessLayerResources;

namespace PracticeDataAccessLayer.Context
{
    public class PracticeDatabaseContext:DbContext
    {
        private readonly IStringLocalizer<PracticeDataAccessLayerStrings> _localizer;
        private readonly ILogger<PracticeDatabaseContext> _logger;
        public PracticeDatabaseContext(DbContextOptions<PracticeDatabaseContext> options,
                                      IStringLocalizer<PracticeDataAccessLayerStrings> localizer,
                                      ILogger<PracticeDatabaseContext> logger)
            : base(options)
        {
            _localizer = localizer ?? throw new ArgumentNullException(nameof(localizer));
            _logger = logger;
        }

        public DbSet<Tasks> Tasks { get; set; }
        public DbSet<ProviderPatient> ProviderPatients { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            try
            {

                modelBuilder.Entity<Tasks>().ToTable(_localizer["Tasks"], _localizer["Practice"]);
                modelBuilder.Entity<Tasks>().Property(m => m.Id).HasDefaultValueSql(_localizer["NewIdFunction"]);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["DatabaseError"]);
            }
        }
    }
}
