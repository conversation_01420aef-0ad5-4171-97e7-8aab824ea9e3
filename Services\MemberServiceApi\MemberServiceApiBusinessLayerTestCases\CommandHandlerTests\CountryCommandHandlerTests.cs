using NUnit.Framework;
using Moq;
using MemberServiceBusinessLayer.CommandHandler;
using MemberServiceDataAccessLayer.Implementation;
using Contracts;
using System;
using System.Threading.Tasks;

namespace MemberServiceApiBusinessLayerTestCases.CommandHandlerTests
{
    [TestFixture]
    public class CountryCommandHandlerTests
    {
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<ICountryRepository> _mockCountryRepository;
        private CountryCommandHandler _countryCommandHandler;

        [SetUp]
        public void Setup()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockCountryRepository = new Mock<ICountryRepository>();

            _mockUnitOfWork.Setup(u => u.CountryRepository).Returns(_mockCountryRepository.Object);

            _countryCommandHandler = new CountryCommandHandler(_mockUnitOfWork.Object);
        }

        [Test]
        public async Task AddCountryAsync_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var country = new Country
            {
                CountryId = Guid.NewGuid(),
                CountryName = "United States"
            };

            _mockCountryRepository
                .Setup(r => r.AddAsync(country))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _countryCommandHandler.AddCountryAsync(country);

            // Assert
            _mockCountryRepository.Verify(r => r.AddAsync(country), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateCountryAsync_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var country = new Country
            {
                CountryId = Guid.NewGuid(),
                CountryName = "United States"
            };

            _mockCountryRepository
                .Setup(r => r.UpdateAsync(country))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _countryCommandHandler.UpdateCountryAsync(country);

            // Assert
            _mockCountryRepository.Verify(r => r.UpdateAsync(country), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteCountryAsync_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var countryId = Guid.NewGuid();

            _mockCountryRepository
                .Setup(r => r.DeleteByIdAsync(countryId))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _countryCommandHandler.DeleteCountryAsync(countryId);

            // Assert
            _mockCountryRepository.Verify(r => r.DeleteByIdAsync(countryId), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public void AddCountryAsync_ShouldThrowArgumentNullException_WhenCountryIsNull()
        {
            // Act & Assert
            Assert.ThrowsAsync<ArgumentNullException>(async () =>
                await _countryCommandHandler.AddCountryAsync(null));

            _mockCountryRepository.Verify(r => r.AddAsync(It.IsAny<Country>()), Times.Never);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Never);
        }

        [Test]
        public void UpdateCountryAsync_ShouldThrowArgumentNullException_WhenCountryIsNull()
        {
            // Act & Assert
            Assert.ThrowsAsync<ArgumentNullException>(async () =>
                await _countryCommandHandler.UpdateCountryAsync(null));

            _mockCountryRepository.Verify(r => r.UpdateAsync(It.IsAny<Country>()), Times.Never);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Never);
        }

        
    }
}
