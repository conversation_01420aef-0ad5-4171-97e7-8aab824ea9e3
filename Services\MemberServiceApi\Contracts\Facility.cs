﻿using Microsoft.EntityFrameworkCore.Metadata;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Contracts
{
    public class Facility : IContract
    {
        public Guid FacilityId { get; set; } 

        public string FacilityName { get; set; } 

        public string StreetName { get; set; } 

        public string City { get; set; } 

        public string State { get; set; } 

        public string Zipcode { get; set; } 

        public string Country { get; set; } 
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime? UpdatedDate { get; set; }
        public Guid? UpdatedBy { get; set; }
        public Boolean? IsActive { get; set; } = true;
        public Guid? OrganizationID { get; set; }
        public bool Subscription { get; set; }
    }
}
