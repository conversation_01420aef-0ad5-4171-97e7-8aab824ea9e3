using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using MemberServiceBusinessLayer.CommandHandler;
using MemberServiceDataAccessLayer.Implementation;
using MemberServiceDataAccessLayer;
using Contracts;
using System;
using System.Threading.Tasks;

namespace MemberServiceApiBusinessLayerTestCases.CommandHandlerTests
{
    [TestFixture]
    public class VisitStatusCommandHandlerTests
    {
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<IVisitStatusRepository> _mockVisitStatusRepository;
        private Mock<ILogger<VisitStatusCommandHandler>> _mockLogger;
        private Mock<IStringLocalizer<VisitStatusCommandHandler>> _mockLocalizer;
        private VisitStatusCommandHandler _visitStatusCommandHandler;

        [SetUp]
        public void Setup()
        {
            _mockConfiguration = new Mock<IConfiguration>();
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockVisitStatusRepository = new Mock<IVisitStatusRepository>();
            _mockLogger = new Mock<ILogger<VisitStatusCommandHandler>>();
            _mockLocalizer = new Mock<IStringLocalizer<VisitStatusCommandHandler>>();

            _mockUnitOfWork.Setup(u => u.VisitStatusRepository).Returns(_mockVisitStatusRepository.Object);

            // Setup localizer to return the input string
            _mockLocalizer.Setup(l => l[It.IsAny<string>()]).Returns<string>(s => new LocalizedString(s, s));

            _visitStatusCommandHandler = new VisitStatusCommandHandler(
                _mockConfiguration.Object,
                _mockUnitOfWork.Object,
                _mockLogger.Object,
                _mockLocalizer.Object
            );
        }

        [Test]
        public async Task UpdateVisitStatusAsync_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var visitStatusId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var visitStatus = new VisitStatus 
            { 
                ID = visitStatusId, 
                Visitstatus = "Completed"
            };

            _mockVisitStatusRepository
                .Setup(r => r.UpdateAsync(visitStatus, orgId, subscription))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _visitStatusCommandHandler.UpdateVisitStatusAsync(visitStatus, orgId, subscription);

            // Assert
            _mockVisitStatusRepository.Verify(r => r.UpdateAsync(visitStatus, orgId, subscription), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteVisitStatusByIdAsync_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var visitStatusId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockVisitStatusRepository
                .Setup(r => r.DeleteByIdAsync(visitStatusId, orgId, subscription))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _visitStatusCommandHandler.DeleteVisitStatusByIdAsync(visitStatusId, orgId, subscription);

            // Assert
            _mockVisitStatusRepository.Verify(r => r.DeleteByIdAsync(visitStatusId, orgId, subscription), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteVisitStatusByEntityAsync_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var visitStatusId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var visitStatus = new VisitStatus 
            { 
                ID = visitStatusId, 
                Visitstatus = "Cancelled"
            };

            _mockVisitStatusRepository
                .Setup(r => r.DeleteByEntityAsync(visitStatus, orgId, subscription))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _visitStatusCommandHandler.DeleteVisitStatusByEntityAsync(visitStatus, orgId, subscription);

            // Assert
            _mockVisitStatusRepository.Verify(r => r.DeleteByEntityAsync(visitStatus, orgId, subscription), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public void UpdateVisitStatusAsync_ShouldRethrowException_WhenRepositoryThrowsException()
        {
            // Arrange
            var visitStatusId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var visitStatus = new VisitStatus 
            { 
                ID = visitStatusId, 
                Visitstatus = "Completed"
            };

            _mockVisitStatusRepository
                .Setup(r => r.UpdateAsync(visitStatus, orgId, subscription))
                .ThrowsAsync(new Exception("Test exception"));

            // Act & Assert
            Assert.ThrowsAsync<Exception>(() => _visitStatusCommandHandler.UpdateVisitStatusAsync(visitStatus, orgId, subscription));
        }
    }
}
