using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using PracticeBusinessLayer.CommandHandler;
using PracticeContracts;
using PracticeDataAccessLayer.Context;
using PracticeDataAccessLayer.Implementation;
using PracticeDataAccessLayer.PracticeDataAccessLayerResources;
using ShardModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Interfaces.ShardManagement;

namespace PracticeApiBusinessLayerTestCases
{
    [TestFixture]
    public class PracticeCommandHandlerTests
    {
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<ILogger<PracticeCommandHandler>> _mockLogger;
        private Mock<IMigration<PracticeDatabaseContext, Tasks, PracticeDataAccessLayerStrings>> _mockMigration;
        private Mock<IPracticeRepository> _mockPracticeRepository;
        private PracticeCommandHandler _commandHandler;
        private Guid _testOrgId;
        private bool _testSubscription;

        [SetUp]
        public void Setup()
        {
            // Initialize mocks
            _mockConfiguration = new Mock<IConfiguration>();
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockLogger = new Mock<ILogger<PracticeCommandHandler>>();
            _mockMigration = new Mock<IMigration<PracticeDatabaseContext, Tasks, PracticeDataAccessLayerStrings>>();
            _mockPracticeRepository = new Mock<IPracticeRepository>();

            // Setup repository mock
            _mockUnitOfWork.Setup(u => u.PracticeRepository).Returns(_mockPracticeRepository.Object);

            // Create command handler with mocked dependencies
            _commandHandler = new PracticeCommandHandler(
                _mockConfiguration.Object,
                _mockUnitOfWork.Object,
                _mockLogger.Object,
                _mockMigration.Object
            );

            // Test data
            _testOrgId = Guid.NewGuid();
            _testSubscription = true;
        }

        [Test]
        public async Task AddTasks_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var tasks = new List<Tasks>
            {
                new Tasks { Id = Guid.NewGuid(), PatientName = "Test Patient 1", TaskType = "Appointment", Subject = "Annual Checkup" },
                new Tasks { Id = Guid.NewGuid(), PatientName = "Test Patient 2", TaskType = "Follow-up", Subject = "Post-Surgery" }
            };

            _mockPracticeRepository.Setup(r => r.AddAsync(tasks, _testOrgId, _testSubscription));
            _mockUnitOfWork.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _commandHandler.AddTasks(tasks, _testOrgId, _testSubscription);

            // Assert
            _mockPracticeRepository.Verify(r => r.AddAsync(tasks, _testOrgId, _testSubscription), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateTasks_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var task = new Tasks { Id = Guid.NewGuid(), PatientName = "Updated Patient", TaskType = "Medication", Subject = "Prescription Renewal" };

            _mockPracticeRepository.Setup(r => r.UpdateAsync(task, _testOrgId, _testSubscription));
            _mockUnitOfWork.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _commandHandler.UpdateTasks(task, _testOrgId, _testSubscription);

            // Assert
            _mockPracticeRepository.Verify(r => r.UpdateAsync(task, _testOrgId, _testSubscription), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteTasksById_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var taskId = Guid.NewGuid();

            _mockPracticeRepository.Setup(r => r.DeleteByIdAsync(taskId, _testOrgId, _testSubscription));
            _mockUnitOfWork.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _commandHandler.DeleteTasksById(taskId, _testOrgId, _testSubscription);

            // Assert
            _mockPracticeRepository.Verify(r => r.DeleteByIdAsync(taskId, _testOrgId, _testSubscription), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteTasksByEntity_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var task = new Tasks { Id = Guid.NewGuid(), PatientName = "Patient to Delete", TaskType = "Lab", Subject = "Blood Work" };

            _mockPracticeRepository.Setup(r => r.DeleteByEntityAsync(task, _testOrgId, _testSubscription));
            _mockUnitOfWork.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _commandHandler.DeleteTasksByEntity(task, _testOrgId, _testSubscription);

            // Assert
            _mockPracticeRepository.Verify(r => r.DeleteByEntityAsync(task, _testOrgId, _testSubscription), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateTasksByPatientId_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var task = new Tasks { Id = Guid.NewGuid(), PatientName = "Patient with Updated Info", TaskType = "Referral", Subject = "Specialist Consultation" };

            _mockPracticeRepository.Setup(r => r.UpdateTasksByPatientIdAsync(task, _testOrgId, _testSubscription))
                .ReturnsAsync(true);
            _mockUnitOfWork.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _commandHandler.UpdateTasksByPatientId(task, _testOrgId, _testSubscription);

            // Assert
            _mockPracticeRepository.Verify(r => r.UpdateTasksByPatientIdAsync(task, _testOrgId, _testSubscription), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }


    }
}
