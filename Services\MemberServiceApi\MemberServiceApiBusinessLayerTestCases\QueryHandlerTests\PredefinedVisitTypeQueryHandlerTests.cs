using NUnit.Framework;
using Moq;
using MemberServiceBusinessLayer.QueryHandler;
using MemberServiceDataAccessLayer.Implementation;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;

namespace MemberServiceApiBusinessLayerTestCases.QueryHandlerTests
{
    [TestFixture]
    public class PredefinedVisitTypeQueryHandlerTests
    {
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<IPredefinedVisitTypeRepository> _mockPredefinedVisitTypeRepository;
        private PredefinedVisitTypeQueryHandler _predefinedVisitTypeQueryHandler;

        [SetUp]
        public void Setup()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockPredefinedVisitTypeRepository = new Mock<IPredefinedVisitTypeRepository>();

            _mockUnitOfWork.Setup(u => u.PredefinedVisitTypeRepository).Returns(_mockPredefinedVisitTypeRepository.Object);

            _predefinedVisitTypeQueryHandler = new PredefinedVisitTypeQueryHandler(_mockUnitOfWork.Object);
        }

        [Test]
        public async Task GetPredefinedVisitType_ShouldReturnAllPredefinedVisitTypes()
        {
            // Arrange
            var expectedVisitTypes = new List<PredefinedVisitType>
            {
                new PredefinedVisitType
                {
                    ID = Guid.NewGuid(),
                    VisitName = "Initial Consultation",
                    CPTCode = "99201"
                },
                new PredefinedVisitType
                {
                    ID = Guid.NewGuid(),
                    VisitName = "Follow-up Visit",
                    CPTCode = "99211"
                }
            };

            _mockPredefinedVisitTypeRepository
                .Setup(r => r.GetAllAsync())
                .ReturnsAsync(expectedVisitTypes);

            // Act
            var result = await _predefinedVisitTypeQueryHandler.GetPredefinedVisitType();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(2));
            Assert.That(result, Is.EquivalentTo(expectedVisitTypes));
            _mockPredefinedVisitTypeRepository.Verify(r => r.GetAllAsync(), Times.Once);
        }

        [Test]
        public void Constructor_ShouldThrowArgumentNullException_WhenUnitOfWorkIsNull()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new PredefinedVisitTypeQueryHandler(null));
        }
    }
}
