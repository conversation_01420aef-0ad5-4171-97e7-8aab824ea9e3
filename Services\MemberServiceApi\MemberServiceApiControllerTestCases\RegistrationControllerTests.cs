using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using MemberServiceApi.Controllers;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using MemberServiceBusinessLayer;

namespace MemberServiceApiControllerTestCases
{
    [TestFixture]
    public class RegistrationControllerTests
    {
        private Mock<IMemberCommandHandler<Member>> _mockCommandHandler;
        private Mock<IMemberQueryHandler<Member>> _mockQueryHandler;
        private Mock<ICheckAccessQueryHandler<ProductAccess>> _mockCheckAccessQueryHandler;
        private Mock<ILogger<RegistrationController>> _mockLogger;
        private Mock<IStringLocalizer<RegistrationController>> _mockLocalizer;
        private Mock<IOrganizationsQueryHandler<Organization>> _mockOrganizationQueryHandler;
        private RegistrationController _controller;

        [SetUp]
        public void Setup()
        {
            _mockCommandHandler = new Mock<IMemberCommandHandler<Member>>();
            _mockQueryHandler = new Mock<IMemberQueryHandler<Member>>();
            _mockCheckAccessQueryHandler = new Mock<ICheckAccessQueryHandler<ProductAccess>>();
            _mockLogger = new Mock<ILogger<RegistrationController>>();
            _mockLocalizer = new Mock<IStringLocalizer<RegistrationController>>();
            _mockOrganizationQueryHandler = new Mock<IOrganizationsQueryHandler<Organization>>();

            // Setup localizer to return the key when requested
            _mockLocalizer.Setup(l => l[It.IsAny<string>()]).Returns((string key) => new LocalizedString(key, key));

            _controller = new RegistrationController(
                _mockCommandHandler.Object,
                _mockQueryHandler.Object,
                _mockLogger.Object,
                _mockLocalizer.Object,
                _mockCheckAccessQueryHandler.Object,
                _mockOrganizationQueryHandler.Object
            );
        }

        [Test]
        public async Task Get_WhenMembersExist_ReturnsOkWithMembers()
        {
            // Arrange
            var mockMembers = new List<Member>
            {
                new Member { Id = Guid.NewGuid(), FirstName = "John", LastName = "Doe" },
                new Member { Id = Guid.NewGuid(), FirstName = "Jane", LastName = "Smith" }
            };

            _mockQueryHandler
                .Setup(q => q.GetMember())
                .ReturnsAsync(mockMembers);

            // Act
            var result = await _controller.Get();

            // Assert
            Assert.That(result.Result, Is.InstanceOf<OkObjectResult>());
            var okResult = result.Result as OkObjectResult;
            Assert.That(okResult.StatusCode, Is.EqualTo(200));
            Assert.That(okResult.Value, Is.EqualTo(mockMembers));
        }

        [Test]
        public async Task Get_WhenNoMembersExist_ReturnsOk()
        {
            // Arrange
            _mockQueryHandler
                .Setup(q => q.GetMember())
                .ReturnsAsync(new List<Member>());

            // Act
            var result = await _controller.Get();

            // Assert
            Assert.That(result.Result, Is.InstanceOf<OkObjectResult>());
            var okResult = result.Result as OkObjectResult;
            Assert.That(okResult.StatusCode, Is.EqualTo(200));
        }

        [Test]
        public async Task Get_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            _mockQueryHandler
                .Setup(q => q.GetMember())
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.Get();

            // Assert
            Assert.That(result.Result, Is.InstanceOf<StatusCodeResult>());
            var statusCodeResult = result.Result as StatusCodeResult;
            Assert.That(statusCodeResult.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task GetById_WhenMemberExists_ReturnsOkWithMember()
        {
            // Arrange
            var memberId = Guid.NewGuid();
            var organizationId = Guid.NewGuid();
            bool subscription = false;
            var mockMember = new Member
            {
                Id = memberId,
                FirstName = "John",
                LastName = "Doe",
                OrganizationID = organizationId,
                Subscription = subscription
            };

            _mockQueryHandler
                .Setup(q => q.GetMemberById(memberId, organizationId, subscription))
                .ReturnsAsync(mockMember);

            // Act
            var result = await _controller.GetById(memberId, organizationId, subscription);

            // Assert
            Assert.That(result.Result, Is.InstanceOf<OkObjectResult>());
            var okResult = result.Result as OkObjectResult;
            Assert.That(okResult.StatusCode, Is.EqualTo(200));
            Assert.That(okResult.Value, Is.EqualTo(mockMember));
        }

        [Test]
        public async Task GetById_WhenMemberDoesNotExist_ReturnsNotFound()
        {
            // Arrange
            var memberId = Guid.NewGuid();
            var organizationId = Guid.NewGuid();
            bool subscription = false;

            _mockQueryHandler
                .Setup(q => q.GetMemberById(memberId, organizationId, subscription))
                .ReturnsAsync((Member)null);

            // Act
            var result = await _controller.GetById(memberId, organizationId, subscription);

            // Assert
            Assert.That(result.Result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result.Result as NotFoundObjectResult;
            Assert.That(notFoundResult.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task GetById_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var memberId = Guid.NewGuid();
            var organizationId = Guid.NewGuid();
            bool subscription = false;

            _mockQueryHandler
                .Setup(q => q.GetMemberById(memberId, organizationId, subscription))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetById(memberId, organizationId, subscription);

            // Assert
            Assert.That(result.Result, Is.InstanceOf<StatusCodeResult>());
            var statusCodeResult = result.Result as StatusCodeResult;
            Assert.That(statusCodeResult.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task Registration_WhenValidData_ReturnsOkWithMessage()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            bool subscription = false;
            var members = new List<MemberDto>
            {
                new MemberDto
                {
                    Id = Guid.NewGuid(),
                    FirstName = "John",
                    LastName = "Doe",
                    Email = "<EMAIL>",
                    OrganizationID = organizationId,
                    Subscription = subscription
                }
            };

            _mockCommandHandler
                .Setup(c => c.RegisterMembersAsync(It.IsAny<List<MemberDto>>(), organizationId, subscription))
                .ReturnsAsync(new List<string>());

            // Act
            var result = await _controller.Registration(members);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult.StatusCode, Is.EqualTo(200));
        }

        [Test]
        public async Task Registration_WhenNullOrEmptyList_ReturnsBadRequest()
        {
            // Act
            var result = await _controller.Registration(null);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult.StatusCode, Is.EqualTo(400));

            // Arrange - Empty List
            var emptyList = new List<MemberDto>();

            // Act
            result = await _controller.Registration(emptyList);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult.StatusCode, Is.EqualTo(400));
        }

        [Test]
        public async Task Registration_WhenErrorsOccur_ReturnsOkWithErrors()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            bool subscription = false;
            var members = new List<MemberDto>
            {
                new MemberDto
                {
                    Id = Guid.NewGuid(),
                    FirstName = "John",
                    LastName = "Doe",
                    Email = "<EMAIL>",
                    OrganizationID = organizationId,
                    Subscription = subscription
                }
            };

            var errors = new List<string> { "Error 1", "Error 2" };

            _mockCommandHandler
                .Setup(c => c.RegisterMembersAsync(It.IsAny<List<MemberDto>>(), organizationId, subscription))
                .ReturnsAsync(errors);

            // Act
            var result = await _controller.Registration(members);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult.StatusCode, Is.EqualTo(200));
            // The controller returns a localized string object when there are duplicate emails
            // Convert to string to compare the actual localized value
            Assert.That(okResult.Value.ToString(), Is.EqualTo("EmailAlreadyExists"));
        }

        [Test]
        public async Task Registration_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            bool subscription = false;
            var members = new List<MemberDto>
            {
                new MemberDto
                {
                    Id = Guid.NewGuid(),
                    FirstName = "John",
                    LastName = "Doe",
                    Email = "<EMAIL>",
                    OrganizationID = organizationId,
                    Subscription = subscription
                }
            };

            _mockCommandHandler
                .Setup(c => c.RegisterMembersAsync(It.IsAny<List<MemberDto>>(), organizationId, subscription))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.Registration(members);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult.StatusCode, Is.EqualTo(500));
        }

        // CheckAccess tests removed as the method doesn't exist in the controller
    }
}
