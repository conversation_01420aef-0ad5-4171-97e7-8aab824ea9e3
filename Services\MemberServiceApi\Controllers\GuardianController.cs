﻿using Contracts;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MemberServiceBusinessLayer;

namespace MemberServiceApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class GuardianController : ControllerBase
    {
        private readonly IGuardianQueryHandler<Guardian> _GuardianQueryHandler;
        private readonly IGuardianCommandHandler<Guardian> _GuardianCommandHandler;
        private readonly ILogger<GuardianController> _logger;
        private readonly IStringLocalizer<GuardianController> _localizer;

        public GuardianController(
            IGuardianQueryHandler<Guardian> GuardianQueryHandler,
            IGuardianCommandHandler<Guardian> GuardianCommandHandler,
            ILogger<GuardianController> logger,
            IStringLocalizer<GuardianController> localizer)
        {
            _GuardianQueryHandler = GuardianQueryHandler ?? throw new ArgumentNullException(nameof(GuardianQueryHandler));
            _GuardianCommandHandler = GuardianCommandHandler ?? throw new ArgumentNullException(nameof(GuardianCommandHandler));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _localizer = localizer ?? throw new ArgumentNullException(nameof(localizer));
        }

        [HttpGet("{id:Guid}/{OrgID:Guid}/{Subscription}")]

        public async Task<IActionResult> GetGuardianById(Guid id, Guid OrgID, bool Subscription)
        {
            IActionResult response;
            _logger.LogInformation(_localizer["FetchingAddressWithID"], id);
            var Guardian = await _GuardianQueryHandler.GetGuardianByIdAsync(id, OrgID, Subscription);
            if (Guardian == null)
            {
                _logger.LogWarning(_localizer["AddressNotFound"], id);
                response = NotFound(_localizer["AddressNotFoundMessage"]);
            }
            else
            {
                _logger.LogInformation(_localizer["AddressFetchedSuccessfully"], id);
                response = Ok(Guardian);
            }
            return response;
        }

        [HttpPost]
        public async Task<IActionResult> AddGuardian([FromBody] Guardian Guardian)
        {
            IActionResult response;
            if (Guardian == null)
            {
                _logger.LogWarning(_localizer["InvalidAddressData"]);
                response = BadRequest(_localizer["AddressInvalidMessage"]);
            }
            else
            {
                try
                {
                    _logger.LogInformation(_localizer["AddingNewAddress"]);
                    await _GuardianCommandHandler.AddGuardianAsync(new List<Guardian> { Guardian }, Guardian.OrganizationID,Guardian.Subscription);
                    _logger.LogInformation(_localizer["AddressAddedSuccessfully"], Guardian.GuardianId);
                    //response = CreatedAtAction(nameof(GetGuardianById), new { id = Guardian.GuardianId }, Guardian);
                    response = CreatedAtAction(nameof(GetGuardianById), new
                    {
                        id = Guardian.GuardianId,
                        OrgID = Guardian.OrganizationID,
                        Guardian.Subscription
                    }, new
                    {
                        Guardian.GuardianId,
                        Guardian.OrganizationID,
                        Guardian.Subscription
                    });

                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["ErrorAddingAddress"]);
                    response = StatusCode(500, _localizer["InternalServerErrorMessage"]);
                }
            }
            return response;
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateGuardian(Guid id, [FromBody] Guardian Guardian)
        {
            IActionResult response;
            if (Guardian == null || id != Guardian.GuardianId)
            {
                _logger.LogWarning(_localizer["InvalidDataForUpdate"], id);
                response = BadRequest(_localizer["AddressInvalidMessage"]);
            }
            else
            {
                try
                {
                    _logger.LogInformation(_localizer["UpdatingAddressWithID"], id);
                    await _GuardianCommandHandler.UpdateGuardianAsync(Guardian, Guardian.OrganizationID, Guardian.Subscription);
                    _logger.LogInformation(_localizer["AddressUpdatedSuccessfully"], id);
                    response = NoContent();
                }
                catch (KeyNotFoundException)
                {
                    _logger.LogWarning(_localizer["AddressNotFoundForUpdate"], id);
                    response = NotFound(_localizer["AddressNotFoundMessage"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["ErrorUpdatingAddress"]);
                    response = StatusCode(500, _localizer["InternalServerErrorMessage"]);
                }
            }
            return response;
        }

        [HttpDelete("{id:guid}/{OrgID:Guid}/{Subscription}")]
        public async Task<IActionResult> DeleteGuardian(Guid id, Guid OrgID, bool Subscription)
        {
            IActionResult response;
            try
            {
                _logger.LogInformation(_localizer["DeletingAddressWithID"], id);
                await _GuardianCommandHandler.DeleteGuardianAsync(id, OrgID, Subscription);
                _logger.LogInformation(_localizer["AddressDeletedSuccessfully"], id);
                response = NoContent();
            }
            catch (KeyNotFoundException)
            {
                _logger.LogWarning(_localizer["AddressNotFoundForDeletion"], id);
                response = NotFound(_localizer["AddressNotFoundMessage"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorDeletingAddress"]);
                response = StatusCode(500, _localizer["InternalServerErrorMessage"]);
            }
            return response;
        }

        [HttpGet]
        [Route("{orgId:guid}/{Subscription}")]
        public async Task<IActionResult> GetAllGuardian(Guid orgId, bool Subscription)
        {
            IActionResult response;
            _logger.LogInformation(_localizer["FetchingAllAddresses"]);
            try
            {
                var Guardian = await _GuardianQueryHandler.GetAllGuardianAsync(orgId, Subscription);

                if (Guardian == null || Guardian.Count == 0)
                {
                    _logger.LogWarning(_localizer["NoAddressesFound"]);
                    response = NotFound(_localizer["AddressesNotFoundMessage"]);
                }
                else
                {
                    _logger.LogInformation(_localizer["AllAddressesFetchedSuccessfully"]);
                    response = Ok(Guardian);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingAddresses"]);
                response = StatusCode(500, _localizer["InternalServerErrorMessage"]);
            }
            return response;
        }
    }
}
