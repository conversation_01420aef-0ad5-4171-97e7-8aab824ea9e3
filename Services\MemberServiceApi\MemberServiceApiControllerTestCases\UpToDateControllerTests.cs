using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using MemberServiceApi.Controllers;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using MemberServiceBusinessLayer;

namespace MemberServiceApiControllerTestCases
{
    [TestFixture]
    public class UpToDateControllerTests
    {
        private Mock<IUpToDateQueryHandler<UpToDate>> _mockQueryHandler;
        private Mock<IUpToDateCommandHandler<UpToDate>> _mockCommandHandler;
        private Mock<ILogger<UpToDateController>> _mockLogger;
        private Mock<IStringLocalizer<UpToDateController>> _mockLocalizer;
        private UpToDateController _controller;

        [SetUp]
        public void Setup()
        {
            _mockQueryHandler = new Mock<IUpToDateQueryHandler<UpToDate>>();
            _mockCommandHandler = new Mock<IUpToDateCommandHandler<UpToDate>>();
            _mockLogger = new Mock<ILogger<UpToDateController>>();
            _mockLocalizer = new Mock<IStringLocalizer<UpToDateController>>();

            // Setup localizer to return the key when requested
            _mockLocalizer.Setup(l => l[It.IsAny<string>()]).Returns((string key) => new LocalizedString(key, key));

            _controller = new UpToDateController(
                _mockQueryHandler.Object,
                _mockCommandHandler.Object,
                _mockLogger.Object,
                _mockLocalizer.Object
            );
        }

        [Test]
        public async Task GetAllUpToDates_WhenUpToDatesExist_ReturnsOkWithUpToDates()
        {
            // Arrange
            var mockUpToDates = new List<UpToDate>
            {
                new UpToDate { Id = Guid.NewGuid(), ProviderName = "UpToDate 1" },
                new UpToDate { Id = Guid.NewGuid(), ProviderName = "UpToDate 2" }
            };

            _mockQueryHandler
                .Setup(q => q.GetAllUpToDatesAsync())
                .ReturnsAsync(mockUpToDates);

            // Act
            var result = await _controller.GetAllUpToDates();

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult.StatusCode, Is.EqualTo(200));
            Assert.That(okResult.Value, Is.EqualTo(mockUpToDates));
        }

        [Test]
        public async Task GetAllUpToDates_WhenNoUpToDatesExist_ReturnsNotFound()
        {
            // Arrange
            _mockQueryHandler
                .Setup(q => q.GetAllUpToDatesAsync())
                .ReturnsAsync(new List<UpToDate>());

            // Act
            var result = await _controller.GetAllUpToDates();

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task GetAllUpToDates_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            _mockQueryHandler
                .Setup(q => q.GetAllUpToDatesAsync())
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetAllUpToDates();

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task GetUpToDateById_WhenUpToDateExists_ReturnsOkWithUpToDate()
        {
            // Arrange
            var upToDateId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var mockUpToDate = new UpToDate
            {
                Id = upToDateId,
                ProviderName = "UpToDate 1",
                OrganizationId = orgId
            };

            _mockQueryHandler
                .Setup(q => q.GetUpToDateByIdAsync(upToDateId))
                .ReturnsAsync(mockUpToDate);

            // Act
            var result = await _controller.GetUpToDateById(upToDateId);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult.StatusCode, Is.EqualTo(200));
            Assert.That(okResult.Value, Is.EqualTo(mockUpToDate));
        }

        [Test]
        public async Task GetUpToDateById_WhenUpToDateDoesNotExist_ReturnsNotFound()
        {
            // Arrange
            var upToDateId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockQueryHandler
                .Setup(q => q.GetUpToDateByIdAsync(upToDateId))
                .ReturnsAsync((UpToDate)null);

            // Act
            var result = await _controller.GetUpToDateById(upToDateId);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task GetUpToDateById_WhenExceptionOccurs_ThrowsException()
        {
            // Arrange
            var upToDateId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockQueryHandler
                .Setup(q => q.GetUpToDateByIdAsync(upToDateId))
                .ThrowsAsync(new Exception("Test exception"));

            // Act & Assert
            try
            {
                await _controller.GetUpToDateById(upToDateId);
                Assert.Fail("Expected exception was not thrown");
            }
            catch (Exception ex)
            {
                Assert.That(ex.Message, Is.EqualTo("Test exception"));
            }
        }

        [Test]
        public async Task AddUpToDate_WhenValidUpToDate_ReturnsCreatedAtAction()
        {
            // Arrange
            var upToDateId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var upToDate = new UpToDate
            {
                Id = upToDateId,
                ProviderName = "New UpToDate",
                OrganizationId = orgId
            };

            _mockCommandHandler
                .Setup(c => c.AddUpToDateAsync(It.IsAny<List<UpToDate>>()))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.AddUpToDate(upToDate);

            // Assert
            Assert.That(result, Is.InstanceOf<CreatedAtActionResult>());
            var createdResult = result as CreatedAtActionResult;
            Assert.That(createdResult.StatusCode, Is.EqualTo(201));
            Assert.That(createdResult.ActionName, Is.EqualTo(nameof(UpToDateController.GetUpToDateById)));
            Assert.That(createdResult.RouteValues["id"], Is.EqualTo(upToDateId));
            Assert.That(createdResult.Value, Is.EqualTo(upToDate));
        }

        [Test]
        public async Task AddUpToDate_WhenNullUpToDate_ReturnsBadRequest()
        {
            // Act
            var result = await _controller.AddUpToDate(null);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult.StatusCode, Is.EqualTo(400));
        }

        [Test]
        public async Task AddUpToDate_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var upToDate = new UpToDate
            {
                Id = Guid.NewGuid(),
                ProviderName = "New UpToDate"
            };

            _mockCommandHandler
                .Setup(c => c.AddUpToDateAsync(It.IsAny<List<UpToDate>>()))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.AddUpToDate(upToDate);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task DeleteUpToDate_WhenUpToDateExists_ReturnsNoContent()
        {
            // Arrange
            var upToDateId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockCommandHandler
                .Setup(c => c.DeleteUpToDateAsync(upToDateId))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.DeleteUpToDate(upToDateId);

            // Assert
            Assert.That(result, Is.InstanceOf<NoContentResult>());
            var noContentResult = result as NoContentResult;
            Assert.That(noContentResult.StatusCode, Is.EqualTo(204));
        }

        [Test]
        public async Task DeleteUpToDate_WhenUpToDateDoesNotExist_ReturnsNotFound()
        {
            // Arrange
            var upToDateId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockCommandHandler
                .Setup(c => c.DeleteUpToDateAsync(upToDateId))
                .ThrowsAsync(new KeyNotFoundException());

            // Act
            var result = await _controller.DeleteUpToDate(upToDateId);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task DeleteUpToDate_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var upToDateId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockCommandHandler
                .Setup(c => c.DeleteUpToDateAsync(upToDateId))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.DeleteUpToDate(upToDateId);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult.StatusCode, Is.EqualTo(500));
        }
    }
}
