﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;

namespace Contracts
{
    public class Member : IContract
    {
        public Guid Id { get; set; }
        public string? UserName { get; set; }
        public string? Password { get; set; }
        public string? Email { get; set; }
        public bool? IsActive { get; set; }
        public bool Subscription { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? PhoneNumber { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public string? Country { get; set; }

        [JsonPropertyName("PreferredName")]
        public string? PreferredName { get; set; }

        [JsonPropertyName("ExternalID")]
        public string? ExternalID { get; set; }

        [JsonPropertyName("MaritalStatus")]
        public string? MaritalStatus { get; set; }

        [JsonPropertyName("SexualOrientation")]
        public string? SexualOrientation { get; set; }

        [JsonPropertyName("PreviousNames")]
        public string? PreviousNames { get; set; }

        public ICollection<ProductUserAccess>? ProductUserAccess { get; set; }


        [JsonPropertyName("Language")]
        public string? Language { get; set; }

        [JsonPropertyName("Ethnicity")]
        public string? Ethnicity { get; set; }

        [JsonPropertyName("Race")]
        public string? Race { get; set; }

        [JsonPropertyName("Nationality")]
        public string? Nationality { get; set; }

        [JsonPropertyName("FamilySize")]
        public int? FamilySize { get; set; }

        [JsonPropertyName("FinancialReviewDate")]
        public DateTime? FinancialReviewDate { get; set; }

        [JsonPropertyName("MonthlyIncome")]
        public decimal? MonthlyIncome { get; set; }

        [JsonPropertyName("ReferralSource")]
        public string? ReferralSource { get; set; }

        [JsonPropertyName("Religion")]
        public string? Religion { get; set; }
        public string? OrganizationName { get; set; }
       
        public DateTime? DateDeceased { get; set; }
        public string? ReasonDeceased { get; set; }


        public string? MiddleName { get; set; }
        public string? Suffix { get; set; }
        public string? FederalTaxId { get; set; }
        public string? DEANumber { get; set; }
        public string? UPIN { get; set; }
        public string? NPI { get; set; }
        public string? ProviderType { get; set; }
        public string? MainMenuRole { get; set; }
        public string? PatientMenuRole { get; set; }
        public string? Supervisor { get; set; }
        public string? JobDescription { get; set; }
        public string? Taxonomy { get; set; }
        public string? NewCropERxRole { get; set; }
        public string? AccessControl { get; set; }
        public string? AdditionalInfo { get; set; }
        public string? DefaultBillingFacility { get; set; }
        public string? PCPName { get; set; }
        public Guid? PCPId { get; set; }
        public Guid? RoleID { get; set; }
        public string? RoleName { get; set; }
        public Guid? OrganizationID { get; set; }
        public Guid? AddressId { get; set; }

        [ForeignKey("AddressId")]
        public virtual Address? Address { get; set; }
        public Guid? InsuranceId { get; set; }
        public Guid? GuardianId { get; set; }
        public Guid? LicenseId { get; set; }
        public Guid? EmployerId { get; set; }
        public string? ProfileImageUrl { get; set; }
        public virtual Insurance? Insurance { get; set; }
        public string? SSN { get; set; }
    }
}
