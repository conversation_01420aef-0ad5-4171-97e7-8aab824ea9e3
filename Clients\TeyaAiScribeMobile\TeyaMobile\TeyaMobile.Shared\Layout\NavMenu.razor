﻿@using Microsoft.AspNetCore.Components.Routing
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Authorization
@inject TeyaMobileViewModel.ViewModel.IAuthenticationService AuthService
@inject NavigationManager Navigation

<nav class="nav-container">
    <div class="nav-content">
        @if (IsAuthenticated && showBackButton)
        {
            <button class="back-button" @onclick="HandleBackClick" title="back">
                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M19 12H5M12 19L5 12L12 5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </button>
        } 
        <!-- Logo positioned to the left -->
        <div class="nav-logo">
            <div class="logo-icon">
                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                    <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                    <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                </svg>
            </div>
            <span class="logo-text">Teya Health</span>
        </div>

        @if (IsAuthenticated)
        {  
            <div class="mobile-controls">
                <!-- Hamburger menu button for mobile -->
                <button class="nav-toggle" @onclick="ToggleMenu" aria-label="Toggle navigation">
                    <span class="bar @(menuOpen ? "bar-active-1" : "")"></span>
                    <span class="bar @(menuOpen ? "bar-active-2" : "")"></span>
                    <span class="bar @(menuOpen ? "bar-active-3" : "")"></span>
                </button>

                <!-- Mobile profile icon -->
                <div class="mobile-profile-icon" @onclick="ToggleMobileProfile">
                    <div class="mobile-avatar">
                        @if (!string.IsNullOrEmpty(UserDisplayName))
                        {
                            <span class="avatar-text">@UserDisplayName.Substring(0, 1).ToUpper()</span>
                        }
                        else
                        {
                            <span class="avatar-text">U</span>
                        }
                        <div class="online-indicator"></div>
                    </div>
                </div>
            </div>

            <!-- Desktop navigation - positioned to the right -->
            <div class="nav-links @(menuOpen ? "nav-links-open" : "")">
                <div class="nav-items">
                    @foreach (var item in AuthenticatedNavItems)
                    {
                        <NavLink href="@item.Href" Match="NavLinkMatch.All" class="nav-link" @onclick="() => menuOpen = false">
                            <span class="nav-icon">@((MarkupString)item.IconSvg)</span>
                            <span class="nav-text">@item.Text</span>
                        </NavLink>
                    }
                </div>

                <!-- Desktop user dropdown -->
                <div class="desktop-user-dropdown" @onclick="ToggleUserMenu" @onblur="CloseUserMenu" tabindex="0">
                    <div class="user-profile">
                        <div class="user-avatar">
                            @if (!string.IsNullOrEmpty(UserDisplayName))
                            {
                                <span class="avatar-text">@UserDisplayName.Substring(0, 1).ToUpper()</span>
                            }
                            else
                            {
                                <span class="avatar-text">U</span>
                            }
                            <div class="online-indicator"></div>
                        </div>
                        <div class="user-info">
                            <span class="user-name">@UserDisplayName</span>
                            <span class="user-status">Online</span>
                        </div>
                        <div class="dropdown-arrow">
                            <svg class="@(userMenuOpen ? "arrow-up" : "")" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                    </div>

                    <!-- Desktop dropdown menu -->
                    <div class="user-menu @(userMenuOpen ? "user-menu-open" : "")">
                        <div class="user-menu-header">
                            <div class="user-menu-avatar">
                                @if (!string.IsNullOrEmpty(UserDisplayName))
                                {
                                    <span>@UserDisplayName.Substring(0, 1).ToUpper()</span>
                                }
                                else
                                {
                                    <span>U</span>
                                }
                            </div>
                            <div class="user-menu-info">
                                <div class="user-menu-name">@UserDisplayName</div>
                                <div class="user-menu-email">@UserEmail</div>
                            </div>
                        </div>
                        <div class="user-menu-divider"></div>
                        <div class="user-menu-items">
                            <button class="user-menu-item" @onclick="ViewProfile">
                                <svg class="menu-item-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                                <span>View Profile</span>
                            </button>
                            <button class="user-menu-item" @onclick="AccountSettings">
                                <svg class="menu-item-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                                    <path d="M19.4 15A1.65 1.65 0 0 0 20.25 13.38L18.73 11.62A1.65 1.65 0 0 0 16.25 11.62L14.73 13.38A1.65 1.65 0 0 0 15.6 15H19.4Z" stroke="currentColor" stroke-width="2"/>
                                </svg>
                                <span>Account Settings</span>
                            </button>
                        </div>
                        <div class="user-menu-divider"></div>
                        <button class="user-menu-item logout-item" @onclick="HandleLogout">
                            <svg class="menu-item-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M9 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <polyline points="16,17 21,12 16,7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <line x1="21" y1="12" x2="9" y2="12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <span>Sign Out</span>
                        </button>
                    </div>
                </div>
            </div>
        }
        else
        {
            <!-- Sign in button - attractive design -->
            <div class="auth-section">
                <NavLink href="/login" class="signin-btn" @onclick="() => menuOpen = false">
                    <svg class="signin-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M15 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <polyline points="10,17 15,12 10,7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <line x1="15" y1="12" x2="3" y2="12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    <span>Sign In</span>
                </NavLink>
            </div>
        }
    </div>
</nav>

<!-- Mobile Profile Modal -->
@if (IsAuthenticated && mobileProfileOpen)
{
    <div class="mobile-profile-modal" @onclick="CloseMobileProfile">
        <div class="mobile-profile-content" @onclick:stopPropagation="true">
            <div class="mobile-profile-header">
                <div class="mobile-profile-avatar">
                    @if (!string.IsNullOrEmpty(UserDisplayName))
                    {
                        <span>@UserDisplayName.Substring(0, 1).ToUpper()</span>
                    }
                    else
                    {
                        <span>U</span>
                    }
                </div>
                <div class="mobile-profile-info">
                    <div class="mobile-profile-name">@UserDisplayName</div>
                    <div class="mobile-profile-email">@UserEmail</div>
                    <div class="mobile-profile-status">
                        <div class="status-dot"></div>
                        <span>Online</span>
                    </div>
                </div>
                <button class="mobile-profile-close" @onclick="CloseMobileProfile">
                    <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <line x1="18" y1="6" x2="6" y2="18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <line x1="6" y1="6" x2="18" y2="18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </button>
            </div>
            
            <div class="mobile-profile-actions">
                <button class="mobile-profile-action" @onclick="ViewProfile">
                    <svg class="action-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    <span>View Profile</span>
                </button>
                
                <button class="mobile-profile-action" @onclick="AccountSettings">
                    <svg class="action-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                        <path d="M19.4 15A1.65 1.65 0 0 0 20.25 13.38L18.73 11.62A1.65 1.65 0 0 0 16.25 11.62L14.73 13.38A1.65 1.65 0 0 0 15.6 15H19.4Z" stroke="currentColor" stroke-width="2"/>
                    </svg>
                    <span>Account Settings</span>
                </button>
                
                <div class="mobile-profile-divider"></div>
                
                <button class="mobile-profile-action logout-action" @onclick="HandleLogout">
                    <svg class="action-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <polyline points="16,17 21,12 16,7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <line x1="21" y1="12" x2="9" y2="12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    <span>Sign Out</span>
                </button>
            </div>
        </div>
    </div>
}

<style>
    .nav-container {
        position: fixed;
        width: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        color: white;
        z-index: 1000;
        backdrop-filter: blur(10px);
    }

    .nav-content {
        max-width: 1400px;
        margin: 0 auto;
        padding: 0 1.5rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 70px;
    }

    /* Logo - positioned closer to left */
    .nav-logo {
        display: flex;
        align-items: center;
        gap: 12px;
        font-size: 1.4rem;
        font-weight: 700;
        user-select: none;
        margin-right: auto;
    }

    .logo-icon {
        width: 36px;
        height: 36px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(10px);
    }

    .logo-icon svg {
        width: 20px;
        height: 20px;
        color: white;
    }

    .logo-text {
        color: white;
        font-weight: 700;
        letter-spacing: -0.5px;
    }

    /* Mobile controls container */
    .mobile-controls {
        display: none;
        align-items: center;
        gap: 12px;
    }

    /* Mobile profile icon */
    .mobile-profile-icon {
        cursor: pointer;
        padding: 4px;
        border-radius: 50%;
        transition: background-color 0.3s ease;
    }

    .mobile-profile-icon:hover {
        background: rgba(255, 255, 255, 0.1);
    }

    .mobile-avatar {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        background: linear-gradient(135deg, #ff6b6b, #4ecdc4);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        font-size: 14px;
        position: relative;
        box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    }

    .online-indicator {
        position: absolute;
        bottom: 2px;
        right: 2px;
        width: 10px;
        height: 10px;
        background: #10b981;
        border: 2px solid white;
        border-radius: 50%;
        animation: pulse 2s infinite;
    }

    @@keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.7; }
    }

    /* Navigation links - positioned to the right */
    .nav-links {
        display: flex;
        align-items: center;
        gap: 2rem;
        margin-left: auto;
    }

    .nav-items {
        display: flex;
        gap: 1.5rem;
        align-items: center;
    }

    .nav-link {
        color: rgba(255, 255, 255, 0.9);
        text-decoration: none;
        font-weight: 500;
        font-size: 0.95rem;
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 16px;
        border-radius: 8px;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .nav-link::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.1);
        transform: scaleX(0);
        transform-origin: left;
        transition: transform 0.3s ease;
        z-index: -1;
    }

    .nav-link:hover::before,
    .nav-link.active::before {
        transform: scaleX(1);
    }

    .nav-link:hover,
    .nav-link.active {
        color: white;
        background: rgba(255, 255, 255, 0.1);
    }

    .nav-icon {
        width: 18px;
        height: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
        fill: currentColor;
    }

    .nav-icon svg {
        width: 100%;
        height: 100%;
    }

    /* Desktop user dropdown */
    .desktop-user-dropdown {
        position: relative;
        cursor: pointer;
    }

    .user-profile {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 8px 12px;
        border-radius: 12px;
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .user-profile:hover {
        background: rgba(255, 255, 255, 0.15);
        transform: translateY(-1px);
    }

    .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #ff6b6b, #4ecdc4);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        font-size: 16px;
        position: relative;
        box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    }

    .user-info {
        display: flex;
        flex-direction: column;
        gap: 2px;
    }

    .user-name {
        font-weight: 600;
        font-size: 14px;
        color: white;
        line-height: 1;
    }

    .user-status {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.7);
        line-height: 1;
    }

    .dropdown-arrow {
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: transform 0.3s ease;
    }

    .dropdown-arrow svg {
        width: 16px;
        height: 16px;
        color: rgba(255, 255, 255, 0.8);
        transition: transform 0.3s ease;
    }

    .dropdown-arrow .arrow-up {
        transform: rotate(180deg);
    }

    /* User menu dropdown */
    .user-menu {
        position: absolute;
        top: calc(100% + 8px);
        right: 0;
        background: white;
        border-radius: 12px;
        box-shadow: 0 10px 40px rgba(0,0,0,0.15);
        min-width: 280px;
        opacity: 0;
        visibility: hidden;
        transform: translateY(-10px);
        transition: all 0.3s ease;
        z-index: 1000;
        border: 1px solid rgba(0,0,0,0.1);
    }

    .user-menu-open {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
    }

    .user-menu-header {
        padding: 20px;
        display: flex;
        align-items: center;
        gap: 12px;
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 12px 12px 0 0;
    }

    .user-menu-avatar {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea, #764ba2);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        font-size: 18px;
    }

    .user-menu-info {
        flex: 1;
    }

    .user-menu-name {
        font-weight: 600;
        color: #1a1a1a;
        font-size: 16px;
        margin-bottom: 2px;
    }

    .user-menu-email {
        color: #6b7280;
        font-size: 14px;
    }

    .user-menu-divider {
        height: 1px;
        background: #e5e7eb;
        margin: 0 12px;
    }

    .user-menu-items {
        padding: 8px;
    }

    .user-menu-item {
        width: 100%;
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px 16px;
        border: none;
        background: none;
        color: #374151;
        font-size: 14px;
        font-weight: 500;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.2s ease;
        text-align: left;
    }

    .user-menu-item:hover {
        background: #f3f4f6;
        color: #1f2937;
    }

    .user-menu-item.logout-item {
        color: #dc2626;
    }

    .user-menu-item.logout-item:hover {
        background: #fef2f2;
        color: #b91c1c;
    }

    .menu-item-icon {
        width: 18px;
        height: 18px;
        color: currentColor;
    }

    /* Sign in button */
    .auth-section {
        margin-left: auto;
    }

    .signin-btn {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 10px 20px;
        background: rgba(255, 255, 255, 0.15);
        color: white;
        text-decoration: none;
        border-radius: 25px;
        font-weight: 600;
        font-size: 14px;
        transition: all 0.3s ease;
        border: 1px solid rgba(255, 255, 255, 0.3);
        backdrop-filter: blur(10px);
    }

    .signin-btn:hover {
        background: rgba(255, 255, 255, 0.25);
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        color: white;
    }

    .signin-icon {
        width: 18px;
        height: 18px;
        color: currentColor;
    }

    /* Hamburger menu */
    .nav-toggle {
        display: none;
        flex-direction: column;
        justify-content: space-between;
        width: 28px;
        height: 20px;
        background: none;
        border: none;
        cursor: pointer;
        padding: 0;
    }

    .bar {
        height: 3px;
        background-color: white;
        border-radius: 2px;
        transition: all 0.3s ease-in-out;
    }

    .bar-active-1 {
        transform: rotate(45deg) translate(6px, 6px);
    }

    .bar-active-2 {
        opacity: 0;
    }

    .bar-active-3 {
        transform: rotate(-45deg) translate(6px, -6px);
    }

    /* Back button styles */
.back-button {
    width: 32px;
    height: 32px;
    background: rgba(0, 0, 0, 0.8);
    border: none;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-right: 8px;
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    flex-shrink: 0;
}

.back-button:hover {
    background: rgba(0, 0, 0, 0.9);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
}

.back-button svg {
    width: 16px;
    height: 16px;
    color: white;
}

/* Update nav-logo to handle back button properly */
.nav-logo {
    display: flex;
    align-items: center;
    gap: 8px; /* Reduced gap for better spacing */
    font-size: 1.4rem;
    font-weight: 700;
    user-select: none;
    margin-right: auto;
}

@@media (max-width: 768px) {
    .back-button {
        width: 28px;
        height: 28px;
        margin-right: 6px;
    }
    
    .back-button svg {
        width: 14px;
        height: 14px;
    }
    
    .nav-logo {
        gap: 6px;
    }
}


    /* Mobile Profile Modal */
    .mobile-profile-modal {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(4px);
        z-index: 2000;
        display: flex;
        align-items: flex-start;
        justify-content: center;
        padding: 80px 20px 20px;
        animation: fadeIn 0.3s ease-out;
    }

    @@keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    .mobile-profile-content {
        background: white;
        border-radius: 16px;
        width: 100%;
        max-width: 400px;
        box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        animation: slideDown 0.3s ease-out;
        overflow: hidden;
    }

    @@keyframes slideDown {
        from { 
            opacity: 0;
            transform: translateY(-20px);
        }
        to { 
            opacity: 1;
            transform: translateY(0);
        }
    }

    .mobile-profile-header {
        padding: 24px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        display: flex;
        align-items: center;
        gap: 16px;
        position: relative;
    }

    .mobile-profile-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 700;
        font-size: 24px;
        backdrop-filter: blur(10px);
        border: 3px solid rgba(255, 255, 255, 0.3);
    }

    .mobile-profile-info {
        flex: 1;
    }

    .mobile-profile-name {
        font-size: 20px;
        font-weight: 700;
        margin-bottom: 4px;
    }

    .mobile-profile-email {
        font-size: 14px;
        opacity: 0.9;
        margin-bottom: 8px;
    }

    .mobile-profile-status {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 12px;
        opacity: 0.8;
    }

    .status-dot {
        width: 8px;
        height: 8px;
        background: #10b981;
        border-radius: 50%;
        animation: pulse 2s infinite;
    }

    .mobile-profile-close {
        position: absolute;
        top: 16px;
        right: 16px;
        background: rgba(255, 255, 255, 0.2);
        border: none;
        color: white;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: background-color 0.2s;
    }

    .mobile-profile-close:hover {
        background: rgba(255, 255, 255, 0.3);
    }

    .mobile-profile-close svg {
        width: 16px;
        height: 16px;
    }

    .mobile-profile-actions {
        padding: 16px;
    }

    .mobile-profile-action {
        width: 100%;
        display: flex;
        align-items: center;
        gap: 16px;
        padding: 16px;
        border: none;
        background: none;
        color: #374151;
        font-size: 16px;
        font-weight: 500;
        border-radius: 12px;
        cursor: pointer;
        transition: all 0.2s ease;
        text-align: left;
        margin-bottom: 8px;
    }

    .mobile-profile-action:hover {
        background: #f3f4f6;
        color: #1f2937;
    }

    .mobile-profile-action.logout-action {
        color: #dc2626;
        margin-top: 8px;
    }

    .mobile-profile-action.logout-action:hover {
        background: #fef2f2;
        color: #b91c1c;
    }

    .action-icon {
        width: 24px;
        height: 24px;
        color: currentColor;
        flex-shrink: 0;
    }

    .mobile-profile-divider {
        height: 1px;
        background: #e5e7eb;
        margin: 16px 0;
    }

    /* Mobile responsive */
    @@media (max-width: 768px) {
        .nav-content {
            padding: 0 1rem;
            height: 60px;
        }

        .mobile-controls {
            display: flex;
        }

        .nav-toggle {
            display: flex;
        }

        .nav-links {
            position: absolute;
            top: 60px;
            left: 0;
            right: 0;
            background: rgba(102, 126, 234, 0.95);
            backdrop-filter: blur(20px);
            flex-direction: column;
            padding: 0;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
            gap: 0;
        }

        .nav-links-open {
            max-height: 400px;
            padding: 1rem 0;
        }

        .nav-items {
            flex-direction: column;
            gap: 0;
            width: 100%;
        }

        .nav-link {
            padding: 1rem 1.5rem;
            width: 100%;
            justify-content: flex-start;
            border-radius: 0;
            font-size: 16px;
        }

        .nav-link::before {
            display: none;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        /* Hide desktop user dropdown on mobile */
        .desktop-user-dropdown {
            display: none;
        }

        .logo-text {
            font-size: 1.2rem;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
        }

        .auth-section {
            margin-left: 0;
        }
    }

    @@media (max-width: 480px) {
        .nav-content {
            padding: 0 0.75rem;
        }

        .logo-text {
            display: none;
        }

        .mobile-profile-modal {
            padding: 70px 16px 16px;
        }
    }
</style>

@code {
    @inject IJSRuntime JSRuntime
    private bool menuOpen = false;
    private bool userMenuOpen = false;
    private bool mobileProfileOpen = false;
    private bool IsAuthenticated = false;
    private string UserDisplayName = "User";
    private string UserEmail = "";
    private bool showBackButton = false;
    private DateTime lastBackClick = DateTime.MinValue;


    protected override async Task OnInitializedAsync()
    {
        await CheckAuthenticationState();
        Navigation.LocationChanged += OnLocationChanged;
        await CheckBackButtonVisibility();
    }

    private async Task HandleBackClick()
    {
        try
        {
            if (DateTime.Now - lastBackClick < TimeSpan.FromSeconds(1))
                return;
            
            lastBackClick = DateTime.Now;
        
            var currentUrl = Navigation.ToBaseRelativePath(Navigation.Uri).ToLower();

            if (currentUrl.StartsWith("recorder") || currentUrl.StartsWith("audio-recorder"))
            {
                Navigation.NavigateTo("/appointments", forceLoad: false);
            }
            else if( currentUrl.StartsWith("appointments"))
            {
                Navigation.NavigateTo("/", forceLoad: false);
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("history.back");
            }
        
            // Update visibility after navigation
            await Task.Delay(100);
            await CheckBackButtonVisibility();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Back navigation error: {ex.Message}");
        }
    }

    private async void OnLocationChanged(object sender, LocationChangedEventArgs e)
    {
        // Update back button visibility when navigation occurs
        await CheckBackButtonVisibility();
        await InvokeAsync(StateHasChanged);
    }

    private async Task CheckBackButtonVisibility()
    {
        try
        {
            var currentUrl = Navigation.Uri;
            var relativePath = Navigation.ToBaseRelativePath(currentUrl).ToLower();
            
            showBackButton = IsAuthenticated && 
                            !string.IsNullOrEmpty(relativePath) && 
                            relativePath != "/" &&
                            relativePath != "dashboard" &&
                            !relativePath.StartsWith("login");
            
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error checking back button visibility: {ex.Message}");
        }
    }

    public void Dispose()
    {
        Navigation.LocationChanged -= OnLocationChanged;
    }


    private async Task CheckAuthenticationState()
    {
        try
        {
            IsAuthenticated = AuthService.IsAuthenticated;
            if (IsAuthenticated)
            {
                UserDisplayName = await AuthService.GetUserNameAsync();
                UserEmail = await AuthService.GetUserEmailAsync();
                
                if (string.IsNullOrEmpty(UserDisplayName))
                {
                    UserDisplayName = "User";
                }
                if (string.IsNullOrEmpty(UserEmail))
                {
                    UserEmail = "<EMAIL>";
                }
            }
            else
            {
                UserDisplayName = "User";
                UserEmail = "";
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Auth state check error: {ex.Message}");
            IsAuthenticated = false;
            UserDisplayName = "User";
            UserEmail = "";
        }
        StateHasChanged();
    }

    private void ToggleMenu()
    {
        menuOpen = !menuOpen;
        if (menuOpen)
        {
            userMenuOpen = false;
            mobileProfileOpen = false;
        }
    }

    private void ToggleUserMenu()
    {
        userMenuOpen = !userMenuOpen;
    }

    private void ToggleMobileProfile()
    {
        mobileProfileOpen = !mobileProfileOpen;
        if (mobileProfileOpen)
        {
            menuOpen = false;
        }
    }

    private void CloseMobileProfile()
    {
        mobileProfileOpen = false;
    }

    private void CloseUserMenu()
    {
        Task.Delay(150).ContinueWith(_ => 
        {
            userMenuOpen = false;
            InvokeAsync(StateHasChanged);
        });
    }

    private void ViewProfile()
    {
        userMenuOpen = false;
        menuOpen = false;
        mobileProfileOpen = false;
        Navigation.NavigateTo("/profile");
    }

    private void AccountSettings()
    {
        userMenuOpen = false;
        menuOpen = false;
        mobileProfileOpen = false;
        Navigation.NavigateTo("/settings");
    }

    private async Task HandleLogout()
    {
        try
        {
            menuOpen = false;
            userMenuOpen = false;
            mobileProfileOpen = false;
            
            // Redirect to home page after logout  see logic for / for mobile and /login for web
            await AuthService.LogoutAsync();
            Navigation.NavigateTo("/", forceLoad: true);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Logout error: {ex.Message}");
        }
    }

    // Navigation items for authenticated users
    private List<NavItem> AuthenticatedNavItems = new()
    {
        new NavItem("Dashboard", "/", HomeIcon),
        new NavItem("Appointments", "/appointments", CalendarIcon),
        new NavItem("AI Scribe", "/audio-recorder", MicrophoneIcon),
        new NavItem("SOAP Notes", "/soapnotes", DocumentIcon),
        new NavItem("Secure Area", "/secure", ShieldIcon)
    };

    class NavItem
    {
        public string Text { get; }
        public string Href { get; }
        public string IconSvg { get; }

        public NavItem(string text, string href, string iconSvg)
        {
            Text = text;
            Href = href;
            IconSvg = iconSvg;
        }
    }

    // SVG icons
    private const string HomeIcon = @"
        <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2'>
          <path d='M3 9.5L12 3l9 6.5V21a1 1 0 0 1-1 1h-5v-6H9v6H4a1 1 0 0 1-1-1V9.5z'/>
        </svg>";

    private const string MicrophoneIcon = @"
        <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2'>
          <path d='M12 14a3 3 0 0 0 3-3V6a3 3 0 0 0-6 0v5a3 3 0 0 0 3 3zm5-3a5 5 0 0 1-10 0H5a7 7 0 0 0 14 0zM11 18v3h2v-3h-2z'/>
        </svg>";

    private const string CalendarIcon = @"
        <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2'>
          <rect x='3' y='4' width='18' height='18' rx='2' ry='2'/>
          <line x1='16' y1='2' x2='16' y2='6'/>
          <line x1='8' y1='2' x2='8' y2='6'/>
          <line x1='3' y1='10' x2='21' y2='10'/>
        </svg>";

    private const string DocumentIcon = @"
        <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2'>
          <path d='M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 2 2h12c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z'/>
        </svg>";

    private const string ShieldIcon = @"
        <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2'>
          <path d='M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z'/>
        </svg>";
}
