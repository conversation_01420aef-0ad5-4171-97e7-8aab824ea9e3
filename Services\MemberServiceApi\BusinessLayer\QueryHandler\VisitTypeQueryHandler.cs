﻿using Contracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MemberServiceDataAccessLayer.Implementation;

namespace MemberServiceBusinessLayer.QueryHandler
{
    public class VisitTypeQueryHandler : IVisitTypeQueryHandler<VisitType>
    {
        private readonly IUnitOfWork _unitOfWork;
        public VisitTypeQueryHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        }

        /// <summary>
        /// Retrieves all visit types asynchronously.
        /// </summary>
        /// <returns>A list of all visit types.</returns>
        public async Task<IEnumerable<VisitType>> GetAllVisitTypesAsync()
        {
            var visitTypes = await _unitOfWork.VisitTypeRepository.GetAllAsync();
            return visitTypes.ToList();
        }

        /// <summary>
        /// Retrieves visit types by Organization ID asynchronously.
        /// </summary>
        /// <param name="orgId">The Organization ID.</param>
        /// <returns>Visit types associated with the specified Organization ID.</returns>
        public async Task<IEnumerable<VisitType>> GetByOrganizationIdAsync(Guid orgId, bool Subscription)
        {
            return await _unitOfWork.VisitTypeRepository.GetByOrganizationIdAsync(orgId);
        }
    }
}
