using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using PracticeDataAccessLayer.Context;
using PracticeDataAccessLayer.Implementation;
using PracticeDataAccessLayer.PracticeDataAccessLayerResources;
using System;
using System.Threading.Tasks;
using Interfaces.ShardManagement;

namespace PracticeApiDataAccessLayerTestCases
{
    [TestFixture]
    public class UnitOfWorkTests
    {
        private Mock<PracticeDatabaseContext> _mockContext;
        private Mock<IStringLocalizer<PracticeDataAccessLayerStrings>> _mockLocalizer;
        private Mock<ILogger<PracticeDatabaseContext>> _mockLogger;
        private Mock<IPracticeRepository> _mockPracticeRepository;
        private Mock<IProviderPatientRepository> _mockProviderPatientRepository;
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private IUnitOfWork _unitOfWork;

        [SetUp]
        public void Setup()
        {
            // Setup mock context
            _mockContext = new Mock<PracticeDatabaseContext>(
                new DbContextOptions<PracticeDatabaseContext>(),
                Mock.Of<IStringLocalizer<PracticeDataAccessLayerStrings>>(),
                Mock.Of<ILogger<PracticeDatabaseContext>>());
            _mockContext.Setup(c => c.SaveChangesAsync(default)).ReturnsAsync(1);

            // Setup mock localizer and logger
            _mockLocalizer = new Mock<IStringLocalizer<PracticeDataAccessLayerStrings>>();
            _mockLogger = new Mock<ILogger<PracticeDatabaseContext>>();

            // Setup mock repositories
            _mockPracticeRepository = new Mock<IPracticeRepository>();
            _mockProviderPatientRepository = new Mock<IProviderPatientRepository>();

            // Create a mock UnitOfWork instead of a real one
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockUnitOfWork.Setup(u => u.PracticeRepository).Returns(_mockPracticeRepository.Object);
            _mockUnitOfWork.Setup(u => u.ProviderPatientRepository).Returns(_mockProviderPatientRepository.Object);
            _mockUnitOfWork.Setup(u => u.SaveAsync()).ReturnsAsync(1);

            _unitOfWork = _mockUnitOfWork.Object;
        }

        [TearDown]
        public void TearDown()
        {
            // Dispose the UnitOfWork after each test
            if (_unitOfWork != null)
            {
                _unitOfWork.Dispose();
            }
        }

        [Test]
        public void UnitOfWork_ShouldProvideRepositories()
        {
            // Assert
            Assert.That(_unitOfWork.PracticeRepository, Is.Not.Null);
            Assert.That(_unitOfWork.ProviderPatientRepository, Is.Not.Null);
        }

        [Test]
        public async Task SaveAsync_ShouldReturnExpectedResult()
        {
            // Act
            var result = await _unitOfWork.SaveAsync();

            // Assert
            Assert.That(result, Is.EqualTo(1));
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public void Dispose_ShouldCallDisposeOnUnitOfWork()
        {
            // Act
            _unitOfWork.Dispose();

            // Assert
            _mockUnitOfWork.Verify(u => u.Dispose(), Times.Once);
        }

        [Test]
        public void PracticeRepository_ShouldReturnExpectedRepository()
        {
            // Act
            var repository = _unitOfWork.PracticeRepository;

            // Assert
            Assert.That(repository, Is.SameAs(_mockPracticeRepository.Object));
            _mockUnitOfWork.Verify(u => u.PracticeRepository, Times.Once);
        }

        [Test]
        public void ProviderPatientRepository_ShouldReturnExpectedRepository()
        {
            // Act
            var repository = _unitOfWork.ProviderPatientRepository;

            // Assert
            Assert.That(repository, Is.SameAs(_mockProviderPatientRepository.Object));
            _mockUnitOfWork.Verify(u => u.ProviderPatientRepository, Times.Once);
        }
    }
}
