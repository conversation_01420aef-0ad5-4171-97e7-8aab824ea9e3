using NUnit.Framework;
using Moq;
using MemberServiceBusinessLayer.QueryHandler;
using MemberServiceDataAccessLayer.Implementation;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;

namespace MemberServiceApiBusinessLayerTestCases.QueryHandlerTests
{
    [TestFixture]
    public class RoleslistQueryHandlerTests
    {
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<IRoleslistRepository> _mockRoleslistRepository;
        private RoleslistQueryHandler _roleslistQueryHandler;

        [SetUp]
        public void Setup()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockRoleslistRepository = new Mock<IRoleslistRepository>();

            _mockUnitOfWork.Setup(u => u.RoleslistRepository).Returns(_mockRoleslistRepository.Object);

            _roleslistQueryHandler = new RoleslistQueryHandler(_mockUnitOfWork.Object);
        }

        [Test]
        public async Task GetRoleslist_ShouldReturnAllRoles()
        {
            // Arrange
            var expectedRoles = new List<Rolesdata>
            {
                new Rolesdata
                {
                    ID = Guid.NewGuid(),
                    Rolename = "Admin"
                },
                new Rolesdata
                {
                    ID = Guid.NewGuid(),
                    Rolename = "User"
                }
            };

            _mockRoleslistRepository
                .Setup(r => r.GetAllAsync())
                .ReturnsAsync(expectedRoles);

            // Act
            var result = await _roleslistQueryHandler.GetRoleslist();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(2));
            Assert.That(result, Is.EquivalentTo(expectedRoles));
            _mockRoleslistRepository.Verify(r => r.GetAllAsync(), Times.Once);
        }

        [Test]
        public void Constructor_ShouldThrowArgumentNullException_WhenUnitOfWorkIsNull()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new RoleslistQueryHandler(null));
        }
    }
}
