using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Configuration;
using MemberServiceBusinessLayer.QueryHandler;
using MemberServiceDataAccessLayer.Implementation;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;

namespace MemberServiceApiBusinessLayerTestCases.QueryHandlerTests
{
    [TestFixture]
    public class ProductQueryHandlerTests
    {
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<IProductRepository> _mockProductRepository;
        private ProductQueryHandler _productQueryHandler;

        [SetUp]
        public void Setup()
        {
            _mockConfiguration = new Mock<IConfiguration>();
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockProductRepository = new Mock<IProductRepository>();

            _mockUnitOfWork.Setup(u => u.ProductRepository).Returns(_mockProductRepository.Object);

            _productQueryHandler = new ProductQueryHandler(_mockConfiguration.Object, _mockUnitOfWork.Object);
        }

        [Test]
        public async Task GetProduct_ShouldReturnAllProducts()
        {
            // Arrange
            var expectedProducts = new List<ProductDTO>
            {
                new ProductDTO { Id = Guid.NewGuid(), Name = "Product 1", Description = "Description 1" },
                new ProductDTO { Id = Guid.NewGuid(), Name = "Product 2", Description = "Description 2" }
            };

            _mockProductRepository.Setup(r => r.GetAllAsync()).ReturnsAsync(expectedProducts);

            // Act
            var result = await _productQueryHandler.GetProduct();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.EquivalentTo(expectedProducts));
            _mockProductRepository.Verify(r => r.GetAllAsync(), Times.Once);
        }

        [Test]
        public async Task GetProductById_ShouldReturnProduct_WhenProductExists()
        {
            // Arrange
            var productId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var expectedProduct = new ProductDTO
            {
                Id = productId,
                Name = "Product 1",
                Description = "Description 1",
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockProductRepository.Setup(r => r.GetByIdAsync(productId, orgId, subscription)).ReturnsAsync(expectedProduct);

            // Act
            var result = await _productQueryHandler.GetProductById(productId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(productId));
            Assert.That(result.Name, Is.EqualTo("Product 1"));
            Assert.That(result.Description, Is.EqualTo("Description 1"));
            _mockProductRepository.Verify(r => r.GetByIdAsync(productId, orgId, subscription), Times.Once);
        }

        [Test]
        public async Task GetProductById_ShouldReturnNull_WhenProductDoesNotExist()
        {
            // Arrange
            var productId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockProductRepository.Setup(r => r.GetByIdAsync(productId, orgId, subscription)).ReturnsAsync((ProductDTO)null);

            // Act
            var result = await _productQueryHandler.GetProductById(productId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Null);
            _mockProductRepository.Verify(r => r.GetByIdAsync(productId, orgId, subscription), Times.Once);
        }
    }
}
