﻿using DataAccessLayer.Context;
using DataAccessLayer.Implementation;
using Interfaces.ShardManagement;
using MemberServiceDataAccessLayer;
using MemberServiceDataAccessLayer.Implementation;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using System;
using Interfaces.ShardManagement;
using System.Threading.Tasks;
using NUnit.Framework.Internal;
using Contracts;
using Microsoft.Extensions.DependencyInjection;

namespace MemberServiceDataAccessLayer.Implementation
{
    public class UnitOfWork : IUnitOfWork
    {
        private readonly AccountDatabaseContext _context;
        private readonly IStringLocalizer<AccountDatabaseContext> _localizer;
        private readonly ILogger<AccountDatabaseContext> _logger;
        private readonly ShardMapManagerService<AccountDatabaseContext, AccountDatabaseContext> _shardMapManagerService;
        IServiceProvider _serviceProvider;

        public IMemberRepository MemberRepository { get; }
        public IProductRepository ProductRepository { get; }
        public ILicenseRepository ProductLicenseRepository { get; }
        public IProductUserAccessRepository ProductUserAccessRepository { get; }
        public IOrganizationRepository OrganizationRepository { get; }
        public IProductOrganizationMappingRepository ProductOrganizationMappingRepository { get; }
        public IRolesRepository RolesRepository { get; }
        public IFacilityRepository FacilityRepository { get; }
        public IAddressesRepository AddressesRepository { get; }
        public IUpToDateRepository UpToDateRepository { get; }
        public IInsuranceRepository InsuranceRepository { get; }
        public IUserThemeRepository UserThemeRepository { get; }
        public IPageRoleMappingRepository PageRoleMappingRepository { get; }
        public IPreDefinedPageRoleMappingRepository PreDefinedPageRoleMappingRepository { get; }
        public IPredefinedVisitTypeRepository PredefinedVisitTypeRepository { get; }
        public IRoleslistRepository RoleslistRepository { get; }
        public IVisitTypeRepository VisitTypeRepository { get; }
        public IVisitStatusRepository VisitStatusRepository { get; }
        public IPagePathRepository PagePathRepository { get; }
        public ICountryRepository CountryRepository { get; }

        public IEmployerRepository EmployerRepository { get; }
        public IGuardianRepository GuardianRepository { get; }

        public IUserLicenseRepository UserLicenseRepository { get; }
        public IPlanTypeRepository PlanTypeRepository { get; }
        public IProductFeatureRepository ProductFeatureRepository { get; }


        public UnitOfWork(AccountDatabaseContext context, ShardMapManagerService<AccountDatabaseContext, AccountDatabaseContext> shardMapManagerService, IStringLocalizer<AccountDatabaseContext> localizer,
                           ILogger<AccountDatabaseContext> logger, IServiceProvider serviceProvider)
        {
            _shardMapManagerService = shardMapManagerService ?? throw new ArgumentNullException(nameof(shardMapManagerService));
            _localizer = localizer ?? throw new ArgumentNullException(nameof(localizer));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));

            MemberRepository = new MemberRepository(_context, _shardMapManagerService, _localizer, _logger);
            ProductRepository = new ProductRepository(_context, _shardMapManagerService, _localizer, _logger);
            ProductLicenseRepository = new LicenseRepository(_context, _shardMapManagerService, _localizer,  _logger);
            ProductUserAccessRepository = new ProductUserAccessRepository(_context, _shardMapManagerService, _localizer, _logger);
            RolesRepository = new RolesRepository(context, _shardMapManagerService,  _localizer, _logger);
            OrganizationRepository = new OrganizationRepository(context);
            ProductOrganizationMappingRepository = new ProductOrganizationMappingRepository(context);
            FacilityRepository = new FacilityRepository(context, _shardMapManagerService, _localizer, _logger);
            AddressesRepository = new AddressesRepository(context, _shardMapManagerService, _localizer,  _logger);
            InsuranceRepository = new InsuranceRepository(context, _shardMapManagerService, _localizer, _logger);
            UpToDateRepository = new UpToDateRepository(context);
            UserThemeRepository = new UserThemeRepository(context, _shardMapManagerService, _localizer, _logger);
            PageRoleMappingRepository = new PageRoleMappingRepository(context, _shardMapManagerService, _localizer, _logger);
            PreDefinedPageRoleMappingRepository = new PreDefinedPageRoleMappingRepository(context, _shardMapManagerService, _localizer, _logger);
            PredefinedVisitTypeRepository = new PredefinedVisitTypeRepository(context);
            RoleslistRepository = new RoleslistRepository(context);
            VisitTypeRepository = new VisitTypeRepository(context, _shardMapManagerService, _localizer, _logger);
            VisitStatusRepository = new VisitStatusRepository(context, _shardMapManagerService, _localizer, _logger);
            PagePathRepository = new PagePathRepository(context, _shardMapManagerService);
            CountryRepository = new CountryRepository(context);
            GuardianRepository = new GuardianRepository(context, _shardMapManagerService, _localizer,  _logger);
            EmployerRepository = new EmployerRepository(context, _shardMapManagerService, _localizer, _logger);
            UserLicenseRepository = new UserLicenseRepository(context);
            PlanTypeRepository = new PlanTypeRepository(context);
            ProductFeatureRepository = new ProductFeatureRepository(context);
        }

        public async Task<int> SaveAsync()
        {
            return await _context.SaveChangesAsync();
        }

        public void Dispose()
        {
            _context.Dispose();
        }
    }
}