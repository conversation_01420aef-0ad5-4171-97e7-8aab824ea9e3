using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using MemberServiceApi.Controllers;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using MemberServiceBusinessLayer;
using ProductServiceApi.Controllers;

namespace MemberServiceApiControllerTestCases
{
    [TestFixture]
    public class ProductsControllerTests
    {
        private Mock<IProductQueryHandler<ProductDTO>> _mockQueryHandler;
        private Mock<IProductCommandHandler<ProductDTO>> _mockCommandHandler;
        private Mock<ILogger<ProductsController>> _mockLogger;
        private Mock<IStringLocalizer<ProductsController>> _mockLocalizer;
        private Mock<IProductMembersQueryHandler<ProductUserAccess>> _mockMembersQueryHandler;
        private Mock<IUserAccessCommandHandler<ProductUserAccess>> _mockAccessCommandHandler;
        private ProductsController _controller;

        [SetUp]
        public void Setup()
        {
            _mockQueryHandler = new Mock<IProductQueryHandler<ProductDTO>>();
            _mockCommandHandler = new Mock<IProductCommandHandler<ProductDTO>>();
            _mockLogger = new Mock<ILogger<ProductsController>>();
            _mockLocalizer = new Mock<IStringLocalizer<ProductsController>>();
            _mockMembersQueryHandler = new Mock<IProductMembersQueryHandler<ProductUserAccess>>();
            _mockAccessCommandHandler = new Mock<IUserAccessCommandHandler<ProductUserAccess>>();

            // Setup localizer to return the key when requested
            _mockLocalizer.Setup(l => l[It.IsAny<string>()]).Returns((string key) => new LocalizedString(key, key));

            _controller = new ProductsController(
                _mockCommandHandler.Object,
                _mockQueryHandler.Object,
                _mockLogger.Object,
                _mockLocalizer.Object,
                _mockMembersQueryHandler.Object,
                _mockAccessCommandHandler.Object
            );
        }

        [Test]
        public async Task Get_WhenProductsExist_ReturnsOkWithProducts()
        {
            // Arrange
            var mockProducts = new List<ProductDTO>
            {
                new ProductDTO { Id = Guid.NewGuid(), Name = "Product 1", Description = "Description 1" },
                new ProductDTO { Id = Guid.NewGuid(), Name = "Product 2", Description = "Description 2" }
            };

            _mockQueryHandler
                .Setup(q => q.GetProduct())
                .ReturnsAsync(mockProducts);

            // Act
            var result = await _controller.Get();

            // Assert
            Assert.That(result.Result, Is.InstanceOf<OkObjectResult>());
            var okResult = result.Result as OkObjectResult;
            Assert.That(okResult.StatusCode, Is.EqualTo(200));
            Assert.That(okResult.Value, Is.EqualTo(mockProducts));
        }

        [Test]
        public async Task Get_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            _mockQueryHandler
                .Setup(q => q.GetProduct())
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.Get();

            // Assert
            Assert.That(result.Result, Is.InstanceOf<StatusCodeResult>());
            var statusCodeResult = result.Result as StatusCodeResult;
            Assert.That(statusCodeResult.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task GetById_WhenProductExists_ReturnsOkWithProduct()
        {
            // Arrange
            var productId = Guid.NewGuid();
            var organizationId = Guid.NewGuid();
            bool subscription = false;
            var mockProduct = new ProductDTO
            {
                Id = productId,
                Name = "Product 1",
                Description = "Description 1",
                OrganizationId = organizationId,
                Subscription = subscription
            };

            _mockQueryHandler
                .Setup(q => q.GetProductById(productId, organizationId, subscription))
                .ReturnsAsync(mockProduct);

            // Act
            var result = await _controller.GetById(productId, organizationId, subscription);

            // Assert
            Assert.That(result.Result, Is.InstanceOf<OkObjectResult>());
            var okResult = result.Result as OkObjectResult;
            Assert.That(okResult.StatusCode, Is.EqualTo(200));
            Assert.That(okResult.Value, Is.EqualTo(mockProduct));
        }

        [Test]
        public async Task GetById_WhenProductDoesNotExist_ReturnsOk()
        {
            // Arrange
            var productId = Guid.NewGuid();
            var organizationId = Guid.NewGuid();
            bool subscription = false;

            _mockQueryHandler
                .Setup(q => q.GetProductById(productId, organizationId, subscription))
                .ReturnsAsync((ProductDTO)null);

            // Act
            var result = await _controller.GetById(productId, organizationId, subscription);

            // Assert
            Assert.That(result.Result, Is.InstanceOf<OkObjectResult>());
            var okResult = result.Result as OkObjectResult;
            Assert.That(okResult.StatusCode, Is.EqualTo(200));
            Assert.That(okResult.Value, Is.Null);
        }

        [Test]
        public async Task GetById_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var productId = Guid.NewGuid();
            var organizationId = Guid.NewGuid();
            bool subscription = false;

            _mockQueryHandler
                .Setup(q => q.GetProductById(productId, organizationId, subscription))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetById(productId, organizationId, subscription);

            // Assert
            Assert.That(result.Result, Is.InstanceOf<StatusCodeResult>());
            var statusCodeResult = result.Result as StatusCodeResult;
            Assert.That(statusCodeResult.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task UpdateById_WhenValidProduct_ReturnsOkWithMessage()
        {
            // Arrange
            var productId = Guid.NewGuid();
            var organizationId = Guid.NewGuid();
            bool subscription = false;
            var product = new ProductDTO
            {
                Id = productId,
                Name = "Updated Product",
                Description = "Updated Description",
                OrganizationId = organizationId,
                Subscription = subscription
            };

            _mockCommandHandler
                .Setup(c => c.UpdateProduct(product, organizationId, subscription))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.UpdateById(productId, product);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult.StatusCode, Is.EqualTo(200));
        }

        [Test]
        public async Task UpdateById_WhenIdMismatch_ReturnsBadRequest()
        {
            // Arrange
            var productId = Guid.NewGuid();
            var product = new ProductDTO
            {
                Id = Guid.NewGuid(), // Different ID
                Name = "Updated Product",
                Description = "Updated Description"
            };

            // Act
            var result = await _controller.UpdateById(productId, product);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult.StatusCode, Is.EqualTo(400));
        }

        [Test]
        public async Task UpdateById_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var productId = Guid.NewGuid();
            var organizationId = Guid.NewGuid();
            bool subscription = false;
            var product = new ProductDTO
            {
                Id = productId,
                Name = "Updated Product",
                Description = "Updated Description",
                OrganizationId = organizationId,
                Subscription = subscription
            };

            _mockCommandHandler
                .Setup(c => c.UpdateProduct(product, organizationId, subscription))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.UpdateById(productId, product);

            // Assert
            Assert.That(result, Is.InstanceOf<StatusCodeResult>());
            var statusCodeResult = result as StatusCodeResult;
            Assert.That(statusCodeResult.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task DeleteById_WhenProductExists_ReturnsOkWithMessage()
        {
            // Arrange
            var productId = Guid.NewGuid();
            var organizationId = Guid.NewGuid();
            bool subscription = false;

            _mockCommandHandler
                .Setup(c => c.DeleteProductById(productId, organizationId, subscription))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.DeleteById(productId, organizationId, subscription);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult.StatusCode, Is.EqualTo(200));
        }

        [Test]
        public async Task DeleteById_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var productId = Guid.NewGuid();
            var organizationId = Guid.NewGuid();
            bool subscription = false;

            _mockCommandHandler
                .Setup(c => c.DeleteProductById(productId, organizationId, subscription))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.DeleteById(productId, organizationId, subscription);

            // Assert
            Assert.That(result, Is.InstanceOf<StatusCodeResult>());
            var statusCodeResult = result as StatusCodeResult;
            Assert.That(statusCodeResult.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task DeleteByEntity_WhenProductExists_ReturnsOkWithMessage()
        {
            // Arrange
            var productId = Guid.NewGuid();
            var organizationId = Guid.NewGuid();
            bool subscription = false;
            var product = new ProductDTO
            {
                Id = productId,
                Name = "Product",
                Description = "Description",
                OrganizationId = organizationId,
                Subscription = subscription
            };

            _mockCommandHandler
                .Setup(c => c.DeleteProductByEntity(product, organizationId, subscription))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.DeleteByEntity(product);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult.StatusCode, Is.EqualTo(200));
        }

        [Test]
        public async Task Registration_WhenValidData_ReturnsOkWithMessage()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            bool subscription = false;
            var registrations = new List<ProductRegistrationDto>
            {
                new ProductRegistrationDto
                {
                    Name = "New Product",
                    Description = "New Description",
                    ByProduct = "Brand A",
                    OrganizationId = organizationId,
                    Subscription = subscription
                }
            };

            _mockCommandHandler
                .Setup(c => c.AddProduct(It.IsAny<List<ProductDTO>>(), organizationId, subscription))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.Registration(registrations);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult.StatusCode, Is.EqualTo(200));
        }

        [Test]
        public async Task Registration_WhenNullOrEmptyList_ReturnsBadRequest()
        {
            // Act
            var result = await _controller.Registration(null);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult.StatusCode, Is.EqualTo(400));

            // Arrange - Empty List
            var emptyList = new List<ProductRegistrationDto>();

            // Act
            result = await _controller.Registration(emptyList);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult.StatusCode, Is.EqualTo(400));
        }
    }
}
