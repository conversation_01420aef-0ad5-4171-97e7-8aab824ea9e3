﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Data.SqlClient;
using Microsoft.Extensions.Localization;
using Microsoft.EntityFrameworkCore;
using System;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Identity.Web.Resource;
using PracticeContracts;
using PracticeBusinessLayer;
using Azure;
using PracticeApi.PracticeApiResources;
using ShardModels;

namespace PracticeApi.Controllers
{
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes")]
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class PracticeController : ControllerBase
    {
        private readonly IPracticeCommandHandler<Tasks> _practiceDataHandler;
        private readonly IPracticeQueryHandler<Tasks> _practiceQueryHandler;
        private readonly ILogger<PracticeController> _logger;
        private readonly IStringLocalizer<PracticeApiStrings> _localizer;


        public PracticeController(
            IPracticeCommandHandler<Tasks> dataHandler,
            IPracticeQueryHandler<Tasks> queryHandler,
            ILogger<PracticeController> logger,
            IStringLocalizer<PracticeApiStrings> localizer
            )
        {
            _practiceDataHandler = dataHandler;
            _practiceQueryHandler = queryHandler;
            _logger = logger;
            _localizer = localizer; // Assigning localizer
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<Tasks>>> Get()
        {
            ActionResult response;
            try
            {
                var tasks = await _practiceQueryHandler.GetTasks();
                response= Ok(tasks);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                response = StatusCode(int.Parse(_localizer["500"]));
            }
            return response;
        }

        [HttpGet("{id:guid}/{orgId:guid}/{Subscription}")]
        public async Task<ActionResult<Tasks>> GetById(Guid id, Guid orgId, bool Subscription)
        {
            ActionResult response;
            try
            {
                var task = await _practiceQueryHandler.GetTaskById(id, orgId, Subscription);
                response= Ok(task);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                response = StatusCode(int.Parse(_localizer["500"]));
            }return response;
        }

        [HttpPut("{id:guid}/{orgId:guid}/{Subscription}")]
        public async Task<IActionResult> UpdateById(Guid id, [FromBody] Tasks task, Guid orgId, bool Subscription)
        {
            IActionResult response;
            try
            {
                await _practiceDataHandler.UpdateTasks(task, orgId, Subscription);
                response= Ok(_localizer["UpdateSuccessful"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["UpdateLogError"]);
                response = StatusCode(int.Parse(_localizer["500"]));
            }return response;
        }

        [HttpDelete("{id:guid}/{orgId:guid}/{Subscription}")]
        public async Task<IActionResult> DeleteById(Guid id,Guid orgId,bool Subscription)
        {
            IActionResult response;
            try
            {
                await _practiceDataHandler.DeleteTasksById(id, orgId, Subscription);
                response= Ok(_localizer["SuccessfulDeletion"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["DeleteLogError"]);
                response = StatusCode(int.Parse(_localizer["500"]));
            }return response;
        }


        [HttpPost]
        [Route("registration/{orgId:guid}/{Subscription}")]
        public async Task<IActionResult> Registration([FromBody] List<Tasks> registrations, Guid orgId, bool Subscription)
        {
            IActionResult response;
            if (registrations == null || registrations.Count == 0)
            {
                response= BadRequest(_localizer["NoTask"]);
            }

            try
            {
                await _practiceDataHandler.AddTasks(registrations, orgId, Subscription);
                response= Ok(_localizer["SuccessfulRegistration"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["PostLogError"]);
                response = StatusCode(int.Parse(_localizer["500"]));
            }return response;
        }

    }
}

