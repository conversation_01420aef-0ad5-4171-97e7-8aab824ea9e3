using NUnit.Framework;
using Moq;
using MemberServiceBusinessLayer.CommandHandler;
using MemberServiceDataAccessLayer.Implementation;
using DataAccessLayer.Implementation;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MemberServiceApiBusinessLayerTestCases.CommandHandlerTests
{
    [TestFixture]
    public class UpToDateCommandHandlerTests
    {
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<IUpToDateRepository> _mockUpToDateRepository;
        private UpToDateCommandHandler _upToDateCommandHandler;

        [SetUp]
        public void Setup()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockUpToDateRepository = new Mock<IUpToDateRepository>();

            _mockUnitOfWork.Setup(u => u.UpToDateRepository).Returns(_mockUpToDateRepository.Object);

            _upToDateCommandHandler = new UpToDateCommandHandler(_mockUnitOfWork.Object);
        }

        [Test]
        public async Task AddUpToDateAsync_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var upToDates = new List<UpToDate>
            {
                new UpToDate 
                { 
                    Id = Guid.NewGuid(), 
                    ProviderName = "Provider 1",
                    SearchName = "Search 1",
                    URL = "https://example1.com",
                    UserName = "user1",
                    Password = "password1"
                },
                new UpToDate 
                { 
                    Id = Guid.NewGuid(), 
                    ProviderName = "Provider 2",
                    SearchName = "Search 2",
                    URL = "https://example2.com",
                    UserName = "user2",
                    Password = "password2"
                }
            };

            _mockUpToDateRepository
                .Setup(r => r.AddAsync(upToDates))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _upToDateCommandHandler.AddUpToDateAsync(upToDates);

            // Assert
            _mockUpToDateRepository.Verify(r => r.AddAsync(upToDates), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateUpToDateAsync_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var upToDate = new UpToDate 
            { 
                Id = Guid.NewGuid(), 
                ProviderName = "Updated Provider",
                SearchName = "Updated Search",
                URL = "https://updated-example.com",
                UserName = "updated-user",
                Password = "updated-password"
            };

            _mockUpToDateRepository
                .Setup(r => r.UpdateAsync(upToDate))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _upToDateCommandHandler.UpdateUpToDateAsync(upToDate);

            // Assert
            _mockUpToDateRepository.Verify(r => r.UpdateAsync(upToDate), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteUpToDateAsync_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var upToDateId = Guid.NewGuid();

            _mockUpToDateRepository
                .Setup(r => r.DeleteByIdAsync(upToDateId))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _upToDateCommandHandler.DeleteUpToDateAsync(upToDateId);

            // Assert
            _mockUpToDateRepository.Verify(r => r.DeleteByIdAsync(upToDateId), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }
    }
}
