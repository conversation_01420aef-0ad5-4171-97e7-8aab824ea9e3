using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using MemberServiceApi.Controllers;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using MemberServiceBusinessLayer;

namespace MemberServiceApiControllerTestCases
{
    [TestFixture]
    public class EmployerControllerTests
    {
        private Mock<IEmployerQueryHandler<Employer>> _mockQueryHandler;
        private Mock<IEmployerCommandHandler<Employer>> _mockCommandHandler;
        private Mock<ILogger<EmployerController>> _mockLogger;
        private Mock<IStringLocalizer<EmployerController>> _mockLocalizer;
        private EmployerController _controller;

        [SetUp]
        public void Setup()
        {
            _mockQueryHandler = new Mock<IEmployerQueryHandler<Employer>>();
            _mockCommandHandler = new Mock<IEmployerCommandHandler<Employer>>();
            _mockLogger = new Mock<ILogger<EmployerController>>();
            _mockLocalizer = new Mock<IStringLocalizer<EmployerController>>();

            // Setup localizer to return the key when requested
            _mockLocalizer.Setup(l => l[It.IsAny<string>()]).Returns((string key) => new LocalizedString(key, key));

            _controller = new EmployerController(
                _mockQueryHandler.Object,
                _mockCommandHandler.Object,
                _mockLogger.Object,
                _mockLocalizer.Object
            );
        }

        [Test]
        public async Task GetAllEmployer_WhenEmployersExist_ReturnsOkWithEmployers()
        {
            // Arrange
            var mockEmployers = new List<Employer>
            {
                new Employer { EmployerId = Guid.NewGuid(), EmployerName = "Company A" },
                new Employer { EmployerId = Guid.NewGuid(), EmployerName = "Company B" }
            };

            _mockQueryHandler
                .Setup(q => q.GetAllEmployerAsync())
                .ReturnsAsync(mockEmployers);

            // Act
            var result = await _controller.GetAllEmployer();

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult.StatusCode, Is.EqualTo(200));
            Assert.That(okResult.Value, Is.EqualTo(mockEmployers));
        }

        [Test]
        public async Task GetAllEmployer_WhenNoEmployersExist_ReturnsNotFound()
        {
            // Arrange
            _mockQueryHandler
                .Setup(q => q.GetAllEmployerAsync())
                .ReturnsAsync(new List<Employer>());

            // Act
            var result = await _controller.GetAllEmployer();

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task GetAllEmployer_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            _mockQueryHandler
                .Setup(q => q.GetAllEmployerAsync())
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetAllEmployer();

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task GetEmployerById_WhenEmployerExists_ReturnsOkWithEmployer()
        {
            // Arrange
            var employerId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var mockEmployer = new Employer
            {
                EmployerId = employerId,
                EmployerName = "Company A",
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockQueryHandler
                .Setup(q => q.GetEmployerByIdAsync(employerId, orgId, subscription))
                .ReturnsAsync(mockEmployer);

            // Act
            var result = await _controller.GetEmployerById(subscription, employerId, orgId);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult.StatusCode, Is.EqualTo(200));
            Assert.That(okResult.Value, Is.EqualTo(mockEmployer));
        }

        [Test]
        public async Task GetEmployerById_WhenEmployerDoesNotExist_ReturnsNotFound()
        {
            // Arrange
            var employerId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockQueryHandler
                .Setup(q => q.GetEmployerByIdAsync(employerId, orgId, subscription))
                .ReturnsAsync((Employer)null);

            // Act
            var result = await _controller.GetEmployerById(subscription, employerId, orgId);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task AddEmployer_WhenValidEmployer_ReturnsCreatedAtAction()
        {
            // Arrange
            var employerId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var employer = new Employer
            {
                EmployerId = employerId,
                EmployerName = "Company A",
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockCommandHandler
                .Setup(c => c.AddEmployerAsync(It.IsAny<List<Employer>>(), orgId, subscription))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.AddEmployer(employer);

            // Assert
            Assert.That(result, Is.InstanceOf<CreatedAtActionResult>());
            var createdResult = result as CreatedAtActionResult;
            Assert.That(createdResult.StatusCode, Is.EqualTo(201));
            Assert.That(createdResult.ActionName, Is.EqualTo(nameof(EmployerController.GetEmployerById)));
            Assert.That(createdResult.RouteValues["id"], Is.EqualTo(employerId));
            Assert.That(createdResult.Value, Is.Not.Null);

            // The controller returns an anonymous object with specific fields, not the full Employer object
            var returnedValue = createdResult.Value;
            var employerIdProperty = returnedValue.GetType().GetProperty("EmployerId");
            var returnedEmployerId = employerIdProperty?.GetValue(returnedValue);
            Assert.That(returnedEmployerId, Is.EqualTo(employer.EmployerId));
        }

        [Test]
        public async Task AddEmployer_WhenNullEmployer_ReturnsBadRequest()
        {
            // Act
            var result = await _controller.AddEmployer(null);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult.StatusCode, Is.EqualTo(400));
        }

        [Test]
        public async Task AddEmployer_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var employer = new Employer
            {
                EmployerId = Guid.NewGuid(),
                EmployerName = "Company A"
            };

            _mockCommandHandler
                .Setup(c => c.AddEmployerAsync(It.IsAny<List<Employer>>(), It.IsAny<Guid>(), It.IsAny<bool>()))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.AddEmployer(employer);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task DeleteEmployer_WhenEmployerExists_ReturnsNoContent()
        {
            // Arrange
            var employerId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockCommandHandler
                .Setup(c => c.DeleteEmployerAsync(employerId, orgId, subscription))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.DeleteEmployer(subscription, employerId, orgId);

            // Assert
            Assert.That(result, Is.InstanceOf<NoContentResult>());
            var noContentResult = result as NoContentResult;
            Assert.That(noContentResult.StatusCode, Is.EqualTo(204));
        }

        [Test]
        public async Task DeleteEmployer_WhenEmployerDoesNotExist_ReturnsNotFound()
        {
            // Arrange
            var employerId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockCommandHandler
                .Setup(c => c.DeleteEmployerAsync(employerId, orgId, subscription))
                .ThrowsAsync(new KeyNotFoundException());

            // Act
            var result = await _controller.DeleteEmployer(subscription, employerId, orgId);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task DeleteEmployer_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var employerId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockCommandHandler
                .Setup(c => c.DeleteEmployerAsync(employerId, orgId, subscription))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.DeleteEmployer(subscription, employerId, orgId);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task UpdateEmployer_WhenValidEmployer_ReturnsNoContent()
        {
            // Arrange
            var employerId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var employer = new Employer
            {
                EmployerId = employerId,
                EmployerName = "Updated Company",
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockCommandHandler
                .Setup(c => c.UpdateEmployerAsync(employer, orgId, subscription))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.UpdateEmployer(employerId, employer);

            // Assert
            Assert.That(result, Is.InstanceOf<NoContentResult>());
            var noContentResult = result as NoContentResult;
            Assert.That(noContentResult.StatusCode, Is.EqualTo(204));
        }

        [Test]
        public async Task UpdateEmployer_WhenNullEmployer_ReturnsBadRequest()
        {
            // Arrange
            var employerId = Guid.NewGuid();

            // Act
            var result = await _controller.UpdateEmployer(employerId, null!);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult.StatusCode, Is.EqualTo(400));
        }

        [Test]
        public async Task UpdateEmployer_WhenIdMismatch_ReturnsBadRequest()
        {
            // Arrange
            var employerId = Guid.NewGuid();
            var differentId = Guid.NewGuid();
            var employer = new Employer
            {
                EmployerId = differentId,
                EmployerName = "Company A"
            };

            // Act
            var result = await _controller.UpdateEmployer(employerId, employer);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult.StatusCode, Is.EqualTo(400));
        }

        [Test]
        public async Task UpdateEmployer_WhenEmployerDoesNotExist_ReturnsNotFound()
        {
            // Arrange
            var employerId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var employer = new Employer
            {
                EmployerId = employerId,
                EmployerName = "Company A",
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockCommandHandler
                .Setup(c => c.UpdateEmployerAsync(employer, orgId, subscription))
                .ThrowsAsync(new KeyNotFoundException());

            // Act
            var result = await _controller.UpdateEmployer(employerId, employer);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task UpdateEmployer_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var employerId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var employer = new Employer
            {
                EmployerId = employerId,
                EmployerName = "Company A",
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockCommandHandler
                .Setup(c => c.UpdateEmployerAsync(employer, orgId, subscription))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.UpdateEmployer(employerId, employer);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult.StatusCode, Is.EqualTo(500));
        }
    }
}
