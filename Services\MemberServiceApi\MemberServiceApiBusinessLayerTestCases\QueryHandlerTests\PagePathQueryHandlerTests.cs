using NUnit.Framework;
using Moq;
using MemberServiceBusinessLayer.QueryHandler;
using MemberServiceDataAccessLayer.Implementation;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;

namespace MemberServiceApiBusinessLayerTestCases.QueryHandlerTests
{
    [TestFixture]
    public class PagePathQueryHandlerTests
    {
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<IPagePathRepository> _mockPagePathRepository;
        private PagePathQueryHandler _pagePathQueryHandler;

        [SetUp]
        public void Setup()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockPagePathRepository = new Mock<IPagePathRepository>();

            _mockUnitOfWork.Setup(u => u.PagePathRepository).Returns(_mockPagePathRepository.Object);

            _pagePathQueryHandler = new PagePathQueryHandler(_mockUnitOfWork.Object);
        }

        [Test]
        public async Task GetPagePathByIdAsync_ShouldReturnPagePath_WhenPagePathExists()
        {
            // Arrange
            var pagePathId = Guid.NewGuid();
            var expectedPagePath = new PagePath 
            { 
                PageId = pagePathId, 
                PagePathValue = "/dashboard",
                CreatedBy = Guid.NewGuid(),
                CreatedDate = DateTime.Now,
                IsActive = true
            };

            _mockPagePathRepository
                .Setup(r => r.GetByIdAsync(pagePathId))
                .ReturnsAsync(expectedPagePath);

            // Act
            var result = await _pagePathQueryHandler.GetPagePathByIdAsync(pagePathId);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.PageId, Is.EqualTo(pagePathId));
            Assert.That(result.PagePathValue, Is.EqualTo("/dashboard"));
            _mockPagePathRepository.Verify(r => r.GetByIdAsync(pagePathId), Times.Once);
        }

        [Test]
        public async Task GetPagePathByIdAsync_ShouldReturnNull_WhenPagePathDoesNotExist()
        {
            // Arrange
            var pagePathId = Guid.NewGuid();

            _mockPagePathRepository
                .Setup(r => r.GetByIdAsync(pagePathId))
                .ReturnsAsync((PagePath)null);

            // Act
            var result = await _pagePathQueryHandler.GetPagePathByIdAsync(pagePathId);

            // Assert
            Assert.That(result, Is.Null);
            _mockPagePathRepository.Verify(r => r.GetByIdAsync(pagePathId), Times.Once);
        }

        [Test]
        public async Task GetAllPagePathsAsync_ShouldReturnAllPagePaths()
        {
            // Arrange
            var expectedPagePaths = new List<PagePath>
            {
                new PagePath 
                { 
                    PageId = Guid.NewGuid(), 
                    PagePathValue = "/dashboard",
                    IsActive = true
                },
                new PagePath 
                { 
                    PageId = Guid.NewGuid(), 
                    PagePathValue = "/users",
                    IsActive = true
                }
            };

            _mockPagePathRepository
                .Setup(r => r.GetAllAsync())
                .ReturnsAsync(expectedPagePaths);

            // Act
            var result = await _pagePathQueryHandler.GetAllPagePathsAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result, Is.EquivalentTo(expectedPagePaths));
            _mockPagePathRepository.Verify(r => r.GetAllAsync(), Times.Once);
        }

        [Test]
        public void Constructor_ShouldThrowArgumentNullException_WhenUnitOfWorkIsNull()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new PagePathQueryHandler(null));
        }
    }
}
