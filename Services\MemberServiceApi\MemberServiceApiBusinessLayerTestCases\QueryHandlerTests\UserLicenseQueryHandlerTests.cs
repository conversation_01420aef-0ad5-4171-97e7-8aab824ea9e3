using NUnit.Framework;
using Moq;
using MemberServiceBusinessLayer.QueryHandler;
using MemberServiceDataAccessLayer.Implementation;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;

namespace MemberServiceApiBusinessLayerTestCases.QueryHandlerTests
{
    [TestFixture]
    public class UserLicenseQueryHandlerTests
    {
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<IUserLicenseRepository> _mockUserLicenseRepository;
        private UserLicenseQueryHandler _userLicenseQueryHandler;

        [SetUp]
        public void Setup()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockUserLicenseRepository = new Mock<IUserLicenseRepository>();

            _mockUnitOfWork.Setup(u => u.UserLicenseRepository).Returns(_mockUserLicenseRepository.Object);

            _userLicenseQueryHandler = new UserLicenseQueryHandler(_mockUnitOfWork.Object);
        }

        [Test]
        public async Task GetUserLicenseByIdAsync_ShouldReturnUserLicense_WhenUserLicenseExists()
        {
            // Arrange
            var licenseId = Guid.NewGuid();
            var expectedLicense = new UserLicense 
            { 
                Id = licenseId, 
                PlanId = Guid.NewGuid(),
                OrganizationId = Guid.NewGuid(),
                ProductId = Guid.NewGuid(),
                Seats = 10,
                ActiveUsers = 5,
                CreatedDate = DateTime.Now.AddDays(-30),
                CreatedBy = Guid.NewGuid(),
                Status = true,
                ExpiryDate = DateTime.Now.AddYears(1)
            };

            _mockUserLicenseRepository
                .Setup(r => r.GetByIdAsync(licenseId))
                .ReturnsAsync(expectedLicense);

            // Act
            var result = await _userLicenseQueryHandler.GetUserLicenseByIdAsync(licenseId);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(licenseId));
            Assert.That(result.Seats, Is.EqualTo(10));
            Assert.That(result.ActiveUsers, Is.EqualTo(5));
            _mockUserLicenseRepository.Verify(r => r.GetByIdAsync(licenseId), Times.Once);
        }

        [Test]
        public async Task GetUserLicenseByIdAsync_ShouldReturnNull_WhenUserLicenseDoesNotExist()
        {
            // Arrange
            var licenseId = Guid.NewGuid();

            _mockUserLicenseRepository
                .Setup(r => r.GetByIdAsync(licenseId))
                .ReturnsAsync((UserLicense)null);

            // Act
            var result = await _userLicenseQueryHandler.GetUserLicenseByIdAsync(licenseId);

            // Assert
            Assert.That(result, Is.Null);
            _mockUserLicenseRepository.Verify(r => r.GetByIdAsync(licenseId), Times.Once);
        }

        [Test]
        public async Task GetUserLicenseByOrganizationIdAsync_ShouldReturnUserLicense_WhenUserLicenseExists()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            var expectedLicense = new UserLicense 
            { 
                Id = Guid.NewGuid(), 
                PlanId = Guid.NewGuid(),
                OrganizationId = organizationId,
                ProductId = Guid.NewGuid(),
                Seats = 10,
                ActiveUsers = 5,
                CreatedDate = DateTime.Now.AddDays(-30),
                CreatedBy = Guid.NewGuid(),
                Status = true,
                ExpiryDate = DateTime.Now.AddYears(1)
            };

            _mockUserLicenseRepository
                .Setup(r => r.GetByOrganizationIdAsync(organizationId))
                .ReturnsAsync(expectedLicense);

            // Act
            var result = await _userLicenseQueryHandler.GetUserLicenseByOrganizationIdAsync(organizationId);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.OrganizationId, Is.EqualTo(organizationId));
            Assert.That(result.Seats, Is.EqualTo(10));
            Assert.That(result.ActiveUsers, Is.EqualTo(5));
            _mockUserLicenseRepository.Verify(r => r.GetByOrganizationIdAsync(organizationId), Times.Once);
        }

        [Test]
        public async Task GetUserLicenseByOrganizationIdAsync_ShouldReturnNull_WhenUserLicenseDoesNotExist()
        {
            // Arrange
            var organizationId = Guid.NewGuid();

            _mockUserLicenseRepository
                .Setup(r => r.GetByOrganizationIdAsync(organizationId))
                .ReturnsAsync((UserLicense)null);

            // Act
            var result = await _userLicenseQueryHandler.GetUserLicenseByOrganizationIdAsync(organizationId);

            // Assert
            Assert.That(result, Is.Null);
            _mockUserLicenseRepository.Verify(r => r.GetByOrganizationIdAsync(organizationId), Times.Once);
        }

        [Test]
        public async Task GetAllUserLicensesAsync_ShouldReturnAllUserLicenses()
        {
            // Arrange
            var expectedLicenses = new List<UserLicense>
            {
                new UserLicense 
                { 
                    Id = Guid.NewGuid(), 
                    OrganizationId = Guid.NewGuid(),
                    Seats = 10,
                    ActiveUsers = 5
                },
                new UserLicense 
                { 
                    Id = Guid.NewGuid(), 
                    OrganizationId = Guid.NewGuid(),
                    Seats = 20,
                    ActiveUsers = 15
                }
            };

            _mockUserLicenseRepository
                .Setup(r => r.GetAllAsync())
                .ReturnsAsync(expectedLicenses);

            // Act
            var result = await _userLicenseQueryHandler.GetAllUserLicensesAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result, Is.EquivalentTo(expectedLicenses));
            _mockUserLicenseRepository.Verify(r => r.GetAllAsync(), Times.Once);
        }

        [Test]
        public void Constructor_ShouldThrowArgumentNullException_WhenUnitOfWorkIsNull()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new UserLicenseQueryHandler(null));
        }
    }
}
