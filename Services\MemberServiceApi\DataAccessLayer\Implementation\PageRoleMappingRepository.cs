using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Contracts;
using DataAccessLayer.Context;
using Interfaces.ShardManagement;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;

namespace MemberServiceDataAccessLayer.Implementation
{
    public class PageRoleMappingRepository : ShardGenericRepository<PageRoleMapping>, IPageRoleMappingRepository
    {
        private readonly AccountDatabaseContext _context;
        private readonly IStringLocalizer<AccountDatabaseContext> _localizer;
        private readonly ILogger<AccountDatabaseContext> _logger;
        private readonly ShardMapManagerService<AccountDatabaseContext, AccountDatabaseContext> _shardMapManagerService;
        private readonly string _shardMapName;

        public PageRoleMappingRepository(AccountDatabaseContext context, ShardMapManagerService<AccountDatabaseContext, AccountDatabaseContext> shardMapManagerService, IStringLocalizer<AccountDatabaseContext> localizer,
            ILogger<AccountDatabaseContext> logger):base(context, shardMapManagerService, localizer, logger)
        {
            _context = context;
            _shardMapManagerService = shardMapManagerService;
            _localizer = localizer;
            _logger = logger;
            _shardMapName = Environment.GetEnvironmentVariable("shardMapName");
        }

        public async Task<List<PageRoleMapping>> GetByPagePathAsync(string pagePath, Guid OrganizationId, bool subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(subscription, OrganizationId, _shardMapName);
            if (context == null) return new List<PageRoleMapping>();

            var result = await context.PageRoleMappings
                .Where(prm => prm.PagePath.Contains(pagePath, StringComparison.OrdinalIgnoreCase))
                .ToListAsync();
            return result;
        }


        public async Task<List<string>> GetRolesByPagePathAsync(string pagePath, Guid OrganizationId, bool subscription)
        {
            if (string.IsNullOrWhiteSpace(pagePath))
                throw new ArgumentException("Page path cannot be null or whitespace.", nameof(pagePath));

            using var context = _shardMapManagerService.GetNextKey(subscription, OrganizationId, _shardMapName);
            if (context == null) return new List<string>();

            var result = await context.PageRoleMappings
                .Where(prm =>
                    prm.PagePath.ToLower() == pagePath.ToLower() &&
                    prm.IsActive &&
                    prm.HasAccess &&
                    prm.OrganizationID == OrganizationId)
                .Select(prm => prm.RoleName)
                .Distinct()
                .ToListAsync();

            return result;
        }

    }
}
