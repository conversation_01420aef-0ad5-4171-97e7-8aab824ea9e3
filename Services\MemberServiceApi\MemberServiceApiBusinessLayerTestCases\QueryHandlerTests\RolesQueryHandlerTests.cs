using NUnit.Framework;
using Moq;
using MemberServiceBusinessLayer.QueryHandler;
using MemberServiceDataAccessLayer.Implementation;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;

namespace MemberServiceApiBusinessLayerTestCases.QueryHandlerTests
{
    [TestFixture]
    public class RolesQueryHandlerTests
    {
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<IRolesRepository> _mockRolesRepository;
        private RolesQueryHandler _rolesQueryHandler;

        [SetUp]
        public void Setup()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockRolesRepository = new Mock<IRolesRepository>();

            _mockUnitOfWork.Setup(u => u.RolesRepository).Returns(_mockRolesRepository.Object);

            _rolesQueryHandler = new RolesQueryHandler(_mockUnitOfWork.Object);
        }

        [Test]
        public async Task GetRoleByIdAsync_ShouldReturnRole_WhenRoleExists()
        {
            // Arrange
            var roleId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var expectedRole = new Role
            {
                RoleId = roleId,
                RoleName = "Admin",

                OrganizationId = orgId,
                IsActive = true
            };

            _mockRolesRepository
                .Setup(r => r.GetByIdAsync(roleId, orgId, subscription))
                .ReturnsAsync(expectedRole);

            // Act
            var result = await _rolesQueryHandler.GetRoleByIdAsync(roleId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.RoleId, Is.EqualTo(roleId));
            Assert.That(result.RoleName, Is.EqualTo("Admin"));

            _mockRolesRepository.Verify(r => r.GetByIdAsync(roleId, orgId, subscription), Times.Once);
        }

        [Test]
        public async Task GetRoleByIdAsync_ShouldReturnNull_WhenRoleDoesNotExist()
        {
            // Arrange
            var roleId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockRolesRepository
                .Setup(r => r.GetByIdAsync(roleId, orgId, subscription))
                .ReturnsAsync((Role)null);

            // Act
            var result = await _rolesQueryHandler.GetRoleByIdAsync(roleId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Null);
            _mockRolesRepository.Verify(r => r.GetByIdAsync(roleId, orgId, subscription), Times.Once);
        }

        [Test]
        public async Task GetRolesByNameAsync_ShouldReturnRoles_WhenRolesExist()
        {
            // Arrange
            var roleName = "Admin";
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var allRoles = new List<Role>
            {
                new Role
                {
                    RoleId = Guid.NewGuid(),
                    RoleName = "Admin",

                    OrganizationId = orgId,
                    IsActive = true
                },
                new Role
                {
                    RoleId = Guid.NewGuid(),
                    RoleName = "Super Admin",

                    OrganizationId = orgId,
                    IsActive = true
                },
                new Role
                {
                    RoleId = Guid.NewGuid(),
                    RoleName = "User",

                    OrganizationId = orgId,
                    IsActive = true
                }
            };

            _mockRolesRepository
                .Setup(r => r.GetAllAsync())
                .ReturnsAsync(allRoles);

            // Act
            var result = await _rolesQueryHandler.GetRolesByNameAsync(roleName, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result.Select(r => r.RoleName).ToList(), Contains.Item("Admin").And.Contains("Super Admin"));
            _mockRolesRepository.Verify(r => r.GetAllAsync(), Times.Once);
        }

        [Test]
        public async Task GetRolesByNameAsync_ShouldReturnEmptyList_WhenNoRolesMatch()
        {
            // Arrange
            var roleName = "NonExistentRole";
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var allRoles = new List<Role>
            {
                new Role
                {
                    RoleId = Guid.NewGuid(),
                    RoleName = "Admin",

                    OrganizationId = orgId,
                    IsActive = true
                },
                new Role
                {
                    RoleId = Guid.NewGuid(),
                    RoleName = "User",

                    OrganizationId = orgId,
                    IsActive = true
                }
            };

            _mockRolesRepository
                .Setup(r => r.GetAllAsync())
                .ReturnsAsync(allRoles);

            // Act
            var result = await _rolesQueryHandler.GetRolesByNameAsync(roleName, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(0));
            _mockRolesRepository.Verify(r => r.GetAllAsync(), Times.Once);
        }

        [Test]
        public void Constructor_ShouldThrowArgumentNullException_WhenUnitOfWorkIsNull()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new RolesQueryHandler(null));
        }
    }
}
