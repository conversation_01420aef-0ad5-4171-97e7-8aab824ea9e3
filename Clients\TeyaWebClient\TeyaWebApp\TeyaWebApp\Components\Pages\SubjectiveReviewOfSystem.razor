﻿@page "/SubjectiveReviewOfSystem"
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize]
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
@using TeyaWebApp.Components.Layout
@using TeyaWebApp.TeyaAIScribeResources
@using MudBlazor
@using Syncfusion.Blazor.RichTextEditor
@using Syncfusion.Blazor.Grids
@using System
@using Syncfusion.Blazor.DropDowns

<SfRichTextEditor Value="@editorContent" @ref="RichTextEditor" ValueChanged="@((string newValue)=>HandelRichTextChange(newValue))">
    <RichTextEditorToolbarSettings Items="@Tools">
        <RichTextEditorCustomToolbarItems>
            <RichTextEditorCustomToolbarItem Name="Symbol">
                <Template>
                    <MudIconButton Icon="@Icons.Material.Filled.ModeEditOutline"
                                   @onclick="OpenNewDialogBox"
                                   Size="Size.Small" />
                </Template>
            </RichTextEditorCustomToolbarItem>
        </RichTextEditorCustomToolbarItems>
    </RichTextEditorToolbarSettings>
</SfRichTextEditor>

<MudDialog @ref="_reviewofsystem" Style="width: 80vw; max-width: 1100px;" OnBackdropClick="HandleBackdropClick">
    <TitleContent>
        <MudText Typo="Typo.h6" Style="font-size: 1rem; font-weight: 600;">
            @Localizer["ReviewOfSystem"]
        </MudText>
        <MudIconButton Icon="@Icons.Material.Filled.Close" Size="Size.Small" OnClick="CancelData" Style="margin: -4px; position: absolute; right: 16px; top: 4px;" />
    </TitleContent>
    <DialogContent>
        <div style="margin: -12px; display: flex; flex-direction: column;">
            <div style="padding: 20px; flex-grow: 1; background-color: #ffffff;">
               
                <SfGrid @ref="ReviewOfSytemGrid" TValue="ReviewOfSystem" Style="font-size: 0.85rem; margin-top: 24px;" DataSource="@reviewofsytem" AllowPaging="true" GridLines="GridLine.Both" Toolbar="@(new List<string>() { "Add" })"
                    PageSettings-PageSize="5">
                    <GridEditSettings AllowAdding="true" AllowEditing="true" AllowDeleting="true" Mode="EditMode.Normal"></GridEditSettings>
                    <GridPageSettings PageSize="10"></GridPageSettings>
                    <GridEvents OnActionBegin="ActionBeginHandler" OnActionComplete="ActionCompletedHandler" TValue="ReviewOfSystem"></GridEvents>
                    <GridColumns>
                        <GridColumn Field="ReviewOfSystemId" IsPrimaryKey="true" Visible="false"></GridColumn>
                         <GridColumn Field="CreatedDate" HeaderText="@Localizer["CreatedDate"]" Width="30" Format="dd-MM-yyyy" TextAlign="TextAlign.Center"></GridColumn>
                       <GridColumn Field="UpdatedDate" HeaderText="@Localizer["UpdatedDate"]" Width="30" Format="dd-MM-yyyy" TextAlign="TextAlign.Center"></GridColumn>
                        <GridColumn Field="Decription" HeaderText="@Localizer["AdditionalsReviews"]" Width="70" HeaderTextAlign="TextAlign.Center" TextAlign="TextAlign.Left"></GridColumn>
                        <GridColumn Field="@nameof(ReviewOfSystem.ChestPain)"
                                    HeaderText="@Localizer["ChestPain"]"
                                    Width="25"
                                    TextAlign="TextAlign.Center"
                                    EditTemplate="@IsChestPainEditTemplate">
                        </GridColumn>
                        <GridColumn Field="@nameof(ReviewOfSystem.Congestion)"
                                    HeaderText="@Localizer["Congestion"]"
                                    Width="25"
                                    TextAlign="TextAlign.Center"
                                    EditTemplate="@IsCongestionEditTemplate">
                        </GridColumn>
                        <GridColumn Field="@nameof(ReviewOfSystem.ItchyEyes)"
                                    HeaderText="@Localizer["ItchyEyes"]"
                                    Width="25"
                                    TextAlign="TextAlign.Center"
                                    EditTemplate="@IsItchyEyesEditTemplate">
                        </GridColumn>
                        <GridColumn HeaderText="@Localizer["Actions"]" Width="15" TextAlign="TextAlign.Center">
                            <GridCommandColumns>
                                <GridCommandColumn Type="CommandButtonType.Delete" ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-delete",CssClass = "e-flat"})" />
                            </GridCommandColumns>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>
                <div style="display: flex; justify-content: flex-end; gap: 12px; padding: 16px 24px; border-top: 1px solid #E0E0E0;">
                    <MudButton Color="Color.Secondary"
                               Variant="Variant.Outlined"
                               OnClick="CancelData"
                               Dense="true"
                               Style="min-width: 120px; height: 40px; font-weight: 600;">
                        @Localizer["Cancel"]
                    </MudButton>
                    <MudButton Color="Color.Primary"
                               Variant="Variant.Filled"
                               OnClick="SaveData"
                               Dense="true"
                               Style="min-width: 120px; height: 40px; font-weight: 600;">
                        @Localizer["Save"]
                    </MudButton>
                </div>
            </div>
        </div>
    </DialogContent>
</MudDialog>


