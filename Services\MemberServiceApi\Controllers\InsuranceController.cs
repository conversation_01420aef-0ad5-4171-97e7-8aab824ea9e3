﻿using Contracts;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MemberServiceBusinessLayer;

namespace InsuranceServiceApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class InsuranceController : ControllerBase
    {
        private readonly IInsuranceQueryHandler<Insurance> _insuranceQueryHandler;
        private readonly IInsuranceCommandHandler<Insurance> _insuranceCommandHandler;
        private readonly ILogger<InsuranceController> _logger;
        private readonly IStringLocalizer<InsuranceController> _localizer;

        public InsuranceController(
            IInsuranceQueryHandler<Insurance> insuranceQueryHandler,
            IInsuranceCommandHandler<Insurance> insuranceCommandHandler,
            ILogger<InsuranceController> logger,
            IStringLocalizer<InsuranceController> localizer)
        {
            _insuranceQueryHandler = insuranceQueryHandler ?? throw new ArgumentNullException(nameof(insuranceQueryHandler));
            _insuranceCommandHandler = insuranceCommandHandler ?? throw new ArgumentNullException(nameof(insuranceCommandHandler));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _localizer = localizer ?? throw new ArgumentNullException(nameof(localizer));
        }

        [HttpGet("{id:Guid}/{OrgID:Guid}/{Subscription}")]

        public async Task<IActionResult> GetInsuranceById(Guid id, Guid OrgID, bool Subscription)
        {
            IActionResult response;
            _logger.LogInformation(_localizer["FetchingInsuranceWithID"], id);
            var insurance = await _insuranceQueryHandler.GetInsuranceByIdAsync(id, OrgID, Subscription);
            if (insurance == null)
            {
                _logger.LogWarning(_localizer["InsuranceNotFound"], id);
                response = NotFound(_localizer["InsuranceNotFoundMessage"]);
            }
            else
            {
                _logger.LogInformation(_localizer["InsuranceFetchedSuccessfully"], id);
                response = Ok(insurance);
            }
            return response;
        }

        [HttpPost]
        public async Task<IActionResult> AddInsurance([FromBody] Insurance insurance)
        {
            IActionResult response;
            if (insurance == null)
            {
                _logger.LogWarning(_localizer["InvalidInsuranceData"]);
                response = BadRequest(_localizer["InsuranceInvalidMessage"]);
            }
            else
            {
                try
                {
                    _logger.LogInformation(_localizer["AddingNewInsurance"]);
                    
                    await _insuranceCommandHandler.AddInsuranceAsync(new List<Insurance> { insurance }, insurance.OrganizationId, insurance.Subscription);
                    _logger.LogInformation(_localizer["InsuranceAddedSuccessfully"], insurance.InsuranceId);
                    response = CreatedAtAction(nameof(GetInsuranceById), new { id = insurance.InsuranceId, OrgID = insurance.OrganizationId, Subscription = insurance.Subscription }, insurance);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["ErrorAddingInsurance"]);
                    response = StatusCode(StatusCodes.Status500InternalServerError, _localizer["ErrorAddingInsurance"]);
                }
            }
            return response;
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateInsurance(Guid id, [FromBody] Insurance insurance)
        {
            IActionResult response;
            if (insurance == null || id != insurance.InsuranceId)
            {
                _logger.LogWarning(_localizer["InvalidDataForUpdate"], id);
                response = BadRequest(_localizer["InsuranceInvalidMessage"]);
            }
            else
            {
                try
                {
                    _logger.LogInformation(_localizer["UpdatingInsuranceWithID"], id);

                    await _insuranceCommandHandler.UpdateInsuranceAsync(insurance, insurance.OrganizationId, insurance.Subscription);
                    _logger.LogInformation(_localizer["InsuranceUpdatedSuccessfully"], id);
                    response = NoContent();
                }
                catch (KeyNotFoundException)
                {
                    _logger.LogWarning(_localizer["InsuranceNotFoundForUpdate"], id);
                    response = NotFound(_localizer["InsuranceNotFoundMessage"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["ErrorUpdatingInsurance"]);
                    response = StatusCode(500, _localizer["InternalServerErrorMessage"]);
                }
            }
            return response;
        }

        [HttpDelete("{id:guid}/{OrgID:Guid}/{Subscription}")]

        public async Task<IActionResult> DeleteInsurance(Guid id, Guid OrgID, bool Subscription)
        {
            IActionResult response;
            try
            {
                _logger.LogInformation(_localizer["DeletingInsuranceWithID"], id);
                await _insuranceCommandHandler.DeleteInsuranceAsync(id, OrgID, Subscription);
                _logger.LogInformation(_localizer["InsuranceDeletedSuccessfully"], id);
                response = NoContent();
            }
            catch (KeyNotFoundException)
            {
                _logger.LogWarning(_localizer["InsuranceNotFoundForDeletion"], id);
                response = NotFound(_localizer["InsuranceNotFoundMessage"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorDeletingInsurance"]);
                response = StatusCode(500, _localizer["InternalServerErrorMessage"]);
            }
            return response;
        }

       

        [HttpGet]
        [Route("{orgId:guid}/{Subscription}")]
        public async Task<IActionResult> GetAllInsurances(Guid orgId, bool Subscription)
        {
            IActionResult response;
            _logger.LogInformation(_localizer["FetchingAllInsurances"]);
            try
            {
                var insurances = await _insuranceQueryHandler.GetAllInsurancesAsync(orgId, Subscription);

                if (insurances == null || insurances.Count == 0)
                {
                    _logger.LogWarning(_localizer["NoInsurancesFound"]);
                    response = NotFound(_localizer["InsurancesNotFoundMessage"]);
                }
                else
                {
                    _logger.LogInformation(_localizer["AllInsurancesFetchedSuccessfully"]);
                    response = Ok(insurances);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingInsurances"]);
                response = StatusCode(500, _localizer["InternalServerErrorMessage"]);
            }
            return response;
        }
    }
}
