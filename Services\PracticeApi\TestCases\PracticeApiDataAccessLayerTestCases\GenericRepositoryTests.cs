using Microsoft.EntityFrameworkCore;
using Moq;
using PracticeContracts;
using PracticeDataAccessLayer.Context;
using PracticeDataAccessLayer.Implementation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using PracticeDataAccessLayer.PracticeDataAccessLayerResources;

namespace PracticeApiDataAccessLayerTestCases
{
    [TestFixture]
    public class GenericRepositoryTests
    {
        private Mock<PracticeDatabaseContext> _mockContext;
        private Mock<DbSet<TestEntity>> _mockDbSet;
        private GenericRepository<TestEntity> _repository;
        private List<TestEntity> _testEntities;
        private TestEntity _testEntity;

        // Test entity class for testing the generic repository
        public class TestEntity
        {
            public Guid Id { get; set; }
            public string Name { get; set; } = string.Empty; // Initialize with empty string to avoid CS8618 warning
        }

        [SetUp]
        public void Setup()
        {
            // Create test data
            _testEntity = new TestEntity { Id = Guid.NewGuid(), Name = "Test Entity" };
            _testEntities = new List<TestEntity>
            {
                _testEntity,
                new TestEntity { Id = Guid.NewGuid(), Name = "Test Entity 2" },
                new TestEntity { Id = Guid.NewGuid(), Name = "Test Entity 3" }
            };

            // Setup mock DbSet
            _mockDbSet = new Mock<DbSet<TestEntity>>();
            var queryable = _testEntities.AsQueryable();

            // Setup IQueryable
            _mockDbSet.As<IQueryable<TestEntity>>().Setup(m => m.Provider).Returns(queryable.Provider);
            _mockDbSet.As<IQueryable<TestEntity>>().Setup(m => m.Expression).Returns(queryable.Expression);
            _mockDbSet.As<IQueryable<TestEntity>>().Setup(m => m.ElementType).Returns(queryable.ElementType);
            _mockDbSet.As<IQueryable<TestEntity>>().Setup(m => m.GetEnumerator()).Returns(() => queryable.GetEnumerator());

            // Setup FindAsync
            _mockDbSet.Setup(m => m.FindAsync(It.IsAny<object[]>())).ReturnsAsync((object[] ids) => _testEntities.FirstOrDefault(e => e.Id.Equals((Guid)ids[0])));

            // Setup AddRangeAsync
            _mockDbSet.Setup(m => m.AddRangeAsync(It.IsAny<IEnumerable<TestEntity>>(), default))
                .Returns(Task.CompletedTask);

            // Setup mock context
            _mockContext = new Mock<PracticeDatabaseContext>(
                new DbContextOptions<PracticeDatabaseContext>(),
                Mock.Of<IStringLocalizer<PracticeDataAccessLayerStrings>>(),
                Mock.Of<ILogger<PracticeDatabaseContext>>());
            _mockContext.Setup(c => c.Set<TestEntity>()).Returns(_mockDbSet.Object);
            _mockContext.Setup(c => c.SaveChangesAsync(default)).ReturnsAsync(1);

            // Create repository with mock context
            _repository = new GenericRepository<TestEntity>(_mockContext.Object);
        }

        [Test]
        public async Task GetAllAsync_ShouldReturnAllEntities()
        {
            // Create a new mock DbSet specifically for this test
            var mockDbSet = new Mock<DbSet<TestEntity>>();
            var queryable = _testEntities.AsQueryable();

            // Setup IQueryable
            mockDbSet.As<IQueryable<TestEntity>>().Setup(m => m.Provider).Returns(queryable.Provider);
            mockDbSet.As<IQueryable<TestEntity>>().Setup(m => m.Expression).Returns(queryable.Expression);
            mockDbSet.As<IQueryable<TestEntity>>().Setup(m => m.ElementType).Returns(queryable.ElementType);
            mockDbSet.As<IQueryable<TestEntity>>().Setup(m => m.GetEnumerator()).Returns(queryable.GetEnumerator);

            // Setup IAsyncEnumerable
            mockDbSet.As<IAsyncEnumerable<TestEntity>>()
                .Setup(m => m.GetAsyncEnumerator(It.IsAny<CancellationToken>()))
                .Returns(GetMockAsyncEnumerator(_testEntities));

            // Setup a new context with this DbSet
            var mockContext = new Mock<PracticeDatabaseContext>(
                new DbContextOptions<PracticeDatabaseContext>(),
                Mock.Of<IStringLocalizer<PracticeDataAccessLayerStrings>>(),
                Mock.Of<ILogger<PracticeDatabaseContext>>());
            mockContext.Setup(c => c.Set<TestEntity>()).Returns(mockDbSet.Object);

            // Create a new repository with this context
            var repository = new GenericRepository<TestEntity>(mockContext.Object);

            // Act
            var result = await repository.GetAllAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(_testEntities.Count));
        }

        // Helper method to create a mock async enumerator
        private static IAsyncEnumerator<T> GetMockAsyncEnumerator<T>(IEnumerable<T> data)
        {
            var enumerator = new Mock<IAsyncEnumerator<T>>();
            var enumeration = data.GetEnumerator();

            enumerator.Setup(e => e.MoveNextAsync())
                .ReturnsAsync(enumeration.MoveNext);

            enumerator.Setup(e => e.Current)
                .Returns(() => enumeration.Current);

            return enumerator.Object;
        }

        [Test]
        public async Task GetByIdAsync_WithValidId_ShouldReturnEntity()
        {
            // Arrange
            var id = _testEntity.Id;

            // Act
            var result = await _repository.GetByIdAsync(id);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(id));
            _mockDbSet.Verify(m => m.FindAsync(new object[] { id }), Times.Once);
        }

        [Test]
        public async Task GetByIdAsync_WithInvalidId_ShouldReturnNull()
        {
            // Arrange
            var invalidId = Guid.NewGuid();

            // Act
            var result = await _repository.GetByIdAsync(invalidId);

            // Assert
            Assert.That(result, Is.Null);
            _mockDbSet.Verify(m => m.FindAsync(new object[] { invalidId }), Times.Once);
        }

        [Test]
        public async Task AddAsync_ShouldAddEntitiesToDbSet()
        {
            // Arrange
            var newEntities = new List<TestEntity>
            {
                new TestEntity { Id = Guid.NewGuid(), Name = "New Entity 1" },
                new TestEntity { Id = Guid.NewGuid(), Name = "New Entity 2" }
            };

            // Act
            await _repository.AddAsync(newEntities);

            // Assert
            _mockDbSet.Verify(m => m.AddRangeAsync(newEntities, default), Times.Once);
        }

        [Test]
        public async Task UpdateAsync_ShouldUpdateEntityAndSaveChanges()
        {
            // Arrange
            var entityToUpdate = new TestEntity { Id = Guid.NewGuid(), Name = "Updated Entity" };

            // Act
            await _repository.UpdateAsync(entityToUpdate);

            // Assert
            _mockDbSet.Verify(m => m.Update(entityToUpdate), Times.Once);
            _mockContext.Verify(m => m.SaveChangesAsync(default), Times.Once);
        }

        [Test]
        public async Task DeleteByEntityAsync_ShouldRemoveEntityAndSaveChanges()
        {
            // Arrange
            var entityToDelete = new TestEntity { Id = Guid.NewGuid(), Name = "Entity to Delete" };

            // Act
            await _repository.DeleteByEntityAsync(entityToDelete);

            // Assert
            _mockDbSet.Verify(m => m.Remove(entityToDelete), Times.Once);
            _mockContext.Verify(m => m.SaveChangesAsync(default), Times.Once);
        }

        [Test]
        public async Task DeleteByIdAsync_WithExistingEntity_ShouldDeleteEntityAndSaveChanges()
        {
            // Arrange
            var id = _testEntity.Id;

            // Act
            await _repository.DeleteByIdAsync(id);

            // Assert
            _mockDbSet.Verify(m => m.FindAsync(new object[] { id }), Times.Once);
            _mockDbSet.Verify(m => m.Remove(_testEntity), Times.Once);
            _mockContext.Verify(m => m.SaveChangesAsync(default), Times.Once);
        }

        [Test]
        public async Task DeleteByIdAsync_WithNonExistingEntity_ShouldNotDeleteAnything()
        {
            // Arrange
            var nonExistingId = Guid.NewGuid();

            // Act
            await _repository.DeleteByIdAsync(nonExistingId);

            // Assert
            _mockDbSet.Verify(m => m.FindAsync(new object[] { nonExistingId }), Times.Once);
            _mockDbSet.Verify(m => m.Remove(It.IsAny<TestEntity>()), Times.Never);
            _mockContext.Verify(m => m.SaveChangesAsync(default), Times.Never);
        }
    }
}
