using Contracts;
using DataAccessLayer.Context;
using Interfaces.ShardManagement;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MemberServiceDataAccessLayer.Implementation
{
    public class UserThemeRepository : ShardGenericRepository<UserTheme>, IUserThemeRepository
    {
        private readonly AccountDatabaseContext _context;

        public UserThemeRepository(AccountDatabaseContext context, ShardMapManagerService<AccountDatabaseContext, AccountDatabaseContext> _shardMapManagerService, IStringLocalizer<AccountDatabaseContext> localizer,
            ILogger<AccountDatabaseContext> logger) : base(context, _shardMapManagerService, localizer, logger)
        {
            _context = context;
        }
    }
}
