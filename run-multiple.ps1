$projects = @(
    ".\Services\MemberServiceApi\MemberServiceApi.csproj",
    ".\Services\AppointmentsApi\Appointments.csproj"
	".\Services\PracticeApi\PracticeApi.csproj"
	".\Services\EncounterNotesApi\EncounterNotesService.csproj"
	".\Services\AlertsApi\AlertsApi.csproj"
	".\Clients\TeyaWebClient\TeyaWebApp\TeyaWebApp\TeyaWebApp.csproj"
)

foreach ($proj in $projects) {
    Start-Process "dotnet" "run --project `"$proj`""
}
