name: build

on:
  workflow_dispatch:
  schedule:
    - cron: '0 0 * * *'
  push:
    branches: [main]
  pull_request:
    branches: [main]

permissions:
  id-token: write
  contents: write
  actions: read

jobs:
  check-branch-name:
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    steps:
      - name: Check Branch Name
        uses: actions/github-script@v6
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const branchName = context.payload.pull_request.head.ref;
            const regex = /^(TWC|TM|TB|PO|DEV)-\d+-[\w-]+$/;

            if (!regex.test(branchName)) {
              core.setFailed('Branch name does not match the required format: TWC/TM/TB/PO/DEV-<NUMBER>-<desc>');
            } else {
              console.log('Branch name matches the required format');
            }

      - name: Notify MS Teams on Branch Name Success
        if: success()
        run: |
          curl -H "Content-Type: application/json" \
          -d '{
            "text": "✅ Branch name check passed!\n- Workflow: check-branch-name\n- Project:Check Branch Name\n- Branch: ${{ github.head_ref }}\n- Triggered by: ${{ github.actor }}\n- Commit: ${{ github.sha }}"
          }' \
          ${{ secrets.TEAMS_WEBHOOK_URL }}

      - name: Notify MS Teams on Branch Name Failure
        if: failure()
        run: |
          curl -H "Content-Type: application/json" \
          -d '{
            "text": "❌ Branch name check failed!\n- Workflow: check-branch-name\n- Project:Check Branch Name\n- Branch: ${{ github.head_ref }}\n- Triggered by: ${{ github.actor }}\n- Commit: ${{ github.sha }}"
          }' \
          ${{ secrets.TEAMS_WEBHOOK_URL }}

  build-and-test:
    runs-on: ubuntu-latest
    needs: check-branch-name
    if: always() && (needs.check-branch-name.result == 'success' || needs.check-branch-name.result == 'skipped')
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Restore test projects dependencies
        shell: pwsh
        run: |
          $testProjects = Get-ChildItem -Path . -Recurse -Include *.csproj | Where-Object { $_.FullName -match 'Test' }
          if ($testProjects.Count -eq 0) {
            Write-Host "No test projects found."
            exit 1
          }
          foreach ($proj in $testProjects) {
            Write-Host "Restoring $($proj.FullName)"
            dotnet restore $proj.FullName
          }

      - name: Discover and run all test projects
        shell: pwsh
        run: |
          $testProjects = Get-ChildItem -Path . -Recurse -Include *.csproj | Where-Object { $_.FullName -match 'Test' }
          foreach ($proj in $testProjects) {
            Write-Host "Running tests in $($proj.FullName)"
            dotnet test $proj.FullName --no-restore --verbosity normal
          }

      - name: Notify MS Teams on Test Success
        if: success()
        run: |
          curl -H "Content-Type: application/json" \
          -d '{
            "text": "✅ All unit tests passed!\n- Workflow: build-and-test\n- Project:Build and Test\n- Branch: ${{ github.ref_name }}\n- Commit: ${{ github.sha }}\n- Triggered by: ${{ github.actor }}"
          }' \
          ${{ secrets.TEAMS_WEBHOOK_URL }}

      - name: Notify MS Teams on Test Failure
        if: failure()
        run: |
          curl -H "Content-Type: application/json" \
          -d '{
            "text": "❌ Unit tests failed!\n- Workflow: build-and-test\n- Project:Build and Test\n- Branch: ${{ github.ref_name }}\n- Commit: ${{ github.sha }}\n- Triggered by: ${{ github.actor }}"
          }' \
          ${{ secrets.TEAMS_WEBHOOK_URL }}
  versioning:
    runs-on: ubuntu-latest
    needs: build-and-test
    # Only run versioning on main branch pushes (not PRs) and when build-and-test succeeds
    if: success() && needs.build-and-test.result == 'success' && github.event_name == 'push' && github.ref == 'refs/heads/main'
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Analyze changes and determine version strategy
        id: changes
        run: |
          # Get the list of changed files in the last commit
          CHANGED_FILES=$(git diff --name-only HEAD~1 HEAD)
          echo "Changed files: $CHANGED_FILES"

          # Initialize change type counters
          BREAKING_CHANGES=0
          FEATURE_CHANGES=0
          BUG_FIXES=0
          CONFIG_CHANGES=0

          # Analyze commit messages for conventional commit patterns
          COMMIT_MSG=$(git log -1 --pretty=%B)
          echo "Commit message: $COMMIT_MSG"

          # Check for breaking changes
          if echo "$COMMIT_MSG" | grep -qiE "(BREAKING CHANGE|breaking:|!:)" || echo "$CHANGED_FILES" | grep -qE "(Migration|Schema|Database)" ; then
            BREAKING_CHANGES=1
            echo "🚨 Breaking changes detected"
          fi

          # Check for new features
          if echo "$COMMIT_MSG" | grep -qiE "(feat:|feature:|add:|new:)" || echo "$CHANGED_FILES" | grep -qE "(Controller|Service|Component)" ; then
            FEATURE_CHANGES=1
            echo "✨ Feature changes detected"
          fi

          # Check for bug fixes
          if echo "$COMMIT_MSG" | grep -qiE "(fix:|bug:|hotfix:|patch:)" ; then
            BUG_FIXES=1
            echo "🐛 Bug fixes detected"
          fi

          # Check for configuration changes
          if echo "$CHANGED_FILES" | grep -qE "\.(json|yml|yaml|config|settings)$" ; then
            CONFIG_CHANGES=1
            echo "⚙️ Configuration changes detected"
          fi

          # Check if any project-related files were changed
          PROJECT_FILES_CHANGED=$(echo "$CHANGED_FILES" | grep -E '\.(csproj|cs|razor|js|css|html|json|yml|yaml)$' || true)

          if [ -n "$PROJECT_FILES_CHANGED" ]; then
            echo "has_changes=true" >> $GITHUB_OUTPUT
            echo "breaking_changes=$BREAKING_CHANGES" >> $GITHUB_OUTPUT
            echo "feature_changes=$FEATURE_CHANGES" >> $GITHUB_OUTPUT
            echo "bug_fixes=$BUG_FIXES" >> $GITHUB_OUTPUT
            echo "config_changes=$CONFIG_CHANGES" >> $GITHUB_OUTPUT
            echo "Project-related files were changed, versioning needed"
          else
            echo "has_changes=false" >> $GITHUB_OUTPUT
            echo "No project-related files changed, skipping versioning"
          fi

      - name: Generate version based on change type
        id: version
        if: steps.changes.outputs.has_changes == 'true'
        run: |
          # Get current date components
          YEAR=$(date +'%Y')
          MONTH=$(date +'%m')
          DAY=$(date +'%d')
          HOUR=$(date +'%H')
          MINUTE=$(date +'%M')

          # Determine version strategy based on change types
          BREAKING_CHANGES=${{ steps.changes.outputs.breaking_changes }}
          FEATURE_CHANGES=${{ steps.changes.outputs.feature_changes }}
          BUG_FIXES=${{ steps.changes.outputs.bug_fixes }}
          CONFIG_CHANGES=${{ steps.changes.outputs.config_changes }}

          if [ "$BREAKING_CHANGES" = "1" ]; then
            # Major version: YYYY.0.0 for breaking changes
            VERSION="v${YEAR}.0.0"
            VERSION_TYPE="major"
            echo "🚨 Major version bump due to breaking changes"
          elif [ "$FEATURE_CHANGES" = "1" ]; then
            # Minor version: YYYY.MM.0 for new features
            VERSION="v${YEAR}.${MONTH}.0"
            VERSION_TYPE="minor"
            echo "✨ Minor version bump due to new features"
          elif [ "$BUG_FIXES" = "1" ]; then
            # Patch version: YYYY.MM.DD for bug fixes
            VERSION="v${YEAR}.${MONTH}.${DAY}"
            VERSION_TYPE="patch"
            echo "🐛 Patch version bump due to bug fixes"
          elif [ "$CONFIG_CHANGES" = "1" ]; then
            # Build version: YYYY.MM.DD.HHMM for config changes
            VERSION="v${YEAR}.${MONTH}.${DAY}.${HOUR}${MINUTE}"
            VERSION_TYPE="build"
            echo "⚙️ Build version bump due to configuration changes"
          else
            # Default: timestamp-based version for other changes
            VERSION="v${YEAR}.${MONTH}.${DAY}.${HOUR}${MINUTE}"
            VERSION_TYPE="auto"
            echo "🔄 Automatic version bump for general changes"
          fi

          # Generate a ticket number for branch naming convention
          TICKET_NUMBER=$(date +'%Y%m%d%H%M')

          echo "VERSION=$VERSION" >> $GITHUB_OUTPUT
          echo "VERSION_TYPE=$VERSION_TYPE" >> $GITHUB_OUTPUT
          echo "TICKET_NUMBER=$TICKET_NUMBER" >> $GITHUB_OUTPUT
          echo "Generated version: $VERSION (type: $VERSION_TYPE)"
          echo "Generated ticket number: $TICKET_NUMBER"

      - name: Detect changed projects
        id: detect_projects
        if: steps.changes.outputs.has_changes == 'true'
        run: |
          # Get changed files from the last commit
          CHANGED_FILES=$(git diff --name-only HEAD~1 HEAD)

          # Find .csproj files that were directly changed or are in directories with changed files
          CHANGED_PROJECTS=""

          # Check each changed file and find related .csproj files
          for file in $CHANGED_FILES; do
            # Get the directory of the changed file
            DIR=$(dirname "$file")

            # Look for .csproj files in the same directory or parent directories
            while [ "$DIR" != "." ] && [ "$DIR" != "/" ]; do
              CSPROJ_IN_DIR=$(find "$DIR" -maxdepth 1 -name "*.csproj" 2>/dev/null || true)
              if [ -n "$CSPROJ_IN_DIR" ]; then
                CHANGED_PROJECTS="$CHANGED_PROJECTS $CSPROJ_IN_DIR"
                break
              fi
              DIR=$(dirname "$DIR")
            done
          done

          # Remove duplicates and sort
          CHANGED_PROJECTS=$(echo "$CHANGED_PROJECTS" | tr ' ' '\n' | sort -u | tr '\n' ' ')

          # If no specific projects found, check for breaking changes that affect all projects
          if [ -z "$CHANGED_PROJECTS" ] && [ "${{ steps.changes.outputs.breaking_changes }}" = "1" ]; then
            echo "Breaking changes detected - versioning all main projects"
            CHANGED_PROJECTS=$(find . -name "*.csproj" -path "*/Services/*" -o -path "*/Clients/*" -o -path "*/Infrastructure/*" | grep -v Test | head -10)
          elif [ -z "$CHANGED_PROJECTS" ]; then
            # If still no projects found, version main projects for safety
            CHANGED_PROJECTS=$(find . -name "*.csproj" -path "*/Services/*" -o -path "*/Clients/*" -o -path "*/Infrastructure/*" | grep -v Test | head -10)
          fi

          echo "projects=$CHANGED_PROJECTS" >> $GITHUB_OUTPUT
          echo "Projects to version: $CHANGED_PROJECTS"

      - name: Update version in changed projects
        if: steps.changes.outputs.has_changes == 'true'
        run: |
          VERSION=${{ steps.version.outputs.VERSION }}
          PROJECTS="${{ steps.detect_projects.outputs.projects }}"

          echo "Setting version $VERSION in changed .csproj files..."

          for csproj in $PROJECTS; do
            if [ -f "$csproj" ]; then
              echo "Updating $csproj"

              # Check if Version element exists
              if grep -q "<Version>" "$csproj"; then
                # Update existing Version element
                sed -i "s|<Version>.*</Version>|<Version>$VERSION</Version>|" "$csproj"
              else
                # Add Version element to the first PropertyGroup
                sed -i "0,/<\/PropertyGroup>/s|<\/PropertyGroup>|    <Version>$VERSION</Version>\n  </PropertyGroup>|" "$csproj"
              fi

              echo "Updated $csproj with version $VERSION"
            else
              echo "Warning: $csproj not found"
            fi
          done

      - name: Create Pull Request for Version Bump
        if: steps.changes.outputs.has_changes == 'true'
        uses: peter-evans/create-pull-request@v5
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          commit-message: "ci: ${{ steps.version.outputs.VERSION_TYPE }} version bump to ${{ steps.version.outputs.VERSION }}"
          branch: "DEV-${{ steps.version.outputs.TICKET_NUMBER }}-version-bump"
          base: main
          title: "🔖 ${{ steps.version.outputs.VERSION_TYPE == 'major' && '🚨 Major' || steps.version.outputs.VERSION_TYPE == 'minor' && '✨ Minor' || steps.version.outputs.VERSION_TYPE == 'patch' && '🐛 Patch' || steps.version.outputs.VERSION_TYPE == 'build' && '⚙️ Build' || '🔄 Auto' }} Version Bump to ${{ steps.version.outputs.VERSION }}"
          body: |
            ## Automated Version Bump

            This PR contains an automated **${{ steps.version.outputs.VERSION_TYPE }}** version bump to `${{ steps.version.outputs.VERSION }}`.

            ### Version Strategy:
            - **Type**: ${{ steps.version.outputs.VERSION_TYPE }}
            - **Version**: ${{ steps.version.outputs.VERSION }}
            - **Reason**: ${{ steps.changes.outputs.breaking_changes == '1' && '🚨 Breaking changes detected' || steps.changes.outputs.feature_changes == '1' && '✨ New features added' || steps.changes.outputs.bug_fixes == '1' && '🐛 Bug fixes applied' || steps.changes.outputs.config_changes == '1' && '⚙️ Configuration changes' || '🔄 General code changes' }}

            ### Change Analysis:
            - **Breaking Changes**: ${{ steps.changes.outputs.breaking_changes == '1' && '✅ Yes' || '❌ No' }}
            - **New Features**: ${{ steps.changes.outputs.feature_changes == '1' && '✅ Yes' || '❌ No' }}
            - **Bug Fixes**: ${{ steps.changes.outputs.bug_fixes == '1' && '✅ Yes' || '❌ No' }}
            - **Config Changes**: ${{ steps.changes.outputs.config_changes == '1' && '✅ Yes' || '❌ No' }}

            ### Projects Updated:
            ```
            ${{ steps.detect_projects.outputs.projects }}
            ```

            ### Version Format Guide:
            - **Major** (`YYYY.0.0`): Breaking changes, database migrations
            - **Minor** (`YYYY.MM.0`): New features, new controllers/services
            - **Patch** (`YYYY.MM.DD`): Bug fixes, hotfixes
            - **Build** (`YYYY.MM.DD.HHMM`): Configuration changes
            - **Auto** (`YYYY.MM.DD.HHMM`): General code changes

            ### Triggered By:
            - **Actor**: ${{ github.actor }}
            - **Commit**: ${{ github.sha }}
            - **Branch**: ${{ github.ref_name }}

            ---

            ⚠️ **Note**: This is an automated PR based on change analysis. Please review the changes before merging.
          labels: |
            automated
            version-bump
            ${{ steps.version.outputs.VERSION_TYPE }}
            ci/cd
          assignees: ${{ github.actor }}

      - name: Notify MS Teams on Versioning Success
        if: steps.changes.outputs.has_changes == 'true'
        run: |
          # Determine emoji based on version type
          case "${{ steps.version.outputs.VERSION_TYPE }}" in
            "major") EMOJI="🚨" ;;
            "minor") EMOJI="✨" ;;
            "patch") EMOJI="🐛" ;;
            "build") EMOJI="⚙️" ;;
            *) EMOJI="🔄" ;;
          esac

          curl -H "Content-Type: application/json" \
          -d "{
            \"text\": \"$EMOJI Versioning PR created!\n- Type: ${{ steps.version.outputs.VERSION_TYPE }}\n- Version: ${{ steps.version.outputs.VERSION }}\n- Branch: DEV-${{ steps.version.outputs.TICKET_NUMBER }}-version-bump\n- Changes: ${{ steps.changes.outputs.breaking_changes == '1' && 'Breaking' || '' }}${{ steps.changes.outputs.feature_changes == '1' && ' Features' || '' }}${{ steps.changes.outputs.bug_fixes == '1' && ' Fixes' || '' }}${{ steps.changes.outputs.config_changes == '1' && ' Config' || '' }}\n- Projects: ${{ steps.detect_projects.outputs.projects }}\n- Triggered by: ${{ github.actor }}\"
          }" \
          ${{ secrets.TEAMS_WEBHOOK_URL }}

      - name: Notify MS Teams on No Changes
        if: steps.changes.outputs.has_changes == 'false'
        run: |
          curl -H "Content-Type: application/json" \
          -d '{
            "text": "ℹ️ Versioning skipped - no project changes detected\n- Workflow: versioning\n- Branch: ${{ github.ref_name }}\n- Triggered by: ${{ github.actor }}\n- Commit: ${{ github.sha }}"
          }' \
          ${{ secrets.TEAMS_WEBHOOK_URL }}


  cache-dependencies:
    runs-on: ubuntu-latest
    steps:
      - name: Cache Docker layers
        uses: actions/cache@v3
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-docker-${{ hashFiles('**/Dockerfile') }}
          restore-keys: |
            ${{ runner.os }}-docker-

  build:
    runs-on: ubuntu-latest
    needs: [cache-dependencies, build-and-test]
    if: success()
    strategy:
      matrix:
        include:
          - name: MemberService API
            CONTAINER_REGISTRY_LOGIN_SERVER: teyahealthdev.azurecr.io
            DOCKER_FILE_PATH: Services/MemberServiceApi/Dockerfile
            PROJECT_NAME_FOR_DOCKER: memberservice
            REGISTRY_USERNAME: TeyaHealthDev
          - name: EncounterNotesService API
            CONTAINER_REGISTRY_LOGIN_SERVER: teyahealthdev.azurecr.io
            DOCKER_FILE_PATH: Services/EncounterNotesApi/Dockerfile
            PROJECT_NAME_FOR_DOCKER: encounternotes
            REGISTRY_USERNAME: TeyaHealthDev
          - name: Practice API
            CONTAINER_REGISTRY_LOGIN_SERVER: teyahealthdev.azurecr.io
            DOCKER_FILE_PATH: Services/PracticeApi/Dockerfile
            PROJECT_NAME_FOR_DOCKER: practice
            REGISTRY_USERNAME: TeyaHealthDev
          - name: Appointments API
            CONTAINER_REGISTRY_LOGIN_SERVER: teyahealthdev.azurecr.io
            DOCKER_FILE_PATH: Services/AppointmentsApi/Dockerfile
            PROJECT_NAME_FOR_DOCKER: appointments
            REGISTRY_USERNAME: TeyaHealthDev
          - name: TeyaWebApp
            CONTAINER_REGISTRY_LOGIN_SERVER: teyahealthdev.azurecr.io
            DOCKER_FILE_PATH: Clients/TeyaWebClient/TeyaWebApp/TeyaWebApp/Dockerfile
            PROJECT_NAME_FOR_DOCKER: teyawebapp
            REGISTRY_USERNAME: TeyaHealthDev

    name: Build ${{ matrix.name }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to container registry
        uses: docker/login-action@v3
        with:
          registry: ${{ matrix.CONTAINER_REGISTRY_LOGIN_SERVER }}
          username: ${{ matrix.REGISTRY_USERNAME }}
          password: ${{ secrets.REGISTRY_PASSWORD }}

      - name: Build container image
        uses: docker/build-push-action@v5
        with:
          context: .
          push: false
          tags: ${{ matrix.CONTAINER_REGISTRY_LOGIN_SERVER }}/${{ matrix.PROJECT_NAME_FOR_DOCKER }}:${{ github.sha }}
          file: ${{ matrix.DOCKER_FILE_PATH }}
          cache-from: type=local,src=/tmp/.buildx-cache
          cache-to: type=local,dest=/tmp/.buildx-cache-new

      - name: Move cache
        run: |
          rm -rf /tmp/.buildx-cache
          mv /tmp/.buildx-cache-new /tmp/.buildx-cache

      - name: Notify MS Teams on Success
        if: success()
        run: |
          curl -H "Content-Type: application/json" \
          -d '{
            "text": "✅ Build succeeded!\n- Workflow: build\n- Project: ${{ matrix.name }}\n- Triggered by: ${{ github.actor }}\n- Branch: ${{ github.ref_name }}\n- Commit: ${{ github.sha }}"
          }' \
          ${{ secrets.TEAMS_WEBHOOK_URL }}

      - name: Notify MS Teams on Failure
        if: failure()
        run: |
          curl -H "Content-Type: application/json" \
          -d '{
            "text": "❌ Build failed!\n- Workflow: build\n- Project: ${{ matrix.name }}\n- Triggered by: ${{ github.actor }}\n- Branch: ${{ github.ref_name }}\n- Commit: ${{ github.sha }}"
          }' \
          ${{ secrets.TEAMS_WEBHOOK_URL }}

  build-sql-database:
    runs-on: ubuntu-latest
    name: Build SQL Database
    needs: build-and-test
    if: success()
    steps:
      - uses: actions/checkout@v4

      - name: Setup .NET SDK
        uses: actions/setup-dotnet@v3
        with:
          dotnet-version: '6.0.x'

      - name: Build Database Project
        run: dotnet build Database/TeyaHealthSQLDatabase/TeyaHealthSQLDatabase.sqlproj -c Debug

      - name: Install SQLPackage
        run: |
          curl -L https://aka.ms/sqlpackage-linux -o sqlpackage.zip
          sudo mkdir -p /usr/local/sqlpackage
          sudo unzip -o sqlpackage.zip -d /usr/local/sqlpackage
          sudo chmod +x /usr/local/sqlpackage/sqlpackage
          echo "/usr/local/sqlpackage" >> $GITHUB_PATH

      - name: Notify MS Teams on DB Deploy Success
        if: success()
        run: |
          curl -H "Content-Type: application/json" \
          -d '{
            "text": "✅ Azure SQL Database deployment succeeded!\n- Workflow: Build Database Project\n- Project: sql-database-build\n- Triggered by: ${{ github.actor }}\n- Branch: ${{ github.ref_name }}\n- Commit: ${{ github.sha }}"
          }' \
          ${{ secrets.TEAMS_WEBHOOK_URL }}

      - name: Notify MS Teams on DB Deploy Failure
        if: failure()
        run: |
          curl -H "Content-Type: application/json" \
          -d '{
            "text": "❌ Azure SQL Database deployment failed!\n- Workflow: Build Database Project\n- Project: sql-database-build\n- Triggered by: ${{ github.actor }}\n- Branch: ${{ github.ref_name }}\n- Commit: ${{ github.sha }}"
          }' \
          ${{ secrets.TEAMS_WEBHOOK_URL }}
