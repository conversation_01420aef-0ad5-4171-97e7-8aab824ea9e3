using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using PracticeContracts;
using PracticeDataAccessLayer.Context;
using PracticeDataAccessLayer.Implementation;
using PracticeDataAccessLayer.PracticeDataAccessLayerResources;
using PracticeNotesDataAccessLayer.Implementation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading;
using System.Threading.Tasks;
using Interfaces.ShardManagement;

namespace PracticeApiDataAccessLayerTestCases
{
    [TestFixture]
    public class PracticeRepositoryTests
    {
        private Mock<PracticeDatabaseContext> _mockContext;
        private Mock<DbSet<Tasks>> _mockDbSet;
        private Mock<IStringLocalizer<PracticeDataAccessLayerStrings>> _mockLocalizer;
        private Mock<ILogger<PracticeDatabaseContext>> _mockLogger;
        private PracticeRepository _repository;
        private Tasks _testTask;
        private Guid _testOrgId;

        [SetUp]
        public void Setup()
        {
            // Create test data
            _testTask = new Tasks
            {
                Id = Guid.NewGuid(),
                SSN = "***********",
                PatientName = "Test Patient",
                TaskType = "Appointment",
                Subject = "Annual Checkup",
                Status = "Scheduled"
            };

            var tasks = new List<Tasks> { _testTask };
            _testOrgId = Guid.NewGuid();

            // Setup mock DbSet
            _mockDbSet = new Mock<DbSet<Tasks>>();
            var queryable = tasks.AsQueryable();

            _mockDbSet.As<IQueryable<Tasks>>().Setup(m => m.Provider).Returns(queryable.Provider);
            _mockDbSet.As<IQueryable<Tasks>>().Setup(m => m.Expression).Returns(queryable.Expression);
            _mockDbSet.As<IQueryable<Tasks>>().Setup(m => m.ElementType).Returns(queryable.ElementType);
            _mockDbSet.As<IQueryable<Tasks>>().Setup(m => m.GetEnumerator()).Returns(() => queryable.GetEnumerator());
            _mockDbSet.Setup(m => m.FindAsync(It.IsAny<object[]>())).ReturnsAsync((object[] ids) => tasks.FirstOrDefault(t => t.Id.Equals((Guid)ids[0])));

            // Setup mock context
            _mockContext = new Mock<PracticeDatabaseContext>(
                new DbContextOptions<PracticeDatabaseContext>(),
                Mock.Of<IStringLocalizer<PracticeDataAccessLayerStrings>>(),
                Mock.Of<ILogger<PracticeDatabaseContext>>());
            _mockContext.Setup(c => c.SaveChangesAsync(default)).ReturnsAsync(1);

            // Setup mock localizer and logger
            _mockLocalizer = new Mock<IStringLocalizer<PracticeDataAccessLayerStrings>>();
            _mockLogger = new Mock<ILogger<PracticeDatabaseContext>>();
        }

    }
}
