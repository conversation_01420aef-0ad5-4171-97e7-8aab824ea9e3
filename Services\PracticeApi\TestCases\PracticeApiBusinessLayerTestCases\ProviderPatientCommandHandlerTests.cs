using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using PracticeBusinessLayer.CommandHandler;
using PracticeContracts;
using PracticeDataAccessLayer.Implementation;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace PracticeApiBusinessLayerTestCases
{
    [TestFixture]
    public class ProviderPatientCommandHandlerTests
    {
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<ILogger<ProviderPatientCommandHandler>> _mockLogger;
        private Mock<IProviderPatientRepository> _mockProviderPatientRepository;
        private ProviderPatientCommandHandler _commandHandler;

        [SetUp]
        public void Setup()
        {
            // Initialize mocks
            _mockConfiguration = new Mock<IConfiguration>();
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockLogger = new Mock<ILogger<ProviderPatientCommandHandler>>();
            _mockProviderPatientRepository = new Mock<IProviderPatientRepository>();

            // Setup repository mock
            _mockUnitOfWork.Setup(u => u.ProviderPatientRepository).Returns(_mockProviderPatientRepository.Object);

            // Create command handler with mocked dependencies
            _commandHandler = new ProviderPatientCommandHandler(
                _mockConfiguration.Object,
                _mockUnitOfWork.Object,
                _mockLogger.Object
            );
        }

        [Test]
        public async Task AddProviderPatient_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var providerPatients = new List<ProviderPatient>
            {
                new ProviderPatient { Id = Guid.NewGuid(), SSN = "***********", PCPId = Guid.NewGuid(), Username = "user1" },
                new ProviderPatient { Id = Guid.NewGuid(), SSN = "***********", PCPId = Guid.NewGuid(), Username = "user2" }
            };

            _mockProviderPatientRepository.Setup(r => r.AddAsync(providerPatients));
            _mockUnitOfWork.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _commandHandler.AddProviderPatient(providerPatients);

            // Assert
            _mockProviderPatientRepository.Verify(r => r.AddAsync(providerPatients), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateProviderPatient_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var providerPatient = new ProviderPatient { Id = Guid.NewGuid(), SSN = "***********", PCPId = Guid.NewGuid(), Username = "testuser" };

            _mockProviderPatientRepository.Setup(r => r.UpdateAsync(providerPatient));
            _mockUnitOfWork.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _commandHandler.UpdateProviderPatient(providerPatient);

            // Assert
            _mockProviderPatientRepository.Verify(r => r.UpdateAsync(providerPatient), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteProviderPatientById_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var providerPatientId = Guid.NewGuid();

            _mockProviderPatientRepository.Setup(r => r.DeleteByIdAsync(providerPatientId));
            _mockUnitOfWork.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _commandHandler.DeleteProviderPatientById(providerPatientId);

            // Assert
            _mockProviderPatientRepository.Verify(r => r.DeleteByIdAsync(providerPatientId), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteProviderPatientByEntity_ShouldCallRepositoryAndSave()
        {
            // Arrange
            var providerPatient = new ProviderPatient { Id = Guid.NewGuid(), SSN = "***********", PCPId = Guid.NewGuid(), Username = "deleteuser" };

            _mockProviderPatientRepository.Setup(r => r.DeleteByEntityAsync(providerPatient));
            _mockUnitOfWork.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _commandHandler.DeleteProviderPatientByEntity(providerPatient);

            // Assert
            _mockProviderPatientRepository.Verify(r => r.DeleteByEntityAsync(providerPatient), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task AddProviderPatient_WithEmptyList_ShouldStillCallRepository()
        {
            // Arrange
            var emptyList = new List<ProviderPatient>();

            _mockProviderPatientRepository.Setup(r => r.AddAsync(emptyList));
            _mockUnitOfWork.Setup(u => u.SaveAsync())
                .ReturnsAsync(0);

            // Act
            await _commandHandler.AddProviderPatient(emptyList);

            // Assert
            _mockProviderPatientRepository.Verify(r => r.AddAsync(emptyList), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateProviderPatient_WithNullId_ShouldStillCallRepository()
        {
            // Arrange
            var providerPatient = new ProviderPatient { SSN = "***********", Username = "nulliduser" };

            _mockProviderPatientRepository.Setup(r => r.UpdateAsync(providerPatient));
            _mockUnitOfWork.Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _commandHandler.UpdateProviderPatient(providerPatient);

            // Assert
            _mockProviderPatientRepository.Verify(r => r.UpdateAsync(providerPatient), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }
    }
}
