@page "/GynecologyHistory"
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize]
@using Syncfusion.Blazor.DropDowns
@using Syncfusion.Blazor.Inputs
@using TeyaWebApp.Model
@inject IGynHistoryService GynHistoryService
@inject ISnackbar Snackbar
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.RichTextEditor
@using MudBlazor
@inject IStringLocalizer<TeyaAIScribeStrings> Localizer
@using TeyaWebApp.Components.Layout
@using TeyaWebApp.TeyaAIScribeResources
@inject HttpClient Http

<SfRichTextEditor @ref="richTextEditor" Value="@richTextContent" ValueChanged="@((string newValue)=>HandelRichTextChange(newValue))">
    <RichTextEditorToolbarSettings Items="@GetToolbarItems()">
        <RichTextEditorCustomToolbarItems>
            <RichTextEditorCustomToolbarItem Name="add">
                <Template>
                    <MudIconButton Icon="@Icons.Material.Filled.ModeEditOutline" @onclick="@(() => OpenBrowsePopup())" />
                </Template>
            </RichTextEditorCustomToolbarItem>
        </RichTextEditorCustomToolbarItems>
    </RichTextEditorToolbarSettings>
</SfRichTextEditor>

<MudDialog @ref="showBrowsePopup" Style="width: 70vw; max-width: 900px;" OnBackdropClick="HandleBackdropClick">
    <DialogContent>
        <div style="margin: -12px; display: flex; flex-direction: column;">
            <div style="padding: 16px 24px; display: flex; justify-content: space-between; align-items: center; border-bottom: 1px solid #E0E0E0;">
                <MudText Typo="Typo.h6" Style="font-size: 1rem; font-weight: 600;">
                    @Localizer["GynecologyHistory"]
                </MudText>
                <MudIconButton Icon="@Icons.Material.Filled.Close" Size="Size.Small" OnClick="CancelChanges" Style="margin: -4px;" />
            </div>

            <div style="padding: 20px; flex-grow: 1; background-color: #ffffff;">


                <SfGrid @ref="GynHistoryGrid" TValue="GynHistoryDTO" GridLines="GridLine.Both" Style="font-size: 0.85rem; margin-top: 24px;" Toolbar="@(new List<string>(){Localizer["Add"]})" DataSource="@gynHistories" AllowPaging="true">
                    <GridEditSettings AllowAdding="true" AllowEditing="true" AllowDeleting="true" AllowEditOnDblClick="true"></GridEditSettings>
                    <GridPageSettings PageSize="10"></GridPageSettings>
                    <GridEvents OnActionComplete="ActionCompletedHandler"
                                OnActionBegin="OnActionBeginHandler"
                                TValue="GynHistoryDTO">
                    </GridEvents>
                    <GridColumns>
                        <GridColumn Field="gynId" IsPrimaryKey="true" Visible="false"></GridColumn>
                        <GridColumn Field="Symptoms"
                                    HeaderText="@Localizer["Symptoms"]"
                                    Width="150"
                                    TextAlign="TextAlign.Center"
                                    ValidationRules="@(new Syncfusion.Blazor.Grids.ValidationRules { Required = true })">
                        </GridColumn>
                        <GridColumn Field="Notes" HeaderText="@Localizer["Notes"]" TextAlign="TextAlign.Center" Width="200" ValidationRules="@(new Syncfusion.Blazor.Grids.ValidationRules { Required = true })"></GridColumn>
                        <GridColumn Field="DateOfHistory" HeaderText="@Localizer["Date"]" Width="60"
                                    TextAlign="TextAlign.Center" Format="MM/dd/yy">
                            
                        </GridColumn>
                        <GridColumn HeaderText="@Localizer["Actions"]" TextAlign="TextAlign.Center" Width="80">
                            <GridCommandColumns>
                                <GridCommandColumn Type="CommandButtonType.Delete" ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-delete", CssClass = "e-flat" })" />
                            </GridCommandColumns>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>
            </div>

            <div style="display: flex; justify-content: flex-end; gap: 12px; padding: 16px 24px; border-top: 1px solid #E0E0E0;">
                <MudButton Color="Color.Secondary"
                           Variant="Variant.Outlined"
                           Dense="true"
                           OnClick="CancelChanges"
                           Style="min-width: 120px; height: 40px; font-weight: 600;">
                    @Localizer["Cancel"]
                </MudButton>
                <MudButton Color="Color.Primary"
                           Variant="Variant.Filled"
                           OnClick="SaveChanges"
                           Dense="true"
                           Style="min-width: 120px; height: 40px; font-weight: 600;">
                    @Localizer["Save"]
                </MudButton>
            </div>
        </div>
    </DialogContent>
</MudDialog>
