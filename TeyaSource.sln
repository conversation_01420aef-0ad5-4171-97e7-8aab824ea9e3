﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.8.34330.188
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Clients", "Clients", "{7F9051A1-9FBA-4E99-BBF6-5F8207CAB3D1}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Services", "Services", "{F096E0E2-AE29-4E42-AF4F-2576B447238E}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Database", "Database", "{693BA719-2BF2-43CB-823E-DA36820ABA38}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TeyaWebApp", "Clients\TeyaWebClient\TeyaWebApp\TeyaWebApp\TeyaWebApp.csproj", "{92581D0B-6C3C-42EA-8BBF-C3522AD7F1A0}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TeyaWebApp.Client", "Clients\TeyaWebClient\TeyaWebApp\TeyaWebApp.Client\TeyaWebApp.Client.csproj", "{2AB2D317-FE76-49A0-89D7-00E1B1EEABDF}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Appointments", "Services\AppointmentsApi\Appointments.csproj", "{C48E1AD0-3112-42CB-8D8B-8B037A4460D1}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "EncounterNotesService", "Services\EncounterNotesApi\EncounterNotesService.csproj", "{3B619D04-ED50-4D2C-A7EC-2107C7A2D6FE}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MemberServiceApi", "Services\MemberServiceApi\MemberServiceApi.csproj", "{AFAC4305-9F1E-4165-9458-01D819B1F87C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PracticeApi", "Services\PracticeApi\PracticeApi.csproj", "{D2F0CAA4-6EDB-4D51-9BE1-16B7AE84B51A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AppointmentsBusinessLayer", "Services\AppointmentsApi\BusinessLayer\AppointmentsBusinessLayer.csproj", "{B2D583D0-BF92-41F5-8D6B-560B7D605749}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AppointmentContracts", "Services\AppointmentsApi\Contracts\AppointmentContracts.csproj", "{A97C3BD7-3050-4C1F-BF97-099BAA155BD9}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AppointmentDataAccessLayer", "Services\AppointmentsApi\DataAccessLayer\AppointmentDataAccessLayer.csproj", "{6CCAF146-47DE-4CFC-ABE3-64000F6F07EE}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "EncounterNotesBusinessLayer", "Services\EncounterNotesApi\BusinessLayer\EncounterNotesBusinessLayer.csproj", "{916B564C-5D3E-4BC4-B4D1-D3593563D0DD}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "EncounterNotesDataAccessLayer", "Services\EncounterNotesApi\DataAccessLayer\EncounterNotesDataAccessLayer.csproj", "{0970DD64-4320-4510-87B9-B969AA656C0A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "AppointmentService", "AppointmentService", "{FBA38A5A-CFC9-466E-9C30-F48BE88F8678}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "UnitTest", "UnitTest", "{15E2B3C7-C7F5-4997-BAC4-943A51C74259}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "EncounterService", "EncounterService", "{D071310F-44FB-43B1-A90F-8EAF203B5F73}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "UnitTest", "UnitTest", "{99F3ED42-8796-4D74-9F43-5FD3B6C3259A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "MemberService", "MemberService", "{FF69A0CE-FD05-4BCF-AF49-530A996AD153}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "UnitTest", "UnitTest", "{51BCA120-1F40-427C-AAC9-060C0D0A1EF5}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MemberServiceBusinessLayer", "Services\MemberServiceApi\BusinessLayer\MemberServiceBusinessLayer.csproj", "{0DEE2DFA-058B-4649-837A-9FEC470E8044}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MemberServiceDataAccessLayer", "Services\MemberServiceApi\DataAccessLayer\MemberServiceDataAccessLayer.csproj", "{8114711B-7A07-48B3-B337-52014F1CA5FE}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "PracticeService", "PracticeService", "{2B119102-3734-4406-A301-4778546F17A8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PracticeBusinessLayer", "Services\PracticeApi\PracticeBusinessLayer\PracticeBusinessLayer.csproj", "{054E81E1-5ABE-4081-A14A-102CD789AF5D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PracticeDataAccessLayer", "Services\PracticeApi\PracticeDataAccessLayer\PracticeDataAccessLayer.csproj", "{6CE40C62-A666-4183-AB5F-F2AF8A49CE77}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "PracticeContracts", "Services\PracticeApi\PracticeContracts\PracticeContracts.csproj", "{1CA4EA9F-A8C2-46BB-8018-2DD8B037DDA6}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Contracts", "Services\MemberServiceApi\Contracts\Contracts.csproj", "{05D62A1B-05F9-4A44-A6A0-543893220654}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "EncounterNotesContracts", "Services\EncounterNotesApi\Contracts\EncounterNotesContracts.csproj", "{8598DB8F-A790-4FD8-A646-400BA18FFAB9}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TeyaUIViewModels", "Clients\TeyaWebClient\TeyaWebApp\TeyaWebApp\TeyaUIViewModels\TeyaUIViewModels.csproj", "{F961FC2C-6861-419B-BBF9-28B0042B97F2}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TeyaUIModels", "Clients\TeyaWebClient\TeyaWebApp\TeyaWebApp\TeyaUIModels\TeyaUIModels.csproj", "{5747E0B4-87EE-4ED7-85DD-372412EB787F}"
EndProject
Project("{00D1A9C2-B5F0-4AF3-8072-F6C62B433612}") = "TeyaHealthSQLDatabase", "Database\TeyaHealthSQLDatabase\TeyaHealthSQLDatabase.sqlproj", "{0D466A75-5329-4633-A5FB-00360A417804}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AlertsApi", "Services\AlertsApi\AlertsApi.csproj", "{B5CFB729-2C42-2B88-32B4-D8F9E0B53FE8}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AlertsBusinessLayer", "Services\AlertsApi\AlertsBusinessLayer\AlertsBusinessLayer.csproj", "{8DCAFBC6-DF7C-454C-B2CE-1BB5CF3A3010}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AlertsDataAccessLayer", "Services\AlertsApi\AlertsDataAccessLayer\AlertsDataAccessLayer.csproj", "{0785D6C4-2745-489B-AEE9-8595DA3BC61F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AlertsContracts", "Services\AlertsApi\AlertsContracts\AlertsContracts.csproj", "{FEDAAE7E-C707-4236-9092-B0269C29526A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "AlertsService", "AlertsService", "{A5C0B424-394D-4903-81F3-DC8035D479DF}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "UnitTest", "UnitTest", "{B5C0B424-394D-4903-81F3-DC8035D479DF}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AlertsAPIBussinessLayerTestCases", "Services\AlertsApi\AlertsAPIBussinessLayerTestCases\AlertsAPIBussinessLayerTestCases.csproj", "{9B87DEE8-AD1D-42F2-ABB4-B01D1D3F5815}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AlertsAPIControllerTests", "Services\AlertsApi\AlertsAPIControllerTests\AlertsAPIControllerTests.csproj", "{E2921D86-9097-467A-AC9F-987E8294EA46}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Infrastructure", "Infrastructure", "{3D9C058B-6A4B-4645-9F34-EFD3062064F0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ShardInterfaces", "Infrastructure\ShardInterfaces\ShardInterfaces.csproj", "{5480F4A1-C95E-441D-AC8B-FA45133F8938}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ShardModels", "Infrastructure\ShardModels\ShardModels.csproj", "{2DD39983-7686-409E-A91B-A74BAEAA0544}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MemberServiceApiBusinessLayerTestCases", "Services\MemberServiceApi\MemberServiceApiBusinessLayerTestCases\MemberServiceApiBusinessLayerTestCases.csproj", "{BFD4EB99-8884-4938-8089-E81ED9201CAA}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MemberServiceApiControllerTestCases", "Services\MemberServiceApi\MemberServiceApiControllerTestCases\MemberServiceApiControllerTestCases.csproj", "{890A43EC-1E14-4DE5-A792-50F6ADF35F0B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AppointmentApiBusinessLayerTestCases", "Services\AppointmentsApi\TestCases\AppointmentApiBusinessLayerTestCases\AppointmentApiBusinessLayerTestCases.csproj", "{ADCD8E7B-73DA-4DB5-A1AA-F0B9CE22EA79}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AppointmentApiDataAccessLayerTestCases", "Services\AppointmentsApi\TestCases\AppointmentApiDataAccessLayerTestCases\AppointmentApiDataAccessLayerTestCases.csproj", "{4F191EA8-2F23-47F2-8ECD-2FF46B742DDF}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AppointmentsApiControllerTestCases", "Services\AppointmentsApi\TestCases\AppointmentsApiControllerTestCases\AppointmentsApiControllerTestCases.csproj", "{41320D13-FEA5-4CFF-969C-D9F466D59C03}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "TestCases", "TestCases", "{3459BA79-06E7-4CC1-8D8A-7D276D5FF83D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PracticeApiBusinessLayerTestCases", "Services\PracticeApi\TestCases\PracticeApiBusinessLayerTestCases\PracticeApiBusinessLayerTestCases.csproj", "{E13AB6A8-DDFA-4DA4-861E-536285AE4AD3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PracticeApiControllerTestCases", "Services\PracticeApi\TestCases\PracticeApiControllerTestCases\PracticeApiControllerTestCases.csproj", "{39406097-F04F-4B02-8C4E-6E3602EEB1D9}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PracticeApiDataAccessLayerTestCases", "Services\PracticeApi\TestCases\PracticeApiDataAccessLayerTestCases\PracticeApiDataAccessLayerTestCases.csproj", "{6F395D56-6569-48F7-AFC7-1C5F0B74B469}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "EncounterNotesApiBusinessLayerUnitTests", "Services\EncounterNotesApi\EncounterNotesApiBusinessLayerUnitTests\EncounterNotesApiBusinessLayerUnitTests.csproj", "{46B39C4A-665F-4A8E-B86E-E43D5F234FD6}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "EncounterNotesApiControllerTestCases", "Services\EncounterNotesApi\EncounterNotesApiControllerTestCases\EncounterNotesApiControllerTestCases.csproj", "{3F31687B-3123-42B8-9848-77D732355555}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MemberServiceApiDataAccessLayerTestCases", "Services\MemberServiceApi\MemberServiceApiDataAccessLayerTestCases\MemberServiceApiDataAccessLayerTestCases.csproj", "{B8FE2FC3-BBEB-42E6-A1B4-00549D41CEDA}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "EncounterNotesApiDataAccessLayerTestCases", "Services\EncounterNotesApi\EncounterNotesApiDataAccessLayerTestCases\EncounterNotesApiDataAccessLayerTestCases.csproj", "{585223D4-FA0A-4C19-9F54-0A7A042DB24C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AlertsApiDataAccessLayerTestCases", "Services\AlertsApi\AlertsApiDataAccessLayerTestCases\AlertsApiDataAccessLayerTestCases.csproj", "{06DF6773-D9B7-4E8C-9DA8-CC073DD8D57E}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "TeyaWebClientTestCases", "TeyaWebClientTestCases", "{9B95D17F-A118-4E85-896F-52BA433E243F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TeyaWebClientUIViewModelTestCases", "Clients\TeyaWebClient\Testcases\TeyaWebClientUIViewModelTestCases\TeyaWebClientUIViewModelTestCases.csproj", "{1D05219C-2620-42F3-9DA9-D1E2A9F5FDFC}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TeyaWebClientViewModelTestCases", "Clients\TeyaWebClient\Testcases\TeyaWebClientViewModelTestCases\TeyaWebClientViewModelTestCases.csproj", "{B0F8D5C8-6DE5-4F2B-8442-F9A0D0EF30C7}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{92581D0B-6C3C-42EA-8BBF-C3522AD7F1A0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{92581D0B-6C3C-42EA-8BBF-C3522AD7F1A0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{92581D0B-6C3C-42EA-8BBF-C3522AD7F1A0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{92581D0B-6C3C-42EA-8BBF-C3522AD7F1A0}.Release|Any CPU.Build.0 = Release|Any CPU
		{2AB2D317-FE76-49A0-89D7-00E1B1EEABDF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2AB2D317-FE76-49A0-89D7-00E1B1EEABDF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2AB2D317-FE76-49A0-89D7-00E1B1EEABDF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2AB2D317-FE76-49A0-89D7-00E1B1EEABDF}.Release|Any CPU.Build.0 = Release|Any CPU
		{C48E1AD0-3112-42CB-8D8B-8B037A4460D1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C48E1AD0-3112-42CB-8D8B-8B037A4460D1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C48E1AD0-3112-42CB-8D8B-8B037A4460D1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C48E1AD0-3112-42CB-8D8B-8B037A4460D1}.Release|Any CPU.Build.0 = Release|Any CPU
		{3B619D04-ED50-4D2C-A7EC-2107C7A2D6FE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3B619D04-ED50-4D2C-A7EC-2107C7A2D6FE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3B619D04-ED50-4D2C-A7EC-2107C7A2D6FE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3B619D04-ED50-4D2C-A7EC-2107C7A2D6FE}.Release|Any CPU.Build.0 = Release|Any CPU
		{AFAC4305-9F1E-4165-9458-01D819B1F87C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AFAC4305-9F1E-4165-9458-01D819B1F87C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AFAC4305-9F1E-4165-9458-01D819B1F87C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AFAC4305-9F1E-4165-9458-01D819B1F87C}.Release|Any CPU.Build.0 = Release|Any CPU
		{D2F0CAA4-6EDB-4D51-9BE1-16B7AE84B51A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D2F0CAA4-6EDB-4D51-9BE1-16B7AE84B51A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D2F0CAA4-6EDB-4D51-9BE1-16B7AE84B51A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D2F0CAA4-6EDB-4D51-9BE1-16B7AE84B51A}.Release|Any CPU.Build.0 = Release|Any CPU
		{B2D583D0-BF92-41F5-8D6B-560B7D605749}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B2D583D0-BF92-41F5-8D6B-560B7D605749}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B2D583D0-BF92-41F5-8D6B-560B7D605749}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B2D583D0-BF92-41F5-8D6B-560B7D605749}.Release|Any CPU.Build.0 = Release|Any CPU
		{A97C3BD7-3050-4C1F-BF97-099BAA155BD9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A97C3BD7-3050-4C1F-BF97-099BAA155BD9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A97C3BD7-3050-4C1F-BF97-099BAA155BD9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A97C3BD7-3050-4C1F-BF97-099BAA155BD9}.Release|Any CPU.Build.0 = Release|Any CPU
		{6CCAF146-47DE-4CFC-ABE3-64000F6F07EE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6CCAF146-47DE-4CFC-ABE3-64000F6F07EE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6CCAF146-47DE-4CFC-ABE3-64000F6F07EE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6CCAF146-47DE-4CFC-ABE3-64000F6F07EE}.Release|Any CPU.Build.0 = Release|Any CPU
		{916B564C-5D3E-4BC4-B4D1-D3593563D0DD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{916B564C-5D3E-4BC4-B4D1-D3593563D0DD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{916B564C-5D3E-4BC4-B4D1-D3593563D0DD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{916B564C-5D3E-4BC4-B4D1-D3593563D0DD}.Release|Any CPU.Build.0 = Release|Any CPU
		{0970DD64-4320-4510-87B9-B969AA656C0A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0970DD64-4320-4510-87B9-B969AA656C0A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0970DD64-4320-4510-87B9-B969AA656C0A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0970DD64-4320-4510-87B9-B969AA656C0A}.Release|Any CPU.Build.0 = Release|Any CPU
		{0DEE2DFA-058B-4649-837A-9FEC470E8044}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0DEE2DFA-058B-4649-837A-9FEC470E8044}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0DEE2DFA-058B-4649-837A-9FEC470E8044}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0DEE2DFA-058B-4649-837A-9FEC470E8044}.Release|Any CPU.Build.0 = Release|Any CPU
		{8114711B-7A07-48B3-B337-52014F1CA5FE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8114711B-7A07-48B3-B337-52014F1CA5FE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8114711B-7A07-48B3-B337-52014F1CA5FE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8114711B-7A07-48B3-B337-52014F1CA5FE}.Release|Any CPU.Build.0 = Release|Any CPU
		{054E81E1-5ABE-4081-A14A-102CD789AF5D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{054E81E1-5ABE-4081-A14A-102CD789AF5D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{054E81E1-5ABE-4081-A14A-102CD789AF5D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{054E81E1-5ABE-4081-A14A-102CD789AF5D}.Release|Any CPU.Build.0 = Release|Any CPU
		{6CE40C62-A666-4183-AB5F-F2AF8A49CE77}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6CE40C62-A666-4183-AB5F-F2AF8A49CE77}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6CE40C62-A666-4183-AB5F-F2AF8A49CE77}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6CE40C62-A666-4183-AB5F-F2AF8A49CE77}.Release|Any CPU.Build.0 = Release|Any CPU
		{1CA4EA9F-A8C2-46BB-8018-2DD8B037DDA6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1CA4EA9F-A8C2-46BB-8018-2DD8B037DDA6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1CA4EA9F-A8C2-46BB-8018-2DD8B037DDA6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1CA4EA9F-A8C2-46BB-8018-2DD8B037DDA6}.Release|Any CPU.Build.0 = Release|Any CPU
		{05D62A1B-05F9-4A44-A6A0-543893220654}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{05D62A1B-05F9-4A44-A6A0-543893220654}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{05D62A1B-05F9-4A44-A6A0-543893220654}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{05D62A1B-05F9-4A44-A6A0-543893220654}.Release|Any CPU.Build.0 = Release|Any CPU
		{8598DB8F-A790-4FD8-A646-400BA18FFAB9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8598DB8F-A790-4FD8-A646-400BA18FFAB9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8598DB8F-A790-4FD8-A646-400BA18FFAB9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8598DB8F-A790-4FD8-A646-400BA18FFAB9}.Release|Any CPU.Build.0 = Release|Any CPU
		{F961FC2C-6861-419B-BBF9-28B0042B97F2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F961FC2C-6861-419B-BBF9-28B0042B97F2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F961FC2C-6861-419B-BBF9-28B0042B97F2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F961FC2C-6861-419B-BBF9-28B0042B97F2}.Release|Any CPU.Build.0 = Release|Any CPU
		{5747E0B4-87EE-4ED7-85DD-372412EB787F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5747E0B4-87EE-4ED7-85DD-372412EB787F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5747E0B4-87EE-4ED7-85DD-372412EB787F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5747E0B4-87EE-4ED7-85DD-372412EB787F}.Release|Any CPU.Build.0 = Release|Any CPU
		{0D466A75-5329-4633-A5FB-00360A417804}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0D466A75-5329-4633-A5FB-00360A417804}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0D466A75-5329-4633-A5FB-00360A417804}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0D466A75-5329-4633-A5FB-00360A417804}.Release|Any CPU.Build.0 = Release|Any CPU
		{B5CFB729-2C42-2B88-32B4-D8F9E0B53FE8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B5CFB729-2C42-2B88-32B4-D8F9E0B53FE8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B5CFB729-2C42-2B88-32B4-D8F9E0B53FE8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B5CFB729-2C42-2B88-32B4-D8F9E0B53FE8}.Release|Any CPU.Build.0 = Release|Any CPU
		{8DCAFBC6-DF7C-454C-B2CE-1BB5CF3A3010}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8DCAFBC6-DF7C-454C-B2CE-1BB5CF3A3010}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8DCAFBC6-DF7C-454C-B2CE-1BB5CF3A3010}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8DCAFBC6-DF7C-454C-B2CE-1BB5CF3A3010}.Release|Any CPU.Build.0 = Release|Any CPU
		{0785D6C4-2745-489B-AEE9-8595DA3BC61F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0785D6C4-2745-489B-AEE9-8595DA3BC61F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0785D6C4-2745-489B-AEE9-8595DA3BC61F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0785D6C4-2745-489B-AEE9-8595DA3BC61F}.Release|Any CPU.Build.0 = Release|Any CPU
		{FEDAAE7E-C707-4236-9092-B0269C29526A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FEDAAE7E-C707-4236-9092-B0269C29526A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FEDAAE7E-C707-4236-9092-B0269C29526A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FEDAAE7E-C707-4236-9092-B0269C29526A}.Release|Any CPU.Build.0 = Release|Any CPU
		{9B87DEE8-AD1D-42F2-ABB4-B01D1D3F5815}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9B87DEE8-AD1D-42F2-ABB4-B01D1D3F5815}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9B87DEE8-AD1D-42F2-ABB4-B01D1D3F5815}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9B87DEE8-AD1D-42F2-ABB4-B01D1D3F5815}.Release|Any CPU.Build.0 = Release|Any CPU
		{E2921D86-9097-467A-AC9F-987E8294EA46}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E2921D86-9097-467A-AC9F-987E8294EA46}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E2921D86-9097-467A-AC9F-987E8294EA46}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E2921D86-9097-467A-AC9F-987E8294EA46}.Release|Any CPU.Build.0 = Release|Any CPU
		{5480F4A1-C95E-441D-AC8B-FA45133F8938}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5480F4A1-C95E-441D-AC8B-FA45133F8938}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5480F4A1-C95E-441D-AC8B-FA45133F8938}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5480F4A1-C95E-441D-AC8B-FA45133F8938}.Release|Any CPU.Build.0 = Release|Any CPU
		{2DD39983-7686-409E-A91B-A74BAEAA0544}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2DD39983-7686-409E-A91B-A74BAEAA0544}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2DD39983-7686-409E-A91B-A74BAEAA0544}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2DD39983-7686-409E-A91B-A74BAEAA0544}.Release|Any CPU.Build.0 = Release|Any CPU
		{BFD4EB99-8884-4938-8089-E81ED9201CAA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BFD4EB99-8884-4938-8089-E81ED9201CAA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BFD4EB99-8884-4938-8089-E81ED9201CAA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BFD4EB99-8884-4938-8089-E81ED9201CAA}.Release|Any CPU.Build.0 = Release|Any CPU
		{890A43EC-1E14-4DE5-A792-50F6ADF35F0B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{890A43EC-1E14-4DE5-A792-50F6ADF35F0B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{890A43EC-1E14-4DE5-A792-50F6ADF35F0B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{890A43EC-1E14-4DE5-A792-50F6ADF35F0B}.Release|Any CPU.Build.0 = Release|Any CPU
		{ADCD8E7B-73DA-4DB5-A1AA-F0B9CE22EA79}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{ADCD8E7B-73DA-4DB5-A1AA-F0B9CE22EA79}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{ADCD8E7B-73DA-4DB5-A1AA-F0B9CE22EA79}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{ADCD8E7B-73DA-4DB5-A1AA-F0B9CE22EA79}.Release|Any CPU.Build.0 = Release|Any CPU
		{4F191EA8-2F23-47F2-8ECD-2FF46B742DDF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4F191EA8-2F23-47F2-8ECD-2FF46B742DDF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4F191EA8-2F23-47F2-8ECD-2FF46B742DDF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4F191EA8-2F23-47F2-8ECD-2FF46B742DDF}.Release|Any CPU.Build.0 = Release|Any CPU
		{41320D13-FEA5-4CFF-969C-D9F466D59C03}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{41320D13-FEA5-4CFF-969C-D9F466D59C03}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{41320D13-FEA5-4CFF-969C-D9F466D59C03}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{41320D13-FEA5-4CFF-969C-D9F466D59C03}.Release|Any CPU.Build.0 = Release|Any CPU
		{E13AB6A8-DDFA-4DA4-861E-536285AE4AD3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E13AB6A8-DDFA-4DA4-861E-536285AE4AD3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E13AB6A8-DDFA-4DA4-861E-536285AE4AD3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E13AB6A8-DDFA-4DA4-861E-536285AE4AD3}.Release|Any CPU.Build.0 = Release|Any CPU
		{39406097-F04F-4B02-8C4E-6E3602EEB1D9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{39406097-F04F-4B02-8C4E-6E3602EEB1D9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{39406097-F04F-4B02-8C4E-6E3602EEB1D9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{39406097-F04F-4B02-8C4E-6E3602EEB1D9}.Release|Any CPU.Build.0 = Release|Any CPU
		{6F395D56-6569-48F7-AFC7-1C5F0B74B469}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6F395D56-6569-48F7-AFC7-1C5F0B74B469}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6F395D56-6569-48F7-AFC7-1C5F0B74B469}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6F395D56-6569-48F7-AFC7-1C5F0B74B469}.Release|Any CPU.Build.0 = Release|Any CPU
		{46B39C4A-665F-4A8E-B86E-E43D5F234FD6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{46B39C4A-665F-4A8E-B86E-E43D5F234FD6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{46B39C4A-665F-4A8E-B86E-E43D5F234FD6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{46B39C4A-665F-4A8E-B86E-E43D5F234FD6}.Release|Any CPU.Build.0 = Release|Any CPU
		{3F31687B-3123-42B8-9848-77D732355555}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3F31687B-3123-42B8-9848-77D732355555}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3F31687B-3123-42B8-9848-77D732355555}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3F31687B-3123-42B8-9848-77D732355555}.Release|Any CPU.Build.0 = Release|Any CPU
		{B8FE2FC3-BBEB-42E6-A1B4-00549D41CEDA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B8FE2FC3-BBEB-42E6-A1B4-00549D41CEDA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B8FE2FC3-BBEB-42E6-A1B4-00549D41CEDA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B8FE2FC3-BBEB-42E6-A1B4-00549D41CEDA}.Release|Any CPU.Build.0 = Release|Any CPU
		{585223D4-FA0A-4C19-9F54-0A7A042DB24C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{585223D4-FA0A-4C19-9F54-0A7A042DB24C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{585223D4-FA0A-4C19-9F54-0A7A042DB24C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{585223D4-FA0A-4C19-9F54-0A7A042DB24C}.Release|Any CPU.Build.0 = Release|Any CPU
		{06DF6773-D9B7-4E8C-9DA8-CC073DD8D57E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{06DF6773-D9B7-4E8C-9DA8-CC073DD8D57E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{06DF6773-D9B7-4E8C-9DA8-CC073DD8D57E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{06DF6773-D9B7-4E8C-9DA8-CC073DD8D57E}.Release|Any CPU.Build.0 = Release|Any CPU
		{1D05219C-2620-42F3-9DA9-D1E2A9F5FDFC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1D05219C-2620-42F3-9DA9-D1E2A9F5FDFC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1D05219C-2620-42F3-9DA9-D1E2A9F5FDFC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1D05219C-2620-42F3-9DA9-D1E2A9F5FDFC}.Release|Any CPU.Build.0 = Release|Any CPU
		{B0F8D5C8-6DE5-4F2B-8442-F9A0D0EF30C7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B0F8D5C8-6DE5-4F2B-8442-F9A0D0EF30C7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B0F8D5C8-6DE5-4F2B-8442-F9A0D0EF30C7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B0F8D5C8-6DE5-4F2B-8442-F9A0D0EF30C7}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{92581D0B-6C3C-42EA-8BBF-C3522AD7F1A0} = {7F9051A1-9FBA-4E99-BBF6-5F8207CAB3D1}
		{2AB2D317-FE76-49A0-89D7-00E1B1EEABDF} = {7F9051A1-9FBA-4E99-BBF6-5F8207CAB3D1}
		{C48E1AD0-3112-42CB-8D8B-8B037A4460D1} = {FBA38A5A-CFC9-466E-9C30-F48BE88F8678}
		{3B619D04-ED50-4D2C-A7EC-2107C7A2D6FE} = {D071310F-44FB-43B1-A90F-8EAF203B5F73}
		{AFAC4305-9F1E-4165-9458-01D819B1F87C} = {FF69A0CE-FD05-4BCF-AF49-530A996AD153}
		{D2F0CAA4-6EDB-4D51-9BE1-16B7AE84B51A} = {2B119102-3734-4406-A301-4778546F17A8}
		{B2D583D0-BF92-41F5-8D6B-560B7D605749} = {FBA38A5A-CFC9-466E-9C30-F48BE88F8678}
		{A97C3BD7-3050-4C1F-BF97-099BAA155BD9} = {FBA38A5A-CFC9-466E-9C30-F48BE88F8678}
		{6CCAF146-47DE-4CFC-ABE3-64000F6F07EE} = {FBA38A5A-CFC9-466E-9C30-F48BE88F8678}
		{916B564C-5D3E-4BC4-B4D1-D3593563D0DD} = {D071310F-44FB-43B1-A90F-8EAF203B5F73}
		{0970DD64-4320-4510-87B9-B969AA656C0A} = {D071310F-44FB-43B1-A90F-8EAF203B5F73}
		{FBA38A5A-CFC9-466E-9C30-F48BE88F8678} = {F096E0E2-AE29-4E42-AF4F-2576B447238E}
		{15E2B3C7-C7F5-4997-BAC4-943A51C74259} = {FBA38A5A-CFC9-466E-9C30-F48BE88F8678}
		{D071310F-44FB-43B1-A90F-8EAF203B5F73} = {F096E0E2-AE29-4E42-AF4F-2576B447238E}
		{99F3ED42-8796-4D74-9F43-5FD3B6C3259A} = {D071310F-44FB-43B1-A90F-8EAF203B5F73}
		{FF69A0CE-FD05-4BCF-AF49-530A996AD153} = {F096E0E2-AE29-4E42-AF4F-2576B447238E}
		{51BCA120-1F40-427C-AAC9-060C0D0A1EF5} = {FF69A0CE-FD05-4BCF-AF49-530A996AD153}
		{0DEE2DFA-058B-4649-837A-9FEC470E8044} = {FF69A0CE-FD05-4BCF-AF49-530A996AD153}
		{8114711B-7A07-48B3-B337-52014F1CA5FE} = {FF69A0CE-FD05-4BCF-AF49-530A996AD153}
		{2B119102-3734-4406-A301-4778546F17A8} = {F096E0E2-AE29-4E42-AF4F-2576B447238E}
		{054E81E1-5ABE-4081-A14A-102CD789AF5D} = {2B119102-3734-4406-A301-4778546F17A8}
		{6CE40C62-A666-4183-AB5F-F2AF8A49CE77} = {2B119102-3734-4406-A301-4778546F17A8}
		{1CA4EA9F-A8C2-46BB-8018-2DD8B037DDA6} = {2B119102-3734-4406-A301-4778546F17A8}
		{05D62A1B-05F9-4A44-A6A0-543893220654} = {FF69A0CE-FD05-4BCF-AF49-530A996AD153}
		{8598DB8F-A790-4FD8-A646-400BA18FFAB9} = {D071310F-44FB-43B1-A90F-8EAF203B5F73}
		{F961FC2C-6861-419B-BBF9-28B0042B97F2} = {7F9051A1-9FBA-4E99-BBF6-5F8207CAB3D1}
		{5747E0B4-87EE-4ED7-85DD-372412EB787F} = {7F9051A1-9FBA-4E99-BBF6-5F8207CAB3D1}
		{0D466A75-5329-4633-A5FB-00360A417804} = {693BA719-2BF2-43CB-823E-DA36820ABA38}
		{B5CFB729-2C42-2B88-32B4-D8F9E0B53FE8} = {A5C0B424-394D-4903-81F3-DC8035D479DF}
		{8DCAFBC6-DF7C-454C-B2CE-1BB5CF3A3010} = {A5C0B424-394D-4903-81F3-DC8035D479DF}
		{0785D6C4-2745-489B-AEE9-8595DA3BC61F} = {A5C0B424-394D-4903-81F3-DC8035D479DF}
		{FEDAAE7E-C707-4236-9092-B0269C29526A} = {A5C0B424-394D-4903-81F3-DC8035D479DF}
		{A5C0B424-394D-4903-81F3-DC8035D479DF} = {F096E0E2-AE29-4E42-AF4F-2576B447238E}
		{B5C0B424-394D-4903-81F3-DC8035D479DF} = {A5C0B424-394D-4903-81F3-DC8035D479DF}
		{9B87DEE8-AD1D-42F2-ABB4-B01D1D3F5815} = {B5C0B424-394D-4903-81F3-DC8035D479DF}
		{E2921D86-9097-467A-AC9F-987E8294EA46} = {B5C0B424-394D-4903-81F3-DC8035D479DF}
		{5480F4A1-C95E-441D-AC8B-FA45133F8938} = {3D9C058B-6A4B-4645-9F34-EFD3062064F0}
		{2DD39983-7686-409E-A91B-A74BAEAA0544} = {3D9C058B-6A4B-4645-9F34-EFD3062064F0}
		{BFD4EB99-8884-4938-8089-E81ED9201CAA} = {51BCA120-1F40-427C-AAC9-060C0D0A1EF5}
		{890A43EC-1E14-4DE5-A792-50F6ADF35F0B} = {51BCA120-1F40-427C-AAC9-060C0D0A1EF5}
		{ADCD8E7B-73DA-4DB5-A1AA-F0B9CE22EA79} = {15E2B3C7-C7F5-4997-BAC4-943A51C74259}
		{4F191EA8-2F23-47F2-8ECD-2FF46B742DDF} = {15E2B3C7-C7F5-4997-BAC4-943A51C74259}
		{41320D13-FEA5-4CFF-969C-D9F466D59C03} = {15E2B3C7-C7F5-4997-BAC4-943A51C74259}
		{3459BA79-06E7-4CC1-8D8A-7D276D5FF83D} = {2B119102-3734-4406-A301-4778546F17A8}
		{E13AB6A8-DDFA-4DA4-861E-536285AE4AD3} = {3459BA79-06E7-4CC1-8D8A-7D276D5FF83D}
		{39406097-F04F-4B02-8C4E-6E3602EEB1D9} = {3459BA79-06E7-4CC1-8D8A-7D276D5FF83D}
		{6F395D56-6569-48F7-AFC7-1C5F0B74B469} = {3459BA79-06E7-4CC1-8D8A-7D276D5FF83D}
		{46B39C4A-665F-4A8E-B86E-E43D5F234FD6} = {99F3ED42-8796-4D74-9F43-5FD3B6C3259A}
		{3F31687B-3123-42B8-9848-77D732355555} = {99F3ED42-8796-4D74-9F43-5FD3B6C3259A}
		{B8FE2FC3-BBEB-42E6-A1B4-00549D41CEDA} = {51BCA120-1F40-427C-AAC9-060C0D0A1EF5}
		{585223D4-FA0A-4C19-9F54-0A7A042DB24C} = {99F3ED42-8796-4D74-9F43-5FD3B6C3259A}
		{06DF6773-D9B7-4E8C-9DA8-CC073DD8D57E} = {B5C0B424-394D-4903-81F3-DC8035D479DF}
		{9B95D17F-A118-4E85-896F-52BA433E243F} = {7F9051A1-9FBA-4E99-BBF6-5F8207CAB3D1}
		{1D05219C-2620-42F3-9DA9-D1E2A9F5FDFC} = {9B95D17F-A118-4E85-896F-52BA433E243F}
		{B0F8D5C8-6DE5-4F2B-8442-F9A0D0EF30C7} = {9B95D17F-A118-4E85-896F-52BA433E243F}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {6DC8759E-4E3D-4CDD-A6F8-9B93A71EA423}
	EndGlobalSection
EndGlobal
