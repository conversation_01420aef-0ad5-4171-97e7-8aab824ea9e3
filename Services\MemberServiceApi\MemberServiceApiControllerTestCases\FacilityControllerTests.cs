using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using MemberServiceApi.Controllers;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using MemberServiceBusinessLayer;
using System.Linq;

namespace MemberServiceApiControllerTestCases
{
    [TestFixture]
    public class FacilityControllerTests
    {
        private Mock<IFacilityQueryHandler<Facility>> _mockQueryHandler;
        private Mock<IFacilityCommandHandler<Facility>> _mockCommandHandler;
        private Mock<ILogger<FacilityController>> _mockLogger;
        private Mock<IStringLocalizer<FacilityController>> _mockLocalizer;
        private FacilityController _controller;

        [SetUp]
        public void Setup()
        {
            _mockQueryHandler = new Mock<IFacilityQueryHandler<Facility>>();
            _mockCommandHandler = new Mock<IFacilityCommandHandler<Facility>>();
            _mockLogger = new Mock<ILogger<FacilityController>>();
            _mockLocalizer = new Mock<IStringLocalizer<FacilityController>>();

            // Setup localizer to return the key when requested
            _mockLocalizer.Setup(l => l[It.IsAny<string>()]).Returns((string key) => new LocalizedString(key, key));

            _controller = new FacilityController(
                _mockQueryHandler.Object,
                _mockCommandHandler.Object,
                _mockLogger.Object,
                _mockLocalizer.Object
            );
        }

        [Test]
        public async Task GetAllFacilities_WhenFacilitiesExist_ReturnsOkWithFacilities()
        {
            // Arrange
            var mockFacilities = new List<Facility>
            {
                new Facility { FacilityId = Guid.NewGuid(), FacilityName = "Facility 1" },
                new Facility { FacilityId = Guid.NewGuid(), FacilityName = "Facility 2" }
            };

            _mockQueryHandler
                .Setup(q => q.GetAllFacilitiesAsync())
                .ReturnsAsync(mockFacilities);

            // Act
            var result = await _controller.GetAllFacilities();

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult?.StatusCode, Is.EqualTo(200));
            Assert.That(okResult?.Value, Is.EqualTo(mockFacilities));
        }

        [Test]
        public async Task GetAllFacilities_WhenNoFacilitiesExist_ReturnsNotFound()
        {
            // Arrange
            _mockQueryHandler
                .Setup(q => q.GetAllFacilitiesAsync())
                .ReturnsAsync(new List<Facility>());

            // Act
            var result = await _controller.GetAllFacilities();

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult?.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task GetAllFacilities_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            _mockQueryHandler
                .Setup(q => q.GetAllFacilitiesAsync())
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetAllFacilities();

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task GetFacilityById_WhenFacilityExists_ReturnsOkWithFacility()
        {
            // Arrange
            var facilityId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var mockFacility = new Facility
            {
                FacilityId = facilityId,
                FacilityName = "Test Facility",
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockQueryHandler
                .Setup(q => q.GetFacilityByIdAsync(subscription, facilityId, orgId))
                .ReturnsAsync(mockFacility);

            // Act
            var result = await _controller.GetFacilityById(facilityId, orgId, subscription);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult?.StatusCode, Is.EqualTo(200));
            Assert.That(okResult?.Value, Is.EqualTo(mockFacility));
        }

        [Test]
        public async Task GetFacilityById_WhenFacilityDoesNotExist_ReturnsNotFound()
        {
            // Arrange
            var facilityId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockQueryHandler
                .Setup(q => q.GetFacilityByIdAsync(subscription, facilityId, orgId))
                .ReturnsAsync((Facility)null);

            // Act
            var result = await _controller.GetFacilityById(facilityId, orgId, subscription);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult?.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task GetFacilityById_WhenExceptionOccurs_ThrowsException()
        {
            // Arrange
            var facilityId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockQueryHandler
                .Setup(q => q.GetFacilityByIdAsync(subscription, facilityId, orgId))
                .ThrowsAsync(new Exception("Test exception"));

            // Act & Assert
            // The controller doesn't handle exceptions, so it will throw the exception
            try
            {
                await _controller.GetFacilityById(facilityId, orgId, subscription);
                Assert.Fail("Expected exception was not thrown");
            }
            catch (Exception ex)
            {
                Assert.That(ex.Message, Is.EqualTo("Test exception"));
            }
        }

        [Test]
        public async Task GetFacilityByOrgId_WhenFacilitiesExist_ReturnsOkWithFacilities()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var mockFacilities = new List<Facility>
            {
                new Facility { FacilityId = Guid.NewGuid(), FacilityName = "Facility 1", OrganizationId = orgId },
                new Facility { FacilityId = Guid.NewGuid(), FacilityName = "Facility 2", OrganizationId = orgId }
            };

            _mockQueryHandler
                .Setup(q => q.GetFacilitiesByOrgAsync(subscription, orgId))
                .ReturnsAsync(mockFacilities);

            // Act
            var result = await _controller.GetFacilityByOrgId(orgId, subscription);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult?.StatusCode, Is.EqualTo(200));
            Assert.That(okResult?.Value, Is.EqualTo(mockFacilities));
        }

        [Test]
        public async Task GetFacilityByOrgId_WhenNoFacilitiesExist_ReturnsNotFound()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockQueryHandler
                .Setup(q => q.GetFacilitiesByOrgAsync(subscription, orgId))
                .ReturnsAsync((IEnumerable<Facility>)null);

            // Act
            var result = await _controller.GetFacilityByOrgId(orgId, subscription);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult?.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task GetFacilityByName_WhenFacilitiesExist_ReturnsOkWithFacilities()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            bool subscription = false;
            string name = "Test";
            var mockFacilities = new List<Facility>
            {
                new Facility { FacilityId = Guid.NewGuid(), FacilityName = "Test Facility 1", OrganizationId = orgId },
                new Facility { FacilityId = Guid.NewGuid(), FacilityName = "Test Facility 2", OrganizationId = orgId }
            };

            _mockQueryHandler
                .Setup(q => q.GetFacilitiesByNameAsync(subscription, name, orgId))
                .ReturnsAsync(mockFacilities);

            // Act
            var result = await _controller.GetFacilityByName(name, orgId, subscription);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult?.StatusCode, Is.EqualTo(200));
            Assert.That(okResult?.Value, Is.EqualTo(mockFacilities));
        }

        [Test]
        public async Task GetFacilityByName_WhenNoFacilitiesExist_ReturnsNotFound()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            bool subscription = false;
            string name = "Test";

            _mockQueryHandler
                .Setup(q => q.GetFacilitiesByNameAsync(subscription, name, orgId))
                .ReturnsAsync(new List<Facility>());

            // Act
            var result = await _controller.GetFacilityByName(name, orgId, subscription);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult?.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task GetFacilityByName_WhenNameIsEmpty_ReturnsBadRequest()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            bool subscription = false;
            string name = "";

            // Act
            var result = await _controller.GetFacilityByName(name, orgId, subscription);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult?.StatusCode, Is.EqualTo(400));
        }

        [Test]
        public async Task GetFacilityByName_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            bool subscription = false;
            string name = "Test";

            _mockQueryHandler
                .Setup(q => q.GetFacilitiesByNameAsync(subscription, name, orgId))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetFacilityByName(name, orgId, subscription);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task AddFacility_WhenValidFacility_ReturnsCreatedAtAction()
        {
            // Arrange
            var facilityId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var facility = new Facility
            {
                FacilityId = facilityId,
                FacilityName = "Test Facility",
                StreetName = "123 Main St",
                City = "Anytown",
                State = "CA",
                Zipcode = "12345",
                Country = "USA",
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockCommandHandler
                .Setup(c => c.AddFacilityAsync(It.IsAny<List<Facility>>(), subscription, orgId))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.AddFacility(facility);

            // Assert
            Assert.That(result, Is.InstanceOf<CreatedAtActionResult>());
            var createdResult = result as CreatedAtActionResult;
            Assert.That(createdResult?.StatusCode, Is.EqualTo(201));
            Assert.That(createdResult?.ActionName, Is.EqualTo(nameof(FacilityController.GetFacilityById)));
            Assert.That(createdResult?.RouteValues["id"], Is.EqualTo(facilityId));
            Assert.That(createdResult?.Value, Is.EqualTo(facility));
        }

        [Test]
        public async Task AddFacility_WhenNullFacility_ReturnsBadRequest()
        {
            // Act
            var result = await _controller.AddFacility(null!);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult?.StatusCode, Is.EqualTo(400));
        }

        [Test]
        public async Task AddFacility_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var facilityId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var facility = new Facility
            {
                FacilityId = facilityId,
                FacilityName = "Test Facility",
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockCommandHandler
                .Setup(c => c.AddFacilityAsync(It.IsAny<List<Facility>>(), subscription, orgId))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.AddFacility(facility);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task UpdateFacility_WhenValidFacility_ReturnsNoContent()
        {
            // Arrange
            var facilityId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var facility = new Facility
            {
                FacilityId = facilityId,
                FacilityName = "Updated Facility",
                StreetName = "123 Main St",
                City = "Anytown",
                State = "CA",
                Zipcode = "12345",
                Country = "USA",
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockCommandHandler
                .Setup(c => c.UpdateFacilityAsync(facility, subscription, orgId))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.UpdateFacility(facilityId, facility);

            // Assert
            Assert.That(result, Is.InstanceOf<NoContentResult>());
            var noContentResult = result as NoContentResult;
            Assert.That(noContentResult?.StatusCode, Is.EqualTo(204));
        }

        [Test]
        public async Task UpdateFacility_WhenNullFacility_ReturnsBadRequest()
        {
            // Arrange
            var facilityId = Guid.NewGuid();

            // Act
            var result = await _controller.UpdateFacility(facilityId, null!);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult?.StatusCode, Is.EqualTo(400));
        }

        [Test]
        public async Task UpdateFacility_WhenIdMismatch_ReturnsBadRequest()
        {
            // Arrange
            var facilityId = Guid.NewGuid();
            var differentId = Guid.NewGuid();
            var facility = new Facility
            {
                FacilityId = differentId,
                FacilityName = "Test Facility"
            };

            // Act
            var result = await _controller.UpdateFacility(facilityId, facility);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult?.StatusCode, Is.EqualTo(400));
        }

        [Test]
        public async Task UpdateFacility_WhenFacilityDoesNotExist_ReturnsNotFound()
        {
            // Arrange
            var facilityId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var facility = new Facility
            {
                FacilityId = facilityId,
                FacilityName = "Test Facility",
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockCommandHandler
                .Setup(c => c.UpdateFacilityAsync(facility, subscription, orgId))
                .ThrowsAsync(new KeyNotFoundException());

            // Act
            var result = await _controller.UpdateFacility(facilityId, facility);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult?.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task UpdateFacility_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var facilityId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var facility = new Facility
            {
                FacilityId = facilityId,
                FacilityName = "Test Facility",
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockCommandHandler
                .Setup(c => c.UpdateFacilityAsync(facility, subscription, orgId))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.UpdateFacility(facilityId, facility);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task DeleteFacility_WhenFacilityExists_ReturnsNoContent()
        {
            // Arrange
            var facilityId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockCommandHandler
                .Setup(c => c.DeleteFacilityAsync(subscription, facilityId, orgId))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.DeleteFacility(facilityId, orgId, subscription);

            // Assert
            Assert.That(result, Is.InstanceOf<NoContentResult>());
            var noContentResult = result as NoContentResult;
            Assert.That(noContentResult?.StatusCode, Is.EqualTo(204));
        }

        [Test]
        public async Task DeleteFacility_WhenFacilityDoesNotExist_ReturnsNotFound()
        {
            // Arrange
            var facilityId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockCommandHandler
                .Setup(c => c.DeleteFacilityAsync(subscription, facilityId, orgId))
                .ThrowsAsync(new KeyNotFoundException());

            // Act
            var result = await _controller.DeleteFacility(facilityId, orgId, subscription);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult?.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task DeleteFacility_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var facilityId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockCommandHandler
                .Setup(c => c.DeleteFacilityAsync(subscription, facilityId, orgId))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.DeleteFacility(facilityId, orgId, subscription);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }
    }
}
