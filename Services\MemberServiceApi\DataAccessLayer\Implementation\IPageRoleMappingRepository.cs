using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Contracts;

namespace MemberServiceDataAccessLayer.Implementation
{
    public interface IPageRoleMappingRepository : IShardGenericRepository<PageRoleMapping>
    {
        Task<List<PageRoleMapping>> GetByPagePathAsync(string pagePath, Guid OrganizationId, bool subscription);
        Task<List<string>> GetRolesByPagePathAsync(string pagePath, Guid OrganizationId, bool subscription);
    }
}
