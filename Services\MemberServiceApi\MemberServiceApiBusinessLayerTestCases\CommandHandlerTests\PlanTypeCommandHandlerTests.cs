using NUnit.Framework;
using Moq;
using MemberServiceBusinessLayer.CommandHandler;
using MemberServiceDataAccessLayer.Implementation;
using Contracts;
using System;
using System.Threading.Tasks;

namespace MemberServiceApiBusinessLayerTestCases.CommandHandlerTests
{
    [TestFixture]
    public class PlanTypeCommandHandlerTests
    {
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<IPlanTypeRepository> _mockPlanTypeRepository;
        private PlanTypeCommandHandler _planTypeCommandHandler;

        [SetUp]
        public void Setup()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockPlanTypeRepository = new Mock<IPlanTypeRepository>();

            _mockUnitOfWork.Setup(u => u.PlanTypeRepository).Returns(_mockPlanTypeRepository.Object);

            _planTypeCommandHandler = new PlanTypeCommandHandler(_mockUnitOfWork.Object);
        }

        [Test]
        public async Task AddPlanTypeAsync_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var planType = new PlanType
            {
                Id = Guid.NewGuid(),
                PlanName = "Premium Plan",
                CreatedDate = DateTime.Now
            };

            _mockPlanTypeRepository
                .Setup(r => r.AddAsync(planType))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _planTypeCommandHandler.AddPlanTypeAsync(planType);

            // Assert
            _mockPlanTypeRepository.Verify(r => r.AddAsync(planType), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdatePlanTypeAsync_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var planType = new PlanType
            {
                Id = Guid.NewGuid(),
                PlanName = "Premium Plan",
                CreatedDate = DateTime.Now,
                UpdatedDate = DateTime.Now
            };

            _mockPlanTypeRepository
                .Setup(r => r.UpdateAsync(planType))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _planTypeCommandHandler.UpdatePlanTypeAsync(planType);

            // Assert
            _mockPlanTypeRepository.Verify(r => r.UpdateAsync(planType), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeletePlanTypeAsync_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var planTypeId = Guid.NewGuid();

            _mockPlanTypeRepository
                .Setup(r => r.DeleteByIdAsync(planTypeId))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _planTypeCommandHandler.DeletePlanTypeAsync(planTypeId);

            // Assert
            _mockPlanTypeRepository.Verify(r => r.DeleteByIdAsync(planTypeId), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public void AddPlanTypeAsync_ShouldThrowArgumentNullException_WhenPlanTypeIsNull()
        {
            // Act & Assert
            Assert.ThrowsAsync<ArgumentNullException>(async () =>
                await _planTypeCommandHandler.AddPlanTypeAsync(null));

            _mockPlanTypeRepository.Verify(r => r.AddAsync(It.IsAny<PlanType>()), Times.Never);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Never);
        }

        [Test]
        public void UpdatePlanTypeAsync_ShouldThrowArgumentNullException_WhenPlanTypeIsNull()
        {
            // Act & Assert
            Assert.ThrowsAsync<ArgumentNullException>(async () =>
                await _planTypeCommandHandler.UpdatePlanTypeAsync(null));

            _mockPlanTypeRepository.Verify(r => r.UpdateAsync(It.IsAny<PlanType>()), Times.Never);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Never);
        }

    }
}
