﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Contracts;
using MemberServiceDataAccessLayer.Implementation;
using Microsoft.Extensions.Localization;

namespace MemberServiceBusinessLayer.QueryHandler
{
    public class RolesQueryHandler : IRolesQueryHandler<Role>
    {
        private readonly IUnitOfWork _unitOfWork;

        public RolesQueryHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        }

        public async Task<Role> GetRoleByIdAsync(Guid id, Guid orgId, bool Subscription)
        {
            var role = await _unitOfWork.RolesRepository.GetByIdAsync(id, orgId, Subscription);
            return role;
        }

        public async Task<List<Role>> GetRolesByNameAsync(string name, Guid orgId, bool Subscription)
        {
            var roles = await _unitOfWork.RolesRepository.GetAllAsync();
            return roles.Where(role => role.RoleName.Contains(name, StringComparison.OrdinalIgnoreCase)).ToList();
        }

        public async Task<IEnumerable<Role>> GetAllRolesAsync()
        {
            return await _unitOfWork.RolesRepository.GetAllAsync();
        }

        public async Task<IEnumerable<Role>> GetRolesByOrgAsync(Guid id, bool Subscription)
        {
            return await _unitOfWork.RolesRepository.GetByOrgIdAsync(id, Subscription);
            
        }
    }
}
