using NUnit.Framework;
using Moq;
using MemberServiceBusinessLayer.CommandHandler;
using MemberServiceDataAccessLayer.Implementation;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MemberServiceApiBusinessLayerTestCases.CommandHandlerTests
{
    [TestFixture]
    public class AddressesCommandHandlerTests
    {
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<IAddressesRepository> _mockAddressesRepository;
        private AddressesCommandHandler _addressesCommandHandler;

        [SetUp]
        public void Setup()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockAddressesRepository = new Mock<IAddressesRepository>();

            _mockUnitOfWork.Setup(u => u.AddressesRepository).Returns(_mockAddressesRepository.Object);

            _addressesCommandHandler = new AddressesCommandHandler(_mockUnitOfWork.Object);
        }

        [Test]
        public async Task AddAddressAsync_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var addresses = new List<Address>
            {
                new Address
                {
                    AddressId = Guid.NewGuid(),
                    AddressLine1 = "123 Main St",
                    City = "Anytown",
                    State = "CA",
                    PostalCode = "12345",
                    Country = "USA",
                    OrganizationId = orgId,
                    Subscription = subscription
                }
            };

            _mockAddressesRepository
                .Setup(r => r.AddAsync(addresses, orgId, subscription))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _addressesCommandHandler.AddAddressAsync(addresses, subscription, orgId);

            // Assert
            _mockAddressesRepository.Verify(r => r.AddAsync(addresses, orgId, subscription), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateAddressAsync_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var address = new Address
            {
                AddressId = Guid.NewGuid(),
                AddressLine1 = "123 Main St",
                City = "Anytown",
                State = "CA",
                PostalCode = "12345",
                Country = "USA",
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockAddressesRepository
                .Setup(r => r.UpdateAsync(address, orgId, subscription))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _addressesCommandHandler.UpdateAddressAsync(address, subscription, orgId);

            // Assert
            _mockAddressesRepository.Verify(r => r.UpdateAsync(address, orgId, subscription), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteAddressAsync_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var addressId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockAddressesRepository
                .Setup(r => r.DeleteByIdAsync(addressId, orgId, subscription))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _addressesCommandHandler.DeleteAddressAsync(addressId, subscription, orgId);

            // Assert
            _mockAddressesRepository.Verify(r => r.DeleteByIdAsync(addressId, orgId, subscription), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

    }
}
