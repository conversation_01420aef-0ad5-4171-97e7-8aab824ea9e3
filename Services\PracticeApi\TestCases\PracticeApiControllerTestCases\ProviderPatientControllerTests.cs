using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using PracticeApi.Controllers;
using PracticeBusinessLayer;
using PracticeContracts;
using System;
using System.Collections.Generic;

namespace PracticeApiControllerTestCases
{
    [TestFixture]
    public class ProviderPatientControllerTests
    {
        private Mock<IProviderPatientCommandHandler<ProviderPatient>> _mockCommandHandler;
        private Mock<IProviderPatientQueryHandler<ProviderPatient>> _mockQueryHandler;
        private Mock<ILogger<ProviderPatientController>> _mockLogger;
        private Mock<IStringLocalizer<ProviderPatientController>> _mockLocalizer;
        private ProviderPatientController _controller;

        [SetUp]
        public void Setup()
        {
            _mockCommandHandler = new Mock<IProviderPatientCommandHandler<ProviderPatient>>();
            _mockQueryHandler = new Mock<IProviderPatientQueryHandler<ProviderPatient>>();
            _mockLogger = new Mock<ILogger<ProviderPatientController>>();
            _mockLocalizer = new Mock<IStringLocalizer<ProviderPatientController>>();

            // Setup localizer mock
            _mockLocalizer.Setup(l => l["GetLogError"]).Returns(new LocalizedString("GetLogError", "Error while fetching provider-patient relationships."));
            _mockLocalizer.Setup(l => l["500"]).Returns(new LocalizedString("500", "500"));
            _mockLocalizer.Setup(l => l["NoTask"]).Returns(new LocalizedString("NoTask", "No provider-patient relationships provided."));
            _mockLocalizer.Setup(l => l["SuccessfulRegistration"]).Returns(new LocalizedString("SuccessfulRegistration", "Registration successful."));
            _mockLocalizer.Setup(l => l["PostLogError"]).Returns(new LocalizedString("PostLogError", "Error while adding provider-patient relationships."));
            _mockLocalizer.Setup(l => l["UpdateLogError"]).Returns(new LocalizedString("UpdateLogError", "Error while updating provider-patient relationship."));
            _mockLocalizer.Setup(l => l["DeleteLogError"]).Returns(new LocalizedString("DeleteLogError", "Error while deleting provider-patient relationship."));
            _mockLocalizer.Setup(l => l["SuccessfulDeletion"]).Returns(new LocalizedString("SuccessfulDeletion", "Provider-patient relationship successfully deleted."));
            _mockLocalizer.Setup(l => l["UpdateSuccessful"]).Returns(new LocalizedString("UpdateSuccessful", "Update successful."));

            _controller = new ProviderPatientController(
                _mockCommandHandler.Object,
                _mockQueryHandler.Object,
                _mockLogger.Object,
                _mockLocalizer.Object
            );
        }

        [Test]
        public async Task Get_ReturnsOkWithProviderPatientList()
        {
            // Arrange
            var mockProviderPatients = new List<ProviderPatient>
            {
                new ProviderPatient { Id = Guid.NewGuid(), PCPId = Guid.NewGuid(), SSN = "***********", Username = "user1" },
                new ProviderPatient { Id = Guid.NewGuid(), PCPId = Guid.NewGuid(), SSN = "***********", Username = "user2" }
            };

            _mockQueryHandler.Setup(q => q.GetProviderPatient()).ReturnsAsync(mockProviderPatients);

            // Act
            var result = await _controller.Get();

            // Assert
            Assert.That(result.Result, Is.TypeOf<OkObjectResult>());
            var okResult = result.Result as OkObjectResult;
            Assert.That(okResult.StatusCode, Is.EqualTo(200));
            Assert.That(okResult.Value, Is.EqualTo(mockProviderPatients));
        }

        [Test]
        public async Task Get_WhenExceptionThrown_ReturnsInternalServerError()
        {
            // Arrange
            _mockQueryHandler.Setup(q => q.GetProviderPatient()).ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _controller.Get();

            // Assert
            Assert.That(result.Result, Is.TypeOf<StatusCodeResult>());
            var statusCodeResult = result.Result as StatusCodeResult;
            Assert.That(statusCodeResult.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task GetById_ValidId_ReturnsProviderPatient()
        {
            // Arrange
            var providerPatientId = Guid.NewGuid();
            var providerPatient = new ProviderPatient
            {
                Id = providerPatientId,
                PCPId = Guid.NewGuid(),
                SSN = "***********",
                Username = "testuser"
            };
            _mockQueryHandler.Setup(q => q.GetProviderPatientById(providerPatientId)).ReturnsAsync(providerPatient);

            // Act
            var result = await _controller.GetById(providerPatientId);

            // Assert
            Assert.That(result.Result, Is.TypeOf<OkObjectResult>());
            var okResult = result.Result as OkObjectResult;
            Assert.That(okResult.StatusCode, Is.EqualTo(200));
            Assert.That(okResult.Value, Is.EqualTo(providerPatient));
        }

        [Test]
        public async Task GetById_WhenExceptionThrown_ReturnsInternalServerError()
        {
            // Arrange
            var providerPatientId = Guid.NewGuid();
            _mockQueryHandler.Setup(q => q.GetProviderPatientById(providerPatientId))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _controller.GetById(providerPatientId);

            // Assert
            Assert.That(result.Result, Is.TypeOf<StatusCodeResult>());
            var statusCodeResult = result.Result as StatusCodeResult;
            Assert.That(statusCodeResult.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task UpdateById_ValidProviderPatient_ReturnsOk()
        {
            // Arrange
            var providerPatientId = Guid.NewGuid();
            var providerPatient = new ProviderPatient
            {
                Id = providerPatientId,
                PCPId = Guid.NewGuid(),
                SSN = "***********",
                Username = "testuser"
            };
            _mockCommandHandler.Setup(c => c.UpdateProviderPatient(providerPatient)).Returns(Task.CompletedTask);

            // Act
            var result = await _controller.UpdateById(providerPatientId, providerPatient);

            // Assert
            Assert.That(result, Is.TypeOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult.Value.ToString(), Is.EqualTo("Update successful."));
        }

        [Test]
        public async Task UpdateById_WhenExceptionThrown_ReturnsInternalServerError()
        {
            // Arrange
            var providerPatientId = Guid.NewGuid();
            var providerPatient = new ProviderPatient
            {
                Id = providerPatientId,
                PCPId = Guid.NewGuid(),
                SSN = "***********",
                Username = "testuser"
            };
            _mockCommandHandler.Setup(c => c.UpdateProviderPatient(providerPatient))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _controller.UpdateById(providerPatientId, providerPatient);

            // Assert
            Assert.That(result, Is.TypeOf<StatusCodeResult>());
            var statusCodeResult = result as StatusCodeResult;
            Assert.That(statusCodeResult.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task DeleteById_ValidId_ReturnsOk()
        {
            // Arrange
            var providerPatientId = Guid.NewGuid();
            _mockCommandHandler.Setup(c => c.DeleteProviderPatientById(providerPatientId)).Returns(Task.CompletedTask);

            // Act
            var result = await _controller.DeleteById(providerPatientId);

            // Assert
            Assert.That(result, Is.TypeOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult.Value.ToString(), Is.EqualTo("Provider-patient relationship successfully deleted."));
        }

        [Test]
        public async Task DeleteById_WhenExceptionThrown_ReturnsInternalServerError()
        {
            // Arrange
            var providerPatientId = Guid.NewGuid();
            _mockCommandHandler.Setup(c => c.DeleteProviderPatientById(providerPatientId))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _controller.DeleteById(providerPatientId);

            // Assert
            Assert.That(result, Is.TypeOf<StatusCodeResult>());
            var statusCodeResult = result as StatusCodeResult;
            Assert.That(statusCodeResult.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task Registration_ValidProviderPatients_ReturnsSuccessfulResponse()
        {
            // Arrange
            var providerPatients = new List<ProviderPatient>
            {
                new ProviderPatient { Id = Guid.NewGuid(), PCPId = Guid.NewGuid(), SSN = "***********", Username = "user1" }
            };
            _mockCommandHandler.Setup(c => c.AddProviderPatient(providerPatients)).Returns(Task.CompletedTask);

            // Act
            var result = await _controller.Registration(providerPatients);

            // Assert
            Assert.That(result, Is.TypeOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult?.Value?.ToString(), Is.EqualTo("Registration successful."));
        }

        [Test]
        public async Task Registration_EmptyProviderPatientsList_SetsResponseToBadRequestButReturnsOk()
        {
            // Arrange
            List<ProviderPatient> providerPatients = new List<ProviderPatient>();
            _mockCommandHandler.Setup(c => c.AddProviderPatient(providerPatients)).Returns(Task.CompletedTask);

            // Act
            var result = await _controller.Registration(providerPatients);

            // Assert
            // Due to a bug in the controller, it sets response to BadRequest but then continues execution
            // and returns Ok instead because there's no return statement after the BadRequest
            Assert.That(result, Is.TypeOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult.Value.ToString(), Is.EqualTo("Registration successful."));
        }

        [Test]
        public async Task Registration_NullProviderPatientsList_SetsResponseToBadRequestButReturnsOk()
        {
            // Arrange
            List<ProviderPatient>? nullList = null;
            _mockCommandHandler.Setup(c => c.AddProviderPatient(It.IsAny<List<ProviderPatient>>())).Returns(Task.CompletedTask);

            // Act
            var result = await _controller.Registration(nullList);

            // Assert
            // Due to a bug in the controller, it sets response to BadRequest but then continues execution
            // and returns Ok instead because there's no return statement after the BadRequest
            Assert.That(result, Is.TypeOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult?.Value?.ToString(), Is.EqualTo("Registration successful."));
        }

        [Test]
        public async Task Registration_WhenExceptionThrown_ReturnsInternalServerError()
        {
            // Arrange
            var providerPatients = new List<ProviderPatient>
            {
                new ProviderPatient { Id = Guid.NewGuid(), PCPId = Guid.NewGuid(), SSN = "***********", Username = "user1" }
            };
            _mockCommandHandler.Setup(c => c.AddProviderPatient(providerPatients))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _controller.Registration(providerPatients);

            // Assert
            Assert.That(result, Is.TypeOf<StatusCodeResult>());
            var statusCodeResult = result as StatusCodeResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }
    }
}
