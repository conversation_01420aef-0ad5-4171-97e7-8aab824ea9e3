﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using DataAccessLayer.Context;
using Contracts;
using Microsoft.EntityFrameworkCore;
using Interfaces.ShardManagement;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;

namespace MemberServiceDataAccessLayer.Implementation
{
    public class InsuranceRepository : ShardGenericRepository<Insurance>, IInsuranceRepository
    {
        private readonly AccountDatabaseContext _context;

        public InsuranceRepository(AccountDatabaseContext context, ShardMapManagerService<AccountDatabaseContext, AccountDatabaseContext> shardMapManagerService, IStringLocalizer<AccountDatabaseContext> localizer,
            ILogger<AccountDatabaseContext> logger) : base(context, shardMapManagerService,localizer, logger)
        {
            _context = context;
        }

        
    }
}
