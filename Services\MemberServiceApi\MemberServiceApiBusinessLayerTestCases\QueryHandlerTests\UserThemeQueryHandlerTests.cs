using NUnit.Framework;
using Moq;
using MemberServiceBusinessLayer.QueryHandler;
using MemberServiceDataAccessLayer.Implementation;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using MemberServiceBusinessLayer;

namespace MemberServiceApiBusinessLayerTestCases.QueryHandlerTests
{
    // Create a test-specific implementation of IUserThemeQueryHandler
    public class TestUserThemeQueryHandler : IUserThemeQueryHandler<UserTheme>
    {
        private readonly IUserThemeRepository _repository;

        public TestUserThemeQueryHandler(IUserThemeRepository repository)
        {
            _repository = repository;
        }

        public async Task<IEnumerable<UserTheme>> GetUserThemes()
        {
            return await _repository.GetAllAsync();
        }

        public async Task<UserTheme> GetUserThemeById(Guid id, Guid OrgID, bool Subscription)
        {
            return await _repository.GetByIdAsync(id, OrgID, Subscription);
        }
    }

    [TestFixture]
    public class UserThemeQueryHandlerTests
    {
        private Mock<IUserThemeRepository> _mockUserThemeRepository;
        private IUserThemeQueryHandler<UserTheme> _userThemeQueryHandler;

        [SetUp]
        public void Setup()
        {
            _mockUserThemeRepository = new Mock<IUserThemeRepository>();
            _userThemeQueryHandler = new TestUserThemeQueryHandler(_mockUserThemeRepository.Object);
        }

        [Test]
        public async Task GetUserThemes_ShouldReturnAllUserThemes()
        {
            // Arrange
            var expectedUserThemes = new List<UserTheme>
            {
                new UserTheme
                {
                    UserId = Guid.NewGuid(),
                    ThemeName = "Dark Theme",
                    OrganizationId = Guid.NewGuid(),
                    Subscription = false
                },
                new UserTheme
                {
                    UserId = Guid.NewGuid(),
                    ThemeName = "Light Theme",
                    OrganizationId = Guid.NewGuid(),
                    Subscription = true
                }
            };

            _mockUserThemeRepository
                .Setup(r => r.GetAllAsync())
                .ReturnsAsync(expectedUserThemes);

            // Act
            var result = await _userThemeQueryHandler.GetUserThemes();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(2));
            Assert.That(result, Is.EquivalentTo(expectedUserThemes));
            _mockUserThemeRepository.Verify(r => r.GetAllAsync(), Times.Once);
        }

        [Test]
        public async Task GetUserThemeById_ShouldReturnUserTheme_WhenUserThemeExists()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var expectedUserTheme = new UserTheme
            {
                UserId = userId,
                ThemeName = "Dark Theme",
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockUserThemeRepository
                .Setup(r => r.GetByIdAsync(userId, orgId, subscription))
                .ReturnsAsync(expectedUserTheme);

            // Act
            var result = await _userThemeQueryHandler.GetUserThemeById(userId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.UserId, Is.EqualTo(userId));
            Assert.That(result.ThemeName, Is.EqualTo("Dark Theme"));
            _mockUserThemeRepository.Verify(r => r.GetByIdAsync(userId, orgId, subscription), Times.Once);
        }

        [Test]
        public async Task GetUserThemeById_ShouldReturnNull_WhenUserThemeDoesNotExist()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockUserThemeRepository
                .Setup(r => r.GetByIdAsync(userId, orgId, subscription))
                .ReturnsAsync((UserTheme)null);

            // Act
            var result = await _userThemeQueryHandler.GetUserThemeById(userId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Null);
            _mockUserThemeRepository.Verify(r => r.GetByIdAsync(userId, orgId, subscription), Times.Once);
        }
    }
}
