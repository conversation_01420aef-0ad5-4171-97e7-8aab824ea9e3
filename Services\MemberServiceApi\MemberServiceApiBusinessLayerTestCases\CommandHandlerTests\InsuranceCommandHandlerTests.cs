using NUnit.Framework;
using Moq;
using MemberServiceBusinessLayer.CommandHandler;
using MemberServiceDataAccessLayer.Implementation;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MemberServiceApiBusinessLayerTestCases.CommandHandlerTests
{
    [TestFixture]
    public class InsuranceCommandHandlerTests
    {
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<IInsuranceRepository> _mockInsuranceRepository;
        private InsuranceCommandHandler _insuranceCommandHandler;

        [SetUp]
        public void Setup()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockInsuranceRepository = new Mock<IInsuranceRepository>();

            _mockUnitOfWork.Setup(u => u.InsuranceRepository).Returns(_mockInsuranceRepository.Object);

            _insuranceCommandHandler = new InsuranceCommandHandler(_mockUnitOfWork.Object);
        }

        [Test]
        public async Task AddInsuranceAsync_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var insurances = new List<Insurance>
            {
                new Insurance
                {
                    InsuranceId = Guid.NewGuid(),
                    PrimaryInsuranceProvider = "Insurance Co",
                    PolicyNumber = "POL123456",
                    PlanName = "Basic Plan",
                    GroupNumber = "GRP123",
                    OrganizationId = orgId,
                    Subscription = subscription
                }
            };

            _mockInsuranceRepository
                .Setup(r => r.AddAsync(insurances, orgId, subscription))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _insuranceCommandHandler.AddInsuranceAsync(insurances, orgId, subscription);

            // Assert
            _mockInsuranceRepository.Verify(r => r.AddAsync(insurances, orgId, subscription), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateInsuranceAsync_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var insurance = new Insurance
            {
                InsuranceId = Guid.NewGuid(),
                PrimaryInsuranceProvider = "Insurance Co",
                PolicyNumber = "POL123456",
                PlanName = "Basic Plan",
                GroupNumber = "GRP123",
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockInsuranceRepository
                .Setup(r => r.UpdateAsync(insurance, orgId, subscription))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _insuranceCommandHandler.UpdateInsuranceAsync(insurance, orgId, subscription);

            // Assert
            _mockInsuranceRepository.Verify(r => r.UpdateAsync(insurance, orgId, subscription), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteInsuranceAsync_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var insuranceId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockInsuranceRepository
                .Setup(r => r.DeleteByIdAsync(insuranceId, orgId, subscription))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _insuranceCommandHandler.DeleteInsuranceAsync(insuranceId, orgId, subscription);

            // Assert
            _mockInsuranceRepository.Verify(r => r.DeleteByIdAsync(insuranceId, orgId, subscription), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

       
    }
}
