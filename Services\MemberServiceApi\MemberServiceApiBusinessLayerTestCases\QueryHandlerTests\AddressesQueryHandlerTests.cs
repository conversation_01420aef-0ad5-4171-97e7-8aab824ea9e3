using NUnit.Framework;
using Moq;
using MemberServiceBusinessLayer.QueryHandler;
using MemberServiceDataAccessLayer.Implementation;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;

namespace MemberServiceApiBusinessLayerTestCases.QueryHandlerTests
{
    [TestFixture]
    public class AddressesQueryHandlerTests
    {
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<IAddressesRepository> _mockAddressesRepository;
        private AddressesQueryHandler _addressesQueryHandler;

        [SetUp]
        public void Setup()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockAddressesRepository = new Mock<IAddressesRepository>();

            _mockUnitOfWork.Setup(u => u.AddressesRepository).Returns(_mockAddressesRepository.Object);

            _addressesQueryHandler = new AddressesQueryHandler(_mockUnitOfWork.Object);
        }

        [Test]
        public async Task GetAddressByIdAsync_ShouldReturnAddress_WhenAddressExists()
        {
            // Arrange
            var addressId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var expectedAddress = new Address 
            { 
                AddressId = addressId, 
                AddressLine1 = "123 Main St", 
                City = "Anytown", 
                State = "CA", 
                PostalCode = "12345", 
                Country = "USA",
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockAddressesRepository
                .Setup(r => r.GetByIdAsync(addressId, orgId, subscription))
                .ReturnsAsync(expectedAddress);

            // Act
            var result = await _addressesQueryHandler.GetAddressByIdAsync(addressId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.AddressId, Is.EqualTo(addressId));
            Assert.That(result.AddressLine1, Is.EqualTo("123 Main St"));
            Assert.That(result.City, Is.EqualTo("Anytown"));
            Assert.That(result.State, Is.EqualTo("CA"));
            Assert.That(result.PostalCode, Is.EqualTo("12345"));
            Assert.That(result.Country, Is.EqualTo("USA"));
            Assert.That(result.OrganizationId, Is.EqualTo(orgId));
            Assert.That(result.Subscription, Is.EqualTo(subscription));
            
            _mockAddressesRepository.Verify(r => r.GetByIdAsync(addressId, orgId, subscription), Times.Once);
        }

        [Test]
        public async Task GetAddressByIdAsync_ShouldReturnNull_WhenAddressDoesNotExist()
        {
            // Arrange
            var addressId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockAddressesRepository
                .Setup(r => r.GetByIdAsync(addressId, orgId, subscription))
                .ReturnsAsync((Address)null);

            // Act
            var result = await _addressesQueryHandler.GetAddressByIdAsync(addressId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Null);
            _mockAddressesRepository.Verify(r => r.GetByIdAsync(addressId, orgId, subscription), Times.Once);
        }

        [Test]
        public async Task GetAllAddressesAsync_ShouldReturnAllAddresses()
        {
            // Arrange
            var addresses = new List<Address>
            {
                new Address 
                { 
                    AddressId = Guid.NewGuid(), 
                    AddressLine1 = "123 Main St", 
                    City = "Anytown", 
                    State = "CA", 
                    PostalCode = "12345", 
                    Country = "USA" 
                },
                new Address 
                { 
                    AddressId = Guid.NewGuid(), 
                    AddressLine1 = "456 Oak Ave", 
                    City = "Othertown", 
                    State = "NY", 
                    PostalCode = "67890", 
                    Country = "USA" 
                }
            };

            _mockAddressesRepository
                .Setup(r => r.GetAllAsync())
                .ReturnsAsync(addresses);

            // Act
            var result = await _addressesQueryHandler.GetAllAddressesAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result.First().AddressLine1, Is.EqualTo("123 Main St"));
            Assert.That(result.Last().AddressLine1, Is.EqualTo("456 Oak Ave"));
            
            _mockAddressesRepository.Verify(r => r.GetAllAsync(), Times.Once);
        }

        [Test]
        public void Constructor_ShouldThrowArgumentNullException_WhenUnitOfWorkIsNull()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new AddressesQueryHandler(null));
        }
    }
}
