using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Configuration;
using MemberServiceBusinessLayer.QueryHandler;
using MemberServiceDataAccessLayer.Implementation;
using Contracts;
using System;
using System.Threading.Tasks;

namespace MemberServiceApiBusinessLayerTestCases.QueryHandlerTests
{
    [TestFixture]
    public class CheckAccessQueryHandlerTests
    {
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<IProductUserAccessRepository> _mockProductUserAccessRepository;
        private CheckAccessQueryHandler _checkAccessQueryHandler;

        [SetUp]
        public void Setup()
        {
            _mockConfiguration = new Mock<IConfiguration>();
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockProductUserAccessRepository = new Mock<IProductUserAccessRepository>();

            _mockUnitOfWork.Setup(u => u.ProductUserAccessRepository).Returns(_mockProductUserAccessRepository.Object);

            _checkAccessQueryHandler = new CheckAccessQueryHandler(_mockConfiguration.Object, _mockUnitOfWork.Object);
        }

        [Test]
        public async Task CheckAccessAsync_ShouldReturnAccessRecord()
        {
            // Arrange
            var memberId = Guid.NewGuid();
            var productId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            var expectedAccessRecord = new ProductUserAccess
            {
                MemberId = memberId,
                ProductId = productId,
                HasAccess = true
            };

            _mockProductUserAccessRepository
                .Setup(r => r.GetAccessRecordAsync(productId, memberId, orgId, subscription))
                .ReturnsAsync(expectedAccessRecord);

            // Act
            var result = await _checkAccessQueryHandler.CheckAccessAsync(memberId, productId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.MemberId, Is.EqualTo(memberId));
            Assert.That(result.ProductId, Is.EqualTo(productId));
            Assert.That(result.HasAccess, Is.True);
            _mockProductUserAccessRepository.Verify(r => r.GetAccessRecordAsync(productId, memberId, orgId, subscription), Times.Once);
        }

        [Test]
        public async Task CheckAccessAsync_ShouldReturnNull_WhenAccessRecordDoesNotExist()
        {
            // Arrange
            var memberId = Guid.NewGuid();
            var productId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockProductUserAccessRepository
                .Setup(r => r.GetAccessRecordAsync(productId, memberId, orgId, subscription))
                .ReturnsAsync((ProductUserAccess)null);

            // Act
            var result = await _checkAccessQueryHandler.CheckAccessAsync(memberId, productId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Null);
            _mockProductUserAccessRepository.Verify(r => r.GetAccessRecordAsync(productId, memberId, orgId, subscription), Times.Once);
        }
    }
}
