using NUnit.Framework;
using Moq;
using MemberServiceBusinessLayer.QueryHandler;
using MemberServiceDataAccessLayer.Implementation;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;

namespace MemberServiceApiBusinessLayerTestCases.QueryHandlerTests
{
    [TestFixture]
    public class ProductFeatureQueryHandlerTests
    {
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<IProductFeatureRepository> _mockProductFeatureRepository;
        private ProductFeatureQueryHandler _productFeatureQueryHandler;

        [SetUp]
        public void Setup()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockProductFeatureRepository = new Mock<IProductFeatureRepository>();

            _mockUnitOfWork.Setup(u => u.ProductFeatureRepository).Returns(_mockProductFeatureRepository.Object);

            _productFeatureQueryHandler = new ProductFeatureQueryHandler(_mockUnitOfWork.Object);
        }

        [Test]
        public async Task GetProductFeatureByIdAsync_ShouldReturnProductFeature_WhenProductFeatureExists()
        {
            // Arrange
            var productFeatureId = Guid.NewGuid();
            var prodId = Guid.NewGuid();
            var createdDate = DateTime.Now.AddDays(-30);

            var expectedProductFeature = new ProductFeature
            {
                Id = productFeatureId,
                FeatureName = "Advanced Reporting",
                ProdId = prodId,
                ProdName = "Premium Product",
                Status = true,
                Created = createdDate
            };

            _mockProductFeatureRepository
                .Setup(r => r.GetByIdAsync(productFeatureId))
                .ReturnsAsync(expectedProductFeature);

            // Act
            var result = await _productFeatureQueryHandler.GetProductFeatureByIdAsync(productFeatureId);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(productFeatureId));
            Assert.That(result.FeatureName, Is.EqualTo("Advanced Reporting"));
            Assert.That(result.ProdId, Is.EqualTo(prodId));
            Assert.That(result.ProdName, Is.EqualTo("Premium Product"));
            Assert.That(result.Status, Is.True);
            Assert.That(result.Created, Is.EqualTo(createdDate));

            _mockProductFeatureRepository.Verify(r => r.GetByIdAsync(productFeatureId), Times.Once);
        }

        [Test]
        public async Task GetProductFeatureByIdAsync_ShouldReturnNull_WhenProductFeatureDoesNotExist()
        {
            // Arrange
            var productFeatureId = Guid.NewGuid();

            _mockProductFeatureRepository
                .Setup(r => r.GetByIdAsync(productFeatureId))
                .ReturnsAsync((ProductFeature)null);

            // Act
            var result = await _productFeatureQueryHandler.GetProductFeatureByIdAsync(productFeatureId);

            // Assert
            Assert.That(result, Is.Null);
            _mockProductFeatureRepository.Verify(r => r.GetByIdAsync(productFeatureId), Times.Once);
        }

        [Test]
        public async Task GetAllProductFeaturesAsync_ShouldReturnAllProductFeatures()
        {
            // Arrange
            var productFeatures = new List<ProductFeature>
            {
                new ProductFeature
                {
                    Id = Guid.NewGuid(),
                    FeatureName = "Advanced Reporting",
                    ProdId = Guid.NewGuid(),
                    ProdName = "Premium Product",
                    Status = true,
                    Created = DateTime.Now.AddDays(-30)
                },
                new ProductFeature
                {
                    Id = Guid.NewGuid(),
                    FeatureName = "Data Export",
                    ProdId = Guid.NewGuid(),
                    ProdName = "Premium Product",
                    Status = true,
                    Created = DateTime.Now.AddDays(-25)
                },
                new ProductFeature
                {
                    Id = Guid.NewGuid(),
                    FeatureName = "User Management",
                    ProdId = Guid.NewGuid(),
                    ProdName = "Basic Product",
                    Status = true,
                    Created = DateTime.Now.AddDays(-20)
                }
            };

            _mockProductFeatureRepository
                .Setup(r => r.GetAllAsync())
                .ReturnsAsync(productFeatures);

            // Act
            var result = await _productFeatureQueryHandler.GetAllProductFeaturesAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(3));
            Assert.That(result.Select(p => p.FeatureName).ToList(),
                Contains.Item("Advanced Reporting").And.Contains("Data Export").And.Contains("User Management"));

            _mockProductFeatureRepository.Verify(r => r.GetAllAsync(), Times.Once);
        }

        [Test]
        public void Constructor_ShouldThrowArgumentNullException_WhenUnitOfWorkIsNull()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new ProductFeatureQueryHandler(null));
        }
    }
}
