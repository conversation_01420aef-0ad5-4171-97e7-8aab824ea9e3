﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Contracts;
using DataAccessLayer.Context;
using Interfaces.ShardManagement;
using Microsoft.EntityFrameworkCore;

namespace MemberServiceDataAccessLayer.Implementation
{
    public class PagePathRepository : IPagePathRepository
    {
        private readonly AccountDatabaseContext _context;

        public PagePathRepository(AccountDatabaseContext context, ShardMapManagerService<AccountDatabaseContext, AccountDatabaseContext> _shardMapManagerService)
        {
            _context = context;
        }

        public async Task AddAsync(PagePath pagePath)
        {
            await _context.PagePaths.AddAsync(pagePath);
        }

        public async Task UpdateAsync(PagePath pagePath)
        {
            _context.PagePaths.Update(pagePath);
            await Task.CompletedTask;
        }

        public async Task DeleteByIdAsync(Guid id)
        {
            var pagePath = await _context.PagePaths.FindAsync(id);
            if (pagePath != null)
            {
                _context.PagePaths.Remove(pagePath);
            }
        }

        public async Task<PagePath> GetByIdAsync(Guid id)
        {
            var result = await _context.PagePaths.FindAsync(id);
            return result;
        }

        public async Task<List<PagePath>> GetAllAsync()
        {
            var result = await _context.PagePaths.ToListAsync();
            return result;
        }

        public async Task<List<PagePath>> GetByKeywordAsync(string keyword)
        {
            var result = await _context.PagePaths
                .Where(pp => pp.PagePathValue.Contains(keyword, StringComparison.OrdinalIgnoreCase))
                .ToListAsync();
            return result;
        }
    }
}
