using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using MemberServiceApi.Controllers;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using MemberServiceBusinessLayer;
using System.Linq;

namespace MemberServiceApiControllerTestCases
{
    [TestFixture]
    public class PreDefinedPageRoleMappingControllerTests
    {
        private Mock<IPreDefinedPageRoleMappingQueryHandler<PreDefinedPageRoleMapping>> _mockQueryHandler;
        private Mock<IPreDefinedPageRoleMappingCommandHandler<PreDefinedPageRoleMapping>> _mockCommandHandler;
        private Mock<ILogger<PreDefinedPageRoleMappingController>> _mockLogger;
        private Mock<IStringLocalizer<PreDefinedPageRoleMappingController>> _mockLocalizer;
        private PreDefinedPageRoleMappingController _controller;

        [SetUp]
        public void Setup()
        {
            _mockQueryHandler = new Mock<IPreDefinedPageRoleMappingQueryHandler<PreDefinedPageRoleMapping>>();
            _mockCommandHandler = new Mock<IPreDefinedPageRoleMappingCommandHandler<PreDefinedPageRoleMapping>>();
            _mockLogger = new Mock<ILogger<PreDefinedPageRoleMappingController>>();
            _mockLocalizer = new Mock<IStringLocalizer<PreDefinedPageRoleMappingController>>();

            // Setup localizer to return the key when requested
            _mockLocalizer.Setup(l => l[It.IsAny<string>()]).Returns((string key) => new LocalizedString(key, key));

            _controller = new PreDefinedPageRoleMappingController(
                _mockQueryHandler.Object,
                _mockCommandHandler.Object,
                _mockLogger.Object,
                _mockLocalizer.Object
            );
        }

        [Test]
        public async Task GetAllPreDefinedPageRoleMappings_WhenMappingsExist_ReturnsOkWithMappings()
        {
            // Arrange
            var mockMappings = new List<PreDefinedPageRoleMapping>
            {
                new PreDefinedPageRoleMapping { Id = Guid.NewGuid(), PagePath = "/dashboard", RoleId = Guid.NewGuid(), RoleName = "Admin" },
                new PreDefinedPageRoleMapping { Id = Guid.NewGuid(), PagePath = "/users", RoleId = Guid.NewGuid(), RoleName = "User" }
            };

            _mockQueryHandler
                .Setup(q => q.GetAllPreDefinedPageRoleMappingsAsync())
                .ReturnsAsync(mockMappings);

            // Act
            var result = await _controller.GetAllPreDefinedPageRoleMappings();

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult?.StatusCode, Is.EqualTo(200));
            Assert.That(okResult?.Value, Is.EqualTo(mockMappings));
        }

        [Test]
        public async Task GetAllPreDefinedPageRoleMappings_WhenNoMappingsExist_ReturnsNotFound()
        {
            // Arrange
            _mockQueryHandler
                .Setup(q => q.GetAllPreDefinedPageRoleMappingsAsync())
                .ReturnsAsync(new List<PreDefinedPageRoleMapping>());

            // Act
            var result = await _controller.GetAllPreDefinedPageRoleMappings();

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult?.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task GetAllPreDefinedPageRoleMappings_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            _mockQueryHandler
                .Setup(q => q.GetAllPreDefinedPageRoleMappingsAsync())
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetAllPreDefinedPageRoleMappings();

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task GetPreDefinedPageRoleMappingById_WhenMappingExists_ReturnsOkWithMapping()
        {
            // Arrange
            var mappingId = Guid.NewGuid();
            var mockMapping = new PreDefinedPageRoleMapping
            {
                Id = mappingId,
                PagePath = "/dashboard",
                RoleId = Guid.NewGuid(),
                RoleName = "Admin"
            };

            _mockQueryHandler
                .Setup(q => q.GetPreDefinedPageRoleMappingByIdAsync(mappingId))
                .ReturnsAsync(mockMapping);

            // Act
            var result = await _controller.GetPreDefinedPageRoleMappingById(mappingId);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult?.StatusCode, Is.EqualTo(200));
            Assert.That(okResult?.Value, Is.EqualTo(mockMapping));
        }

        [Test]
        public async Task GetPreDefinedPageRoleMappingById_WhenMappingDoesNotExist_ReturnsNotFound()
        {
            // Arrange
            var mappingId = Guid.NewGuid();

            _mockQueryHandler
                .Setup(q => q.GetPreDefinedPageRoleMappingByIdAsync(mappingId))
                .ReturnsAsync((PreDefinedPageRoleMapping)null);

            // Act
            var result = await _controller.GetPreDefinedPageRoleMappingById(mappingId);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult?.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task GetPreDefinedPageRoleMappingById_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var mappingId = Guid.NewGuid();

            _mockQueryHandler
                .Setup(q => q.GetPreDefinedPageRoleMappingByIdAsync(mappingId))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetPreDefinedPageRoleMappingById(mappingId);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task GetPagesByRoleName_WhenPagesExist_ReturnsOkWithPages()
        {
            // Arrange
            var roleName = "Admin";
            var mockPages = new List<PreDefinedPageRoleMapping>
            {
                new PreDefinedPageRoleMapping { Id = Guid.NewGuid(), PagePath = "/dashboard", RoleId = Guid.NewGuid(), RoleName = roleName },
                new PreDefinedPageRoleMapping { Id = Guid.NewGuid(), PagePath = "/users", RoleId = Guid.NewGuid(), RoleName = roleName }
            };

            _mockQueryHandler
                .Setup(q => q.GetPagesByRoleIdAsync(roleName))
                .ReturnsAsync(mockPages);

            // Act
            var result = await _controller.GetPagesByRoleName(roleName);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult?.StatusCode, Is.EqualTo(200));
            Assert.That(okResult?.Value, Is.EqualTo(mockPages));
        }

        [Test]
        public async Task GetPagesByRoleName_WhenNoPagesExist_ReturnsNotFound()
        {
            // Arrange
            var roleName = "NonexistentRole";

            _mockQueryHandler
                .Setup(q => q.GetPagesByRoleIdAsync(roleName))
                .ReturnsAsync(new List<PreDefinedPageRoleMapping>());

            // Act
            var result = await _controller.GetPagesByRoleName(roleName);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult?.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task GetPagesByRoleName_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var roleName = "Admin";

            _mockQueryHandler
                .Setup(q => q.GetPagesByRoleIdAsync(roleName))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetPagesByRoleName(roleName);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task GetPreDefinedPageRoleMappingByPagePath_WhenMappingsExist_ReturnsOkWithMappings()
        {
            // Arrange
            var pagePath = "/dashboard";
            var mockMappings = new List<PreDefinedPageRoleMapping>
            {
                new PreDefinedPageRoleMapping { Id = Guid.NewGuid(), PagePath = pagePath, RoleId = Guid.NewGuid(), RoleName = "Admin" },
                new PreDefinedPageRoleMapping { Id = Guid.NewGuid(), PagePath = pagePath, RoleId = Guid.NewGuid(), RoleName = "User" }
            };

            _mockQueryHandler
                .Setup(q => q.GetPreDefinedPageRoleMappingsByPagePathAsync(pagePath))
                .ReturnsAsync(mockMappings);

            // Act
            var result = await _controller.GetPreDefinedPageRoleMappingByPagePath(pagePath);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult?.StatusCode, Is.EqualTo(200));
            Assert.That(okResult?.Value, Is.EqualTo(mockMappings));
        }

        [Test]
        public async Task GetPreDefinedPageRoleMappingByPagePath_WhenNoMappingsExist_ReturnsNotFound()
        {
            // Arrange
            var pagePath = "/nonexistent";

            _mockQueryHandler
                .Setup(q => q.GetPreDefinedPageRoleMappingsByPagePathAsync(pagePath))
                .ReturnsAsync(new List<PreDefinedPageRoleMapping>());

            // Act
            var result = await _controller.GetPreDefinedPageRoleMappingByPagePath(pagePath);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult?.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task GetPreDefinedPageRoleMappingByPagePath_WhenPagePathIsEmpty_ReturnsBadRequest()
        {
            // Arrange
            string pagePath = "";

            // Act
            var result = await _controller.GetPreDefinedPageRoleMappingByPagePath(pagePath);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult?.StatusCode, Is.EqualTo(400));
        }

        [Test]
        public async Task GetPreDefinedPageRoleMappingByPagePath_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var pagePath = "/dashboard";

            _mockQueryHandler
                .Setup(q => q.GetPreDefinedPageRoleMappingsByPagePathAsync(pagePath))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetPreDefinedPageRoleMappingByPagePath(pagePath);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task AddPreDefinedPageRoleMapping_WhenValidMapping_ReturnsCreatedAtAction()
        {
            // Arrange
            var mappingId = Guid.NewGuid();
            var mapping = new PreDefinedPageRoleMapping
            {
                Id = mappingId,
                PagePath = "/dashboard",
                RoleId = Guid.NewGuid(),
                RoleName = "Admin",
                HasAccess = true
            };

            _mockCommandHandler
                .Setup(c => c.AddPreDefinedPageRoleMappingAsync(mapping))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.AddPreDefinedPageRoleMapping(mapping);

            // Assert
            Assert.That(result, Is.InstanceOf<CreatedAtActionResult>());
            var createdResult = result as CreatedAtActionResult;
            Assert.That(createdResult?.StatusCode, Is.EqualTo(201));
            Assert.That(createdResult?.ActionName, Is.EqualTo(nameof(PreDefinedPageRoleMappingController.GetPreDefinedPageRoleMappingById)));
            Assert.That(createdResult?.RouteValues["id"], Is.EqualTo(mappingId));
            Assert.That(createdResult?.Value, Is.EqualTo(mapping));
        }

        [Test]
        public async Task AddPreDefinedPageRoleMapping_WhenNullMapping_ReturnsBadRequest()
        {
            // Act
            var result = await _controller.AddPreDefinedPageRoleMapping(null);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult?.StatusCode, Is.EqualTo(400));
        }

        [Test]
        public async Task AddPreDefinedPageRoleMapping_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var mappingId = Guid.NewGuid();
            var mapping = new PreDefinedPageRoleMapping
            {
                Id = mappingId,
                PagePath = "/dashboard",
                RoleId = Guid.NewGuid(),
                RoleName = "Admin",
                HasAccess = true
            };

            _mockCommandHandler
                .Setup(c => c.AddPreDefinedPageRoleMappingAsync(mapping))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.AddPreDefinedPageRoleMapping(mapping);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }
    }
}
