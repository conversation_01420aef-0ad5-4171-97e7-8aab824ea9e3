using NUnit.Framework;
using Moq;
using MemberServiceBusinessLayer.CommandHandler;
using MemberServiceDataAccessLayer.Implementation;
using Contracts;
using System;
using System.Threading.Tasks;

namespace MemberServiceApiBusinessLayerTestCases.CommandHandlerTests
{
    [TestFixture]
    public class ProductFeatureCommandHandlerTests
    {
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<IProductFeatureRepository> _mockProductFeatureRepository;
        private ProductFeatureCommandHandler _productFeatureCommandHandler;

        [SetUp]
        public void Setup()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockProductFeatureRepository = new Mock<IProductFeatureRepository>();

            _mockUnitOfWork.Setup(u => u.ProductFeatureRepository).Returns(_mockProductFeatureRepository.Object);

            _productFeatureCommandHandler = new ProductFeatureCommandHandler(_mockUnitOfWork.Object);
        }

        [Test]
        public async Task AddProductFeatureAsync_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var productFeature = new ProductFeature
            {
                Id = Guid.NewGuid(),
                FeatureName = "Advanced Reporting",
                ProdId = Guid.NewGuid(),
                ProdName = "Premium Product",
                Status = true,
                Created = DateTime.Now
            };

            _mockProductFeatureRepository
                .Setup(r => r.AddAsync(productFeature))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _productFeatureCommandHandler.AddProductFeatureAsync(productFeature);

            // Assert
            _mockProductFeatureRepository.Verify(r => r.AddAsync(productFeature), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateProductFeatureAsync_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var productFeature = new ProductFeature
            {
                Id = Guid.NewGuid(),
                FeatureName = "Advanced Reporting",
                ProdId = Guid.NewGuid(),
                ProdName = "Premium Product",
                Status = true,
                Created = DateTime.Now,
                Updated = DateTime.Now
            };

            _mockProductFeatureRepository
                .Setup(r => r.UpdateAsync(productFeature))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _productFeatureCommandHandler.UpdateProductFeatureAsync(productFeature);

            // Assert
            _mockProductFeatureRepository.Verify(r => r.UpdateAsync(productFeature), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteProductFeatureAsync_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var productFeatureId = Guid.NewGuid();

            _mockProductFeatureRepository
                .Setup(r => r.DeleteByIdAsync(productFeatureId))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _productFeatureCommandHandler.DeleteProductFeatureAsync(productFeatureId);

            // Assert
            _mockProductFeatureRepository.Verify(r => r.DeleteByIdAsync(productFeatureId), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public void AddProductFeatureAsync_ShouldThrowArgumentNullException_WhenProductFeatureIsNull()
        {
            // Act & Assert
            Assert.ThrowsAsync<ArgumentNullException>(async () =>
                await _productFeatureCommandHandler.AddProductFeatureAsync(null));

            _mockProductFeatureRepository.Verify(r => r.AddAsync(It.IsAny<ProductFeature>()), Times.Never);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Never);
        }

        [Test]
        public void UpdateProductFeatureAsync_ShouldThrowArgumentNullException_WhenProductFeatureIsNull()
        {
            // Act & Assert
            Assert.ThrowsAsync<ArgumentNullException>(async () =>
                await _productFeatureCommandHandler.UpdateProductFeatureAsync(null));

            _mockProductFeatureRepository.Verify(r => r.UpdateAsync(It.IsAny<ProductFeature>()), Times.Never);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Never);
        }

    }
}
