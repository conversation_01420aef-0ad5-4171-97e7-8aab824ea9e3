using Microsoft.Extensions.Configuration;
using Moq;
using PracticeBusinessLayer.QueryHandler;
using PracticeContracts;
using PracticeDataAccessLayer.Implementation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace PracticeApiBusinessLayerTestCases
{
    [TestFixture]
    public class PracticeQueryHandlerTests
    {
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<IPracticeRepository> _mockPracticeRepository;
        private PracticeQueryHandler _queryHandler;
        private Guid _testOrgId;
        private bool _testSubscription;

        [SetUp]
        public void Setup()
        {
            // Initialize mocks
            _mockConfiguration = new Mock<IConfiguration>();
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockPracticeRepository = new Mock<IPracticeRepository>();

            // Setup repository mock
            _mockUnitOfWork.Setup(u => u.PracticeRepository).Returns(_mockPracticeRepository.Object);

            // Create query handler with mocked dependencies
            _queryHandler = new PracticeQueryHandler(
                _mockConfiguration.Object,
                _mockUnitOfWork.Object
            );

            // Test data
            _testOrgId = Guid.NewGuid();
            _testSubscription = true;
        }

        [Test]
        public async Task GetTasks_ShouldReturnAllTasks()
        {
            // Arrange
            var expectedTasks = new List<Tasks>
            {
                new Tasks { Id = Guid.NewGuid(), PatientName = "Patient 1", TaskType = "Appointment", Subject = "Initial Consultation" },
                new Tasks { Id = Guid.NewGuid(), PatientName = "Patient 2", TaskType = "Follow-up", Subject = "Medication Review" },
                new Tasks { Id = Guid.NewGuid(), PatientName = "Patient 3", TaskType = "Lab", Subject = "Blood Test Results" }
            };

            _mockPracticeRepository.Setup(r => r.GetAllAsync())
                .ReturnsAsync(expectedTasks);

            // Act
            var result = await _queryHandler.GetTasks();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.EqualTo(expectedTasks));
            _mockPracticeRepository.Verify(r => r.GetAllAsync(), Times.Once);
        }

        [Test]
        public async Task GetTasks_WhenNoTasks_ShouldReturnEmptyList()
        {
            // Arrange
            var emptyList = new List<Tasks>();
            _mockPracticeRepository.Setup(r => r.GetAllAsync())
                .ReturnsAsync(emptyList);

            // Act
            var result = await _queryHandler.GetTasks();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.Empty);
            _mockPracticeRepository.Verify(r => r.GetAllAsync(), Times.Once);
        }

        [Test]
        public async Task GetTaskById_ShouldReturnCorrectTask()
        {
            // Arrange
            var taskId = Guid.NewGuid();
            var expectedTask = new Tasks {
                Id = taskId,
                PatientName = "Test Patient",
                TaskType = "Consultation",
                Subject = "Annual Physical",
                OrganizationId = _testOrgId,
                Subscription = _testSubscription
            };

            _mockPracticeRepository.Setup(r => r.GetByIdAsync(taskId, _testOrgId, _testSubscription))
                .ReturnsAsync(expectedTask);

            // Act
            var result = await _queryHandler.GetTaskById(taskId, _testOrgId, _testSubscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(taskId));
            Assert.That(result, Is.EqualTo(expectedTask));
            _mockPracticeRepository.Verify(r => r.GetByIdAsync(taskId, _testOrgId, _testSubscription), Times.Once);
        }

        [Test]
        public async Task GetTaskById_WhenTaskNotFound_ShouldReturnNull()
        {
            // Arrange
            var taskId = Guid.NewGuid();

            _mockPracticeRepository.Setup(r => r.GetByIdAsync(taskId, _testOrgId, _testSubscription))
                .ReturnsAsync((Tasks)null);

            // Act
            var result = await _queryHandler.GetTaskById(taskId, _testOrgId, _testSubscription);

            // Assert
            Assert.That(result, Is.Null);
            _mockPracticeRepository.Verify(r => r.GetByIdAsync(taskId, _testOrgId, _testSubscription), Times.Once);
        }

        [Test]
        public async Task GetTaskById_ShouldPassCorrectParameters()
        {
            // Arrange
            var taskId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = false;

            _mockPracticeRepository.Setup(r => r.GetByIdAsync(taskId, orgId, subscription))
                .ReturnsAsync(new Tasks());

            // Act
            await _queryHandler.GetTaskById(taskId, orgId, subscription);

            // Assert
            _mockPracticeRepository.Verify(r => r.GetByIdAsync(taskId, orgId, subscription), Times.Once);
        }
    }
}
