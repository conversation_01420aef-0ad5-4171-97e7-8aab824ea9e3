using Contracts;
using Microsoft.AspNetCore.Mvc;
using MemberServiceBusinessLayer.CommandHandler;  
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MemberServiceBusinessLayer;

namespace MemberServiceApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ProductOrganizationMappingController : ControllerBase
    {
        private readonly IProductOrganizationMappingsQueryHandler<ProductOrganizationMapping> _ProductOrganizationMappingsQueryHandler;
        private readonly IProductOrganizationMappingsCommandHandler<ProductOrganizationMapping> _ProductOrganizationMappingsCommandHandler;
        private readonly ILogger<ProductOrganizationMappingController> _logger;
        private readonly IStringLocalizer<ProductOrganizationMappingController> _localizer;

        public ProductOrganizationMappingController(
            IProductOrganizationMappingsQueryHandler<ProductOrganizationMapping> ProductOrganizationMappingsQueryHandler,
            IProductOrganizationMappingsCommandHandler<ProductOrganizationMapping> ProductOrganizationMappingsCommandHandler,
            ILogger<ProductOrganizationMappingController> logger,
            IStringLocalizer<ProductOrganizationMappingController> localizer)
        {
            _ProductOrganizationMappingsQueryHandler = ProductOrganizationMappingsQueryHandler ?? throw new ArgumentNullException(nameof(ProductOrganizationMappingsQueryHandler));
            _ProductOrganizationMappingsCommandHandler = ProductOrganizationMappingsCommandHandler ?? throw new ArgumentNullException(nameof(ProductOrganizationMappingsCommandHandler));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _localizer = localizer ?? throw new ArgumentNullException(nameof(localizer));
        }

        /// <summary>
        /// Gets an ProductOrganizationMapping by its ID.
        /// </summary>
        /// <param name="id">The ID of the ProductOrganizationMapping.</param>
        /// <returns>The ProductOrganizationMapping with the specified ID.</returns>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetProductOrganizationMappingById(Guid id)
        {
            _logger.LogInformation(_localizer["Fetching ProductOrganizationMapping with ID: {0}"], id);
            var ProductOrganizationMapping = await _ProductOrganizationMappingsQueryHandler.GetProductOrganizationMappingByIdAsync(id);

            IActionResult result;
            if (ProductOrganizationMapping == null)
            {
                _logger.LogWarning(_localizer["ProductOrganizationMapping with ID: {0} not found"], id);
                result = NotFound(_localizer["ProductOrganizationMappingNotFound"]);
            }
            else
            {
                _logger.LogInformation(_localizer["ProductOrganizationMapping with ID: {0} fetched successfully"], id);
                result = Ok(ProductOrganizationMapping);
            }

            return result;
        }

        /// <summary>
        /// Adds a new ProductOrganizationMapping.
        /// </summary>
        /// <param name="ProductOrganizationMapping">The ProductOrganizationMapping to add.</param>
        /// <returns>The added ProductOrganizationMapping.</returns>
        [HttpPost]
        public async Task<IActionResult> AddProductOrganizationMapping([FromBody] ProductOrganizationMapping ProductOrganizationMapping)
        {
            IActionResult result;

            if (ProductOrganizationMapping == null)
            {
                _logger.LogWarning(_localizer["Invalid ProductOrganizationMapping data received"]);
                result = BadRequest(_localizer["ProductOrganizationMappingInvalid"]);
            }
            else
            {
                try
                {
                    _logger.LogInformation(_localizer["Adding new ProductOrganizationMapping"]);
                    Guid id = ProductOrganizationMapping.ProductOrganizationMappingId;
                    await _ProductOrganizationMappingsCommandHandler.AddProductOrganizationMappingAsync(new List<ProductOrganizationMapping> { ProductOrganizationMapping }, id);
                    _logger.LogInformation(_localizer["ProductOrganizationMapping added successfully with ID: {0}"], id);
                    result = CreatedAtAction(nameof(GetProductOrganizationMappingById), new { id }, ProductOrganizationMapping);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["ErrorAddingProductOrganizationMapping"]);
                    result = StatusCode(500, _localizer["InternalServerError"]);
                }
            }

            return result;
        }

        /// <summary>
        /// Updates an existing ProductOrganizationMapping.
        /// </summary>
        /// <param name="id">The ID of the ProductOrganizationMapping to update.</param>
        /// <param name="ProductOrganizationMapping">The updated ProductOrganizationMapping data.</param>
        /// <returns>No content if the update is successful.</returns>
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateProductOrganizationMapping(Guid id, [FromBody] ProductOrganizationMapping ProductOrganizationMapping)
        {
            IActionResult result;

            if (ProductOrganizationMapping == null || id != ProductOrganizationMapping.ProductOrganizationMappingId)
            {
                _logger.LogWarning(_localizer["Invalid data for updating ProductOrganizationMapping with ID: {0}"], id);
                result = BadRequest(_localizer["ProductOrganizationMappingInvalid"]);
            }
            else
            {
                try
                {
                    _logger.LogInformation(_localizer["Updating ProductOrganizationMapping with ID: {0}"], id);
                    await _ProductOrganizationMappingsCommandHandler.UpdateProductOrganizationMappingAsync(ProductOrganizationMapping);
                    _logger.LogInformation(_localizer["ProductOrganizationMapping with ID: {0} updated successfully"], id);
                    result = NoContent();
                }
                catch (KeyNotFoundException)
                {
                    _logger.LogWarning(_localizer["ProductOrganizationMapping with ID: {0} not found for update"], id);
                    result = NotFound(_localizer["ProductOrganizationMappingNotFound"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["ErrorUpdatingProductOrganizationMapping"]);
                    result = StatusCode(500, _localizer["InternalServerError"]);
                }
            }

            return result;
        }

        /// <summary>
        /// Deletes an ProductOrganizationMapping by its ID.
        /// </summary>
        /// <param name="id">The ID of the ProductOrganizationMapping to delete.</param>
        /// <returns>No content if the deletion is successful.</returns>
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteProductOrganizationMapping(Guid id)
        {
            IActionResult result;

            try
            {
                _logger.LogInformation(_localizer["Deleting ProductOrganizationMapping with ID: {0}"], id);
                await _ProductOrganizationMappingsCommandHandler.DeleteProductOrganizationMappingAsync(id);
                _logger.LogInformation(_localizer["ProductOrganizationMapping with ID: {0} deleted successfully"], id);
                result = NoContent();
            }
            catch (KeyNotFoundException)
            {
                _logger.LogWarning(_localizer["ProductOrganizationMapping with ID: {0} not found for deletion"], id);
                result = NotFound(_localizer["ProductOrganizationMappingNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorDeletingProductOrganizationMapping"]);
                result = StatusCode(500, _localizer["InternalServerError"]);
            }

            return result;
        }

        /// <summary>
        /// Gets all ProductOrganizationMappings.
        /// </summary>
        /// <returns>A list of all ProductOrganizationMappings.</returns>
        [HttpGet]
        public async Task<IActionResult> GetAllProductOrganizationMappings()
        {
            IActionResult result;

            try
            {
                _logger.LogInformation(_localizer["Fetching all ProductOrganizationMappings"]);
                var mappings = await _ProductOrganizationMappingsQueryHandler.GetAllProductOrganizationMappingsAsync();

                if (mappings == null || mappings.Count == 0)
                {
                    _logger.LogWarning(_localizer["No ProductOrganizationMappings found"]);
                    result = NotFound(_localizer["ProductOrganizationMappingNotFound"]);
                }
                else
                {
                    _logger.LogInformation(_localizer["ProductOrganizationMappings fetched successfully"]);
                    result = Ok(mappings);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingProductOrganizationMappings"]);
                result = StatusCode(500, _localizer["InternalServerError"]);
            }

            return result;
        }

        /// <summary>
        /// Gets all ProductOrganizationMappings for a specific ProductId.
        /// </summary>
        /// <param name="productId">The ProductId to filter mappings.</param>
        /// <returns>A list of ProductOrganizationMappings for the given ProductId.</returns>
        [HttpGet("product/{productId}")]
        public async Task<IActionResult> GetMappingsByProductId(Guid productId)
        {
            IActionResult result;

            try
            {
                _logger.LogInformation(_localizer["Fetching ProductOrganizationMappings for ProductId: {0}"], productId);
                var allMappings = await _ProductOrganizationMappingsQueryHandler.GetAllProductOrganizationMappingsAsync();
                var filtered = allMappings?.FindAll(m => m.ProductId == productId);

                if (filtered == null || filtered.Count == 0)
                {
                    _logger.LogWarning(_localizer["No ProductOrganizationMappings found for ProductId: {0}"], productId);
                    result = NotFound(_localizer["ProductOrganizationMappingNotFound"]);
                }
                else
                {
                    _logger.LogInformation(_localizer["ProductOrganizationMappings for ProductId: {0} fetched successfully"], productId);
                    result = Ok(filtered);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingMappingsByProductId"], productId);
                result = StatusCode(500, _localizer["InternalServerError"]);
            }

            return result;
        }
    }
}
