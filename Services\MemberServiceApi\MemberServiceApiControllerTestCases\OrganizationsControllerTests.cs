using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using MemberServiceApi.Controllers;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using MemberServiceBusinessLayer;

namespace MemberServiceApiControllerTestCases
{
    [TestFixture]
    public class OrganizationsControllerTests
    {
        private Mock<IOrganizationsQueryHandler<Organization>> _mockQueryHandler;
        private Mock<IOrganizationsCommandHandler<Organization>> _mockCommandHandler;
        private Mock<ILogger<OrganizationsController>> _mockLogger;
        private Mock<IStringLocalizer<OrganizationsController>> _mockLocalizer;
        private OrganizationsController _controller;

        [SetUp]
        public void Setup()
        {
            _mockQueryHandler = new Mock<IOrganizationsQueryHandler<Organization>>();
            _mockCommandHandler = new Mock<IOrganizationsCommandHandler<Organization>>();
            _mockLogger = new Mock<ILogger<OrganizationsController>>();
            _mockLocalizer = new Mock<IStringLocalizer<OrganizationsController>>();

            // Setup localizer to return the key when requested
            _mockLocalizer.Setup(l => l[It.IsAny<string>()]).Returns((string key) => new LocalizedString(key, key));

            _controller = new OrganizationsController(
                _mockQueryHandler.Object,
                _mockCommandHandler.Object,
                _mockLogger.Object,
                _mockLocalizer.Object
            );
        }

        [Test]
        public async Task GetAllOrganizations_WhenOrganizationsExist_ReturnsOkWithOrganizations()
        {
            // Arrange
            var mockOrganizations = new List<Organization>
            {
                new Organization { OrganizationId = Guid.NewGuid(), OrganizationName = "Org 1" },
                new Organization { OrganizationId = Guid.NewGuid(), OrganizationName = "Org 2" }
            };

            _mockQueryHandler
                .Setup(q => q.GetAllOrganizationsAsync())
                .ReturnsAsync(mockOrganizations);

            // Act
            var result = await _controller.GetAllOrganizations();

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult.StatusCode, Is.EqualTo(200));
            Assert.That(okResult.Value, Is.EqualTo(mockOrganizations));
        }

        [Test]
        public async Task GetAllOrganizations_WhenNoOrganizationsExist_ReturnsNotFound()
        {
            // Arrange
            _mockQueryHandler
                .Setup(q => q.GetAllOrganizationsAsync())
                .ReturnsAsync(new List<Organization>());

            // Act
            var result = await _controller.GetAllOrganizations();

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task GetAllOrganizations_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            _mockQueryHandler
                .Setup(q => q.GetAllOrganizationsAsync())
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetAllOrganizations();

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task GetOrganizationById_WhenOrganizationExists_ReturnsOkWithOrganization()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            var mockOrganization = new Organization
            {
                OrganizationId = organizationId,
                OrganizationName = "Org 1"
            };

            _mockQueryHandler
                .Setup(q => q.GetOrganizationByIdAsync(organizationId))
                .ReturnsAsync(mockOrganization);

            // Act
            var result = await _controller.GetOrganizationById(organizationId);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult.StatusCode, Is.EqualTo(200));
            Assert.That(okResult.Value, Is.EqualTo(mockOrganization));
        }

        [Test]
        public async Task GetOrganizationById_WhenOrganizationDoesNotExist_ReturnsNotFound()
        {
            // Arrange
            var organizationId = Guid.NewGuid();

            _mockQueryHandler
                .Setup(q => q.GetOrganizationByIdAsync(organizationId))
                .ReturnsAsync((Organization)null);

            // Act
            var result = await _controller.GetOrganizationById(organizationId);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task GetOrganizationById_WhenExceptionOccurs_ThrowsException()
        {
            // Arrange
            var organizationId = Guid.NewGuid();

            _mockQueryHandler
                .Setup(q => q.GetOrganizationByIdAsync(organizationId))
                .ThrowsAsync(new Exception("Test exception"));

            // Act & Assert
            try
            {
                await _controller.GetOrganizationById(organizationId);
                Assert.Fail("Expected exception was not thrown");
            }
            catch (Exception ex)
            {
                Assert.That(ex.Message, Is.EqualTo("Test exception"));
            }
        }

        [Test]
        public async Task AddOrganization_WhenValidOrganization_ReturnsCreatedAtAction()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            var organization = new Organization
            {
                OrganizationId = organizationId,
                OrganizationName = "New Org"
            };

            _mockCommandHandler
                .Setup(c => c.AddOrganizationAsync(It.IsAny<List<Organization>>(), organizationId))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.AddOrganization(organization);

            // Assert
            Assert.That(result, Is.InstanceOf<CreatedAtActionResult>());
            var createdResult = result as CreatedAtActionResult;
            Assert.That(createdResult.StatusCode, Is.EqualTo(201));
            Assert.That(createdResult.ActionName, Is.EqualTo(nameof(OrganizationsController.GetOrganizationById)));
            Assert.That(createdResult.RouteValues["id"], Is.EqualTo(organizationId));
            Assert.That(createdResult.Value, Is.EqualTo(organization));
        }

        [Test]
        public async Task AddOrganization_WhenNullOrganization_ReturnsBadRequest()
        {
            // Act
            var result = await _controller.AddOrganization(null);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult.StatusCode, Is.EqualTo(400));
        }

        [Test]
        public async Task AddOrganization_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var organization = new Organization
            {
                OrganizationId = Guid.NewGuid(),
                OrganizationName = "New Org"
            };

            _mockCommandHandler
                .Setup(c => c.AddOrganizationAsync(It.IsAny<List<Organization>>(), organization.OrganizationId))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.AddOrganization(organization);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task UpdateOrganization_WhenValidOrganization_ReturnsNoContent()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            var organization = new Organization
            {
                OrganizationId = organizationId,
                OrganizationName = "Updated Org"
            };

            _mockCommandHandler
                .Setup(c => c.UpdateOrganizationAsync(organization))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.UpdateOrganization(organizationId, organization);

            // Assert
            Assert.That(result, Is.InstanceOf<NoContentResult>());
            var noContentResult = result as NoContentResult;
            Assert.That(noContentResult.StatusCode, Is.EqualTo(204));
        }

        [Test]
        public async Task UpdateOrganization_WhenIdMismatch_ReturnsBadRequest()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            var organization = new Organization
            {
                OrganizationId = Guid.NewGuid(), // Different ID
                OrganizationName = "Updated Org"
            };

            // Act
            var result = await _controller.UpdateOrganization(organizationId, organization);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult.StatusCode, Is.EqualTo(400));
        }

        [Test]
        public async Task UpdateOrganization_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            var organization = new Organization
            {
                OrganizationId = organizationId,
                OrganizationName = "Updated Org"
            };

            _mockCommandHandler
                .Setup(c => c.UpdateOrganizationAsync(organization))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.UpdateOrganization(organizationId, organization);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task DeleteOrganization_WhenOrganizationExists_ReturnsNoContent()
        {
            // Arrange
            var organizationId = Guid.NewGuid();

            _mockCommandHandler
                .Setup(c => c.DeleteOrganizationAsync(organizationId))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.DeleteOrganization(organizationId);

            // Assert
            Assert.That(result, Is.InstanceOf<NoContentResult>());
            var noContentResult = result as NoContentResult;
            Assert.That(noContentResult.StatusCode, Is.EqualTo(204));
        }

        [Test]
        public async Task DeleteOrganization_WhenOrganizationDoesNotExist_ReturnsNotFound()
        {
            // Arrange
            var organizationId = Guid.NewGuid();

            _mockCommandHandler
                .Setup(c => c.DeleteOrganizationAsync(organizationId))
                .ThrowsAsync(new KeyNotFoundException());

            // Act
            var result = await _controller.DeleteOrganization(organizationId);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task DeleteOrganization_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var organizationId = Guid.NewGuid();

            _mockCommandHandler
                .Setup(c => c.DeleteOrganizationAsync(organizationId))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.DeleteOrganization(organizationId);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task GetOrganizationByName_WhenOrganizationsExist_ReturnsOkWithOrganizations()
        {
            // Arrange
            var name = "Test Organization";
            var mockOrganizations = new List<Organization>
            {
                new Organization { OrganizationId = Guid.NewGuid(), OrganizationName = "Test Organization" },
                new Organization { OrganizationId = Guid.NewGuid(), OrganizationName = "Test Organization 2" }
            };

            _mockQueryHandler
                .Setup(q => q.GetOrganizationsByNameAsync(name))
                .ReturnsAsync(mockOrganizations);

            // Act
            var result = await _controller.GetOrganizationByName(name);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult.StatusCode, Is.EqualTo(200));
            Assert.That(okResult.Value, Is.EqualTo(mockOrganizations));
        }

        [Test]
        public async Task GetOrganizationByName_WhenNoOrganizationsExist_ReturnsNotFound()
        {
            // Arrange
            var name = "Nonexistent Organization";

            _mockQueryHandler
                .Setup(q => q.GetOrganizationsByNameAsync(name))
                .ReturnsAsync(new List<Organization>());

            // Act
            var result = await _controller.GetOrganizationByName(name);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task GetOrganizationByName_WhenNameIsEmpty_ReturnsBadRequest()
        {
            // Arrange
            string name = "";

            // Act
            var result = await _controller.GetOrganizationByName(name);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult.StatusCode, Is.EqualTo(400));
        }

        [Test]
        public async Task GetOrganizationByName_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var name = "Test Organization";

            _mockQueryHandler
                .Setup(q => q.GetOrganizationsByNameAsync(name))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetOrganizationByName(name);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult.StatusCode, Is.EqualTo(500));
        }
    }
}
