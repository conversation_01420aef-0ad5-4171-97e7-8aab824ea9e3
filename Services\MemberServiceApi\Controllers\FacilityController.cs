﻿using Contracts;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MemberServiceBusinessLayer;

namespace MemberServiceApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class FacilityController : ControllerBase
    {
        private readonly IFacilityQueryHandler<Facility> _facilityQueryHandler;
        private readonly IFacilityCommandHandler<Facility> _facilityCommandHandler;
        private readonly ILogger<FacilityController> _logger;
        private readonly IStringLocalizer<FacilityController> _localizer;

        public FacilityController(
            IFacilityQueryHandler<Facility> facilityQueryHandler,
            IFacilityCommandHandler<Facility> facilityCommandHandler,
            ILogger<FacilityController> logger,
            IStringLocalizer<FacilityController> localizer)
        {
            _facilityQueryHandler = facilityQueryHandler ?? throw new ArgumentNullException(nameof(facilityQueryHandler));
            _facilityCommandHandler = facilityCommandHandler ?? throw new ArgumentNullException(nameof(facilityCommandHandler));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _localizer = localizer ?? throw new ArgumentNullException(nameof(localizer));
        }

        /// <summary>
        /// Gets a facility by its ID.
        /// </summary>
        /// <param name="id">The ID of the facility.</param>
        /// <returns>An IActionResult containing the facility or a not found result.</returns>
        [HttpGet("{id:Guid}/{OrgID:Guid}/{Subscription}")]
        public async Task<IActionResult> GetFacilityById(Guid id, Guid OrgID, bool Subscription)
        {
            IActionResult response;
            _logger.LogInformation(_localizer["FetchingFacilityWithID"], id);
            var facility = await _facilityQueryHandler.GetFacilityByIdAsync(Subscription, id, OrgID);

            if (facility == null)
            {
                _logger.LogWarning(_localizer["FacilityNotFoundWithID"], id);
                response = NotFound(_localizer["FacilityNotFound"]);
            }
            else
            {
                _logger.LogInformation(_localizer["FacilityFetchedSuccessfully"], id);
                response = Ok(facility);
            }

            return response;
        }

        /// <summary>
        /// Gets a list of facilities by its Organization ID.
        /// </summary>
        /// <param name="id">The Organization ID of the facility.</param>
        /// <returns>An IActionResult containing the facility or a not found result.</returns>
        [HttpGet("Org/{id}/{Subscription}")]
        public async Task<IActionResult> GetFacilityByOrgId(Guid id, bool Subscription)
        {
            IActionResult response;
            _logger.LogInformation(_localizer["FetchingFacilityWithID"], id);
            var facility = await _facilityQueryHandler.GetFacilitiesByOrgAsync(Subscription, id);

            if (facility == null)
            {
                _logger.LogWarning(_localizer["FacilityNotFoundWithID"], id);
                response = NotFound(_localizer["FacilityNotFound"]);
            }
            else
            {
                _logger.LogInformation(_localizer["FacilityFetchedSuccessfully"], id);
                response = Ok(facility);
            }

            return response;
        }

        /// <summary>
        /// Adds a new facility.
        /// </summary>
        /// <param name="facility">The facility to add.</param>
        /// <returns>An IActionResult containing the result of the operation.</returns>
        [HttpPost]
        public async Task<IActionResult> AddFacility([FromBody] Facility facility)
        {
            IActionResult response;

            if (facility == null)
            {
                _logger.LogWarning(_localizer["FacilityCannotBeNull"]);
                response = BadRequest(_localizer["InvalidFacilityData"]);
            }
            else
            {
                try
                {
                    _logger.LogInformation(_localizer["AddingNewFacility"]);
                    Guid orgId = facility.OrganizationID
                                   ?? throw new InvalidOperationException("OrganizationId cannot be null.");
                    await _facilityCommandHandler.AddFacilityAsync(new List<Facility> { facility },facility.Subscription, orgId);
                    _logger.LogInformation(_localizer["FacilityAddedSuccessfully"], facility.FacilityId);
                    response = CreatedAtAction(nameof(GetFacilityById), new { id = facility.FacilityId }, facility);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["ErrorAddingFacility"]);
                    response = StatusCode(500, _localizer["InternalServerError"]);
                }
            }

            return response;
        }

        /// <summary>
        /// Updates an existing facility.
        /// </summary>
        /// <param name="id">The ID of the facility to update.</param>
        /// <param name="facility">The updated facility data.</param>
        /// <returns>An IActionResult containing the result of the operation.</returns>
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateFacility(Guid id, [FromBody] Facility facility)
        {
            IActionResult response;

            if (facility == null || id != facility.FacilityId)
            {
                _logger.LogWarning(_localizer["InvalidFacilityUpdateData"], id);
                response = BadRequest(_localizer["FacilityInvalid"]);
            }
            else
            {
                try
                {
                    _logger.LogInformation(_localizer["UpdatingFacilityWithID"], id);
                    Guid orgId = facility.OrganizationID
                                   ?? throw new InvalidOperationException("OrganizationId cannot be null.");
                    await _facilityCommandHandler.UpdateFacilityAsync(facility,facility.Subscription, orgId);
                    _logger.LogInformation(_localizer["FacilityUpdatedSuccessfully"], id);
                    response = NoContent();
                }
                catch (KeyNotFoundException)
                {
                    _logger.LogWarning(_localizer["FacilityNotFoundForUpdate"], id);
                    response = NotFound(_localizer["FacilityNotFound"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["ErrorUpdatingFacility"]);
                    response = StatusCode(500, _localizer["InternalServerError"]);
                }
            }

            return response;
        }

        /// <summary>
        /// Deletes a facility by its ID.
        /// </summary>
        /// <param name="id">The ID of the facility to delete.</param>
        /// <returns>An IActionResult containing the result of the operation.</returns>
        [HttpDelete("{id:guid}/{OrgID:Guid}/{Subscription}")]
        public async Task<IActionResult> DeleteFacility(Guid id, Guid OrgID, bool Subscription)
        {
            IActionResult response;

            try
            {
                _logger.LogInformation(_localizer["DeletingFacilityWithID"], id);
                await _facilityCommandHandler.DeleteFacilityAsync(Subscription, id, OrgID);
                _logger.LogInformation(_localizer["FacilityDeletedSuccessfully"], id);
                response = NoContent();
            }
            catch (KeyNotFoundException)
            {
                _logger.LogWarning(_localizer["FacilityNotFoundForDeletion"], id);
                response = NotFound(_localizer["FacilityNotFound"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorDeletingFacility"]);
                response = StatusCode(500, _localizer["InternalServerError"]);
            }

            return response;
        }

        /// <summary>
        /// Searches for facilities by name.
        /// </summary>
        /// <param name="name">The name of the facility to search for.</param>
        /// <returns>An IActionResult containing the search results or a not found result.</returns>
        [HttpGet("search/{OrgID:Guid}/{Subscription}")]
        public async Task<IActionResult> GetFacilityByName([FromQuery] string name, Guid OrgID, bool Subscription)
        {
            IActionResult response;

            if (string.IsNullOrWhiteSpace(name))
            {
                _logger.LogWarning(_localizer["FacilityNameRequiredForSearch"]);
                response = BadRequest(_localizer["FacilityNameRequired"]);
            }
            else
            {
                try
                {
                    _logger.LogInformation(_localizer["SearchingForFacilityByName"], name);
                    var facilities = await _facilityQueryHandler.GetFacilitiesByNameAsync(Subscription,name, OrgID);

                    if (facilities == null || !facilities.Any())
                    {
                        _logger.LogWarning(_localizer["NoFacilitiesFoundByName"], name);
                        response = NotFound(_localizer["FacilitiesNotFound"]);
                    }
                    else
                    {
                        _logger.LogInformation(_localizer["FacilitiesFetchedSuccessfully"], name);
                        response = Ok(facilities);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["ErrorSearchingFacilities"]);
                    response = StatusCode(500, _localizer["InternalServerError"]);
                }
            }

            return response;
        }

        /// <summary>
        /// Gets all facilities.
        /// </summary>
        /// <returns>An IActionResult containing the list of all facilities or a not found result.</returns>
        [HttpGet]
        [Route("{orgId:guid}/{Subscription}")]
        public async Task<IActionResult> GetAllFacilities(Guid orgId, bool Subscription)
        {
            IActionResult response;

            _logger.LogInformation(_localizer["FetchingAllFacilities"]);
            try
            {
                var facilities = await _facilityQueryHandler.GetAllFacilitiesAsync(orgId, Subscription);

                if (facilities == null || facilities.Count == 0)
                {
                    _logger.LogWarning(_localizer["NoFacilitiesFound"]);
                    response = NotFound(_localizer["FacilitiesNotFound"]);
                }
                else
                {
                    _logger.LogInformation(_localizer["AllFacilitiesFetchedSuccessfully"]);
                    response = Ok(facilities);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingFacilities"]);
                response = StatusCode(500, _localizer["InternalServerError"]);
            }

            return response;
        }

    }
}
