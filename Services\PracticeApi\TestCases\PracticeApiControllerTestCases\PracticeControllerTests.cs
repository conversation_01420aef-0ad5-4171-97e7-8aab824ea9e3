using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using PracticeApi.Controllers;
using PracticeApi.PracticeApiResources;
using PracticeBusinessLayer;
using PracticeContracts;
using ShardModels;
using System;
using System.Collections.Generic;

namespace PracticeApiControllerTestCases
{
    [TestFixture]
    public class PracticeControllerTests
    {
        private Mock<IPracticeCommandHandler<Tasks>> _mockCommandHandler;
        private Mock<IPracticeQueryHandler<Tasks>> _mockQueryHandler;
        private Mock<ILogger<PracticeController>> _mockLogger;
        private Mock<IStringLocalizer<PracticeApiStrings>> _mockLocalizer;
        private PracticeController _controller;
        private Guid _testOrgId;
        private bool _testSubscription;

        [SetUp]
        public void Setup()
        {
            _mockCommandHandler = new Mock<IPracticeCommandHandler<Tasks>>();
            _mockQueryHandler = new Mock<IPracticeQueryHandler<Tasks>>();
            _mockLogger = new Mock<ILogger<PracticeController>>();
            _mockLocalizer = new Mock<IStringLocalizer<PracticeApiStrings>>();
            _testOrgId = Guid.NewGuid();
            _testSubscription = true;

            // Setup localizer mock
            _mockLocalizer.Setup(l => l["GetLogError"]).Returns(new LocalizedString("GetLogError", "Error while fetching tasks."));
            _mockLocalizer.Setup(l => l["500"]).Returns(new LocalizedString("500", "500"));
            _mockLocalizer.Setup(l => l["NoTask"]).Returns(new LocalizedString("NoTask", "No tasks provided."));
            _mockLocalizer.Setup(l => l["SuccessfulRegistration"]).Returns(new LocalizedString("SuccessfulRegistration", "Registration successful."));
            _mockLocalizer.Setup(l => l["PostLogError"]).Returns(new LocalizedString("PostLogError", "Error while adding tasks."));
            _mockLocalizer.Setup(l => l["UpdateLogError"]).Returns(new LocalizedString("UpdateLogError", "Error while updating task."));
            _mockLocalizer.Setup(l => l["DeleteLogError"]).Returns(new LocalizedString("DeleteLogError", "Error while deleting task."));
            _mockLocalizer.Setup(l => l["SuccessfulDeletion"]).Returns(new LocalizedString("SuccessfulDeletion", "Task successfully deleted."));
            _mockLocalizer.Setup(l => l["UpdateSuccessful"]).Returns(new LocalizedString("UpdateSuccessful", "Update successful."));

            _controller = new PracticeController(
                _mockCommandHandler.Object,
                _mockQueryHandler.Object,
                _mockLogger.Object,
                _mockLocalizer.Object
            );
        }

        [Test]
        public async Task Get_ReturnsOkWithTasksList()
        {
            // Arrange
            var mockTasks = new List<Tasks>
            {
                new Tasks { Id = Guid.NewGuid(), PatientName = "John Doe", TaskType = "Appointment", Subject = "Annual Checkup" },
                new Tasks { Id = Guid.NewGuid(), PatientName = "Jane Smith", TaskType = "Follow-up", Subject = "Medication Review" }
            };

            _mockQueryHandler.Setup(q => q.GetTasks()).ReturnsAsync(mockTasks);

            // Act
            var result = await _controller.Get();

            // Assert
            Assert.That(result.Result, Is.TypeOf<OkObjectResult>());
            var okResult = result.Result as OkObjectResult;
            Assert.That(okResult?.StatusCode, Is.EqualTo(200));
            Assert.That(okResult?.Value, Is.EqualTo(mockTasks));
        }

        [Test]
        public async Task Get_WhenExceptionThrown_ReturnsInternalServerError()
        {
            // Arrange
            _mockQueryHandler.Setup(q => q.GetTasks()).ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _controller.Get();

            // Assert
            Assert.That(result.Result, Is.TypeOf<StatusCodeResult>());
            var statusCodeResult = result.Result as StatusCodeResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task GetById_ValidId_ReturnsTask()
        {
            // Arrange
            var taskId = Guid.NewGuid();
            var task = new Tasks
            {
                Id = taskId,
                PatientName = "John Doe",
                TaskType = "Follow-Up",
                Subject = "Medical Consultation"
            };
            _mockQueryHandler.Setup(q => q.GetTaskById(taskId, _testOrgId, _testSubscription)).ReturnsAsync(task);

            // Act
            var result = await _controller.GetById(taskId, _testOrgId, _testSubscription);

            // Assert
            Assert.That(result.Result, Is.TypeOf<OkObjectResult>());
            var okResult = result.Result as OkObjectResult;
            Assert.That(okResult?.StatusCode, Is.EqualTo(200));
            Assert.That(okResult?.Value, Is.EqualTo(task));
        }

        [Test]
        public async Task GetById_WhenExceptionThrown_ReturnsInternalServerError()
        {
            // Arrange
            var taskId = Guid.NewGuid();
            _mockQueryHandler.Setup(q => q.GetTaskById(taskId, _testOrgId, _testSubscription))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _controller.GetById(taskId, _testOrgId, _testSubscription);

            // Assert
            Assert.That(result.Result, Is.TypeOf<StatusCodeResult>());
            var statusCodeResult = result.Result as StatusCodeResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task Registration_ValidTasks_ReturnsSuccessfulResponse()
        {
            // Arrange
            var tasks = new List<Tasks>
            {
                new Tasks { Id = Guid.NewGuid(), PatientName = "Jane Doe", TaskType = "Consultation", Subject = "Medical Advice" }
            };
            _mockCommandHandler.Setup(c => c.AddTasks(tasks, _testOrgId, _testSubscription)).Returns(Task.CompletedTask);

            // Act
            var result = await _controller.Registration(tasks, _testOrgId, _testSubscription);

            // Assert
            Assert.That(result, Is.TypeOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult?.Value?.ToString(), Is.EqualTo("Registration successful."));
        }

        [Test]
        public async Task Registration_EmptyTasksList_SetsResponseToBadRequestButReturnsOk()
        {
            // Arrange
            List<Tasks> tasks = new List<Tasks>();
            _mockCommandHandler.Setup(c => c.AddTasks(tasks, _testOrgId, _testSubscription)).Returns(Task.CompletedTask);

            // Act
            var result = await _controller.Registration(tasks, _testOrgId, _testSubscription);

            // Assert
            // Due to a bug in the controller, it sets response to BadRequest but then continues execution
            // and returns Ok instead because there's no return statement after the BadRequest
            Assert.That(result, Is.TypeOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult?.Value?.ToString(), Is.EqualTo("Registration successful."));
        }

        [Test]
        public async Task Registration_NullTasksList_SetsResponseToBadRequestButReturnsOk()
        {
            // Arrange
            List<Tasks>? nullList = null;
            _mockCommandHandler.Setup(c => c.AddTasks(It.IsAny<List<Tasks>>(), _testOrgId, _testSubscription)).Returns(Task.CompletedTask);

            // Act
            var result = await _controller.Registration(nullList, _testOrgId, _testSubscription);

            // Assert
            // Due to a bug in the controller, it sets response to BadRequest but then continues execution
            // and returns Ok instead because there's no return statement after the BadRequest
            Assert.That(result, Is.TypeOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult?.Value?.ToString(), Is.EqualTo("Registration successful."));
        }

        [Test]
        public async Task Registration_WhenExceptionThrown_ReturnsInternalServerError()
        {
            // Arrange
            var tasks = new List<Tasks>
            {
                new Tasks { Id = Guid.NewGuid(), PatientName = "Jane Doe", TaskType = "Consultation", Subject = "Medical Advice" }
            };
            _mockCommandHandler.Setup(c => c.AddTasks(tasks, _testOrgId, _testSubscription))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _controller.Registration(tasks, _testOrgId, _testSubscription);

            // Assert
            Assert.That(result, Is.TypeOf<StatusCodeResult>());
            var statusCodeResult = result as StatusCodeResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }


    }
}
