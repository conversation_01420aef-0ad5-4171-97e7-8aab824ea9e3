﻿using Contracts;
using DataAccessLayer.Context;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MemberServiceDataAccessLayer;
using ShardModels;
using Interfaces.ShardManagement;
using System.Security.Cryptography;

namespace MemberServiceDataAccessLayer.Implementation
{
    /// <summary>
    /// Repository implementation for handling member-related database operations.
    /// </summary>
    public class MemberRepository : ShardGenericRepository<Member>, IMemberRepository
    {
        private readonly AccountDatabaseContext _context;
        private readonly ShardMapManagerService<AccountDatabaseContext, AccountDatabaseContext> _shardMapManagerService;
        private readonly IStringLocalizer<AccountDatabaseContext> _localizer;
        private readonly ILogger<AccountDatabaseContext> _logger;
        private readonly string _shardMapName;

        /// <summary>
        /// Initializes a new instance of the <see cref="MemberRepository"/> class.
        /// </summary>
        /// <param name="context">Database context for member-related operations.</param>
        public MemberRepository(AccountDatabaseContext context, ShardMapManagerService<AccountDatabaseContext, AccountDatabaseContext> shardMapManagerService,
            IStringLocalizer<AccountDatabaseContext> localizer,
            ILogger<AccountDatabaseContext> logger) : base(context, shardMapManagerService,localizer,logger)
        {
            _context = context;
            _shardMapManagerService = shardMapManagerService;
            _localizer = localizer;
            _logger = logger;
            _shardMapName = Environment.GetEnvironmentVariable("shardMapName");
        }

        /// <summary>
        /// Retrieves a list of existing emails from the database.
        /// </summary>
        /// <param name="emails">List of emails to check.</param>
        /// <returns>A list of emails that already exist in the database.</returns>
        public async Task<List<string>> GetExistingEmailsAsync(List<string> emails, Guid orgId,bool Subscription)
        {
            var allEntities = new List<string>();
            var allKeys = _shardMapManagerService.GetAllShardKeys(_shardMapName);
            foreach (var key in allKeys)
            {
                try
                {
                    using (var context = _shardMapManagerService.GetDbContext(key, _shardMapName))
                    {
                        var entities = await context.Members
                                        .Where(member => emails.Contains(member.Email))
                                        .Select(member => member.Email)
                                        .ToListAsync();
                        allEntities.AddRange(entities);
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error for shard key {BitConverter.ToString(key)}: {ex.Message}");
                    throw; 
                }
            }
            return allEntities;

        }

        public async Task<IEnumerable<Member>> SearchMembersAsync(string searchTerm, Guid orgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, orgId, _shardMapName);

            return await context.Members
                .Where(member => member.UserName.ToLower().Contains(searchTerm.ToLower()))
                .ToListAsync();
        }

        public async Task<IEnumerable<Member>> SearchMembersByEmailAsync(string searchTerm, Guid orgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, orgId, _shardMapName);

            return await context.Members
                .Where(member => member.Email.ToLower().Contains(searchTerm.ToLower()))
                .ToListAsync();
        }
        /// <summary>
        /// Retrieves patient details for the specified list of patient IDs.
        /// </summary>
        /// <param name="patientIds">List of patient unique identifiers.</param>
        /// <returns>A collection of office visit members with matching patient IDs.</returns>
        public async Task<IEnumerable<Office_visit_members>> GetPatientsByIdsAsync(List<Guid> patientIds, Guid orgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, orgId, _shardMapName);
            if (context == null) return new List<Office_visit_members>();

            return await context.Members
                .Where(member => patientIds.Contains(member.Id))
                .Select(member => new Office_visit_members
                {
                    PatientId = member.Id,
                    UserName = member.UserName,
                    DateOfBirth = member.DateOfBirth,
                    SexualOrientation = member.SexualOrientation
                })
                .ToListAsync();

        }

        /// <summary>
        /// Get ProviderPatients belonging to a specific Organization
        /// </summary>
        /// <param name="organizationId"></param>
        /// <returns></returns>
        public async Task<IEnumerable<ProviderPatient>> GetProviderPatientByOrganizationId(Guid organizationId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, organizationId, _shardMapName);
            if (context == null) return new List<ProviderPatient>();

            return await context.Members
                .Where(member => member.OrganizationID == organizationId
                    && member.RoleName == "Provider")
                .Select(member => new ProviderPatient
                {
                    SSN = member.SSN,
                    Id = member.Id,
                    Username = member.UserName,
                    PCPId = member.PCPId
                })
                .ToListAsync();
        }

        /// <summary>
        /// Get Patients belonging to a specific Organization
        /// </summary>
        /// <param name="organizationId"></param>
        /// <returns></returns>
        public async Task<IEnumerable<ProviderPatient>> GetPatientsByOrganizationId(Guid organizationId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, organizationId, _shardMapName);
            if (context == null) return new List<ProviderPatient>();

            return await context.Members
                .Where(member => member.OrganizationID == organizationId
                    && member.RoleName == "Patient")
                .Select(member => new ProviderPatient
                {
                    SSN = member.SSN,
                    Id = member.Id,
                    Username = member.UserName,
                    PCPId = member.PCPId
                })
                .ToListAsync();
        }
    }
}
