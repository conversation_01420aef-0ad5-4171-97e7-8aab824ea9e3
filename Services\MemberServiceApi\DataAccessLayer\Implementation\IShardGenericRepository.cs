﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MemberServiceDataAccessLayer.Implementation

{
    public interface IShardGenericRepository<T> where T : class
    {
        Task AddAsync(IEnumerable<T> entities, Guid OrgId, bool Subscription);
        Task<IEnumerable<T>> GetAllAsync();
        Task<IEnumerable<T>> GetByOrgIdAsync( Guid orgId, bool subscription);
        Task<bool> DeleteByNameAsync(string name, Guid OrgID, bool Subscription);
        Task<IEnumerable<T>> GetAllAsync(Guid OrgId, bool Subscription);
        Task<T> GetByIdAsync(Guid id, Guid OrgId, bool Subscription);
        Task UpdateAsync(T entity, Guid OrgId, bool Subscription);
        Task<T> GetFirstByOrganizationIdAsync(Guid id, bool Subscription);
        Task UpdateRangeAsync(IEnumerable<T> entities, Guid OrgId, bool Subscription);
        Task DeleteByEntityAsync(T entity, Guid OrgId, bool Subscription);
        Task DeleteByIdAsync(Guid id, Guid OrgId, bool Subscription);
    }
}