﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Contracts;

namespace MemberServiceDataAccessLayer.Implementation
{
    public interface IUserLicenseRepository : IGenericRepository<UserLicense>
    {
        Task AddAsync(UserLicense license);
        Task UpdateAsync(UserLicense license);
        Task DeleteByIdAsync(Guid id);
        Task<UserLicense> GetByIdAsync(Guid id);
        Task<UserLicense> GetByOrganizationIdAsync(Guid id);
        Task<List<UserLicense>> GetAllAsync();
    }
}
