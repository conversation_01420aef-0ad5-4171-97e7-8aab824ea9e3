using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using MemberServiceApi.Controllers;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using MemberServiceBusinessLayer;
using System.Linq;

namespace MemberServiceApiControllerTestCases
{
    [TestFixture]
    public class VisitStatusControllerTests
    {
        private Mock<IVisitStatusQueryHandler<VisitStatus>> _mockQueryHandler;
        private Mock<ILogger<VisitStatusController>> _mockLogger;
        private Mock<IStringLocalizer<VisitStatusController>> _mockLocalizer;
        private VisitStatusController _controller;

        [SetUp]
        public void Setup()
        {
            _mockQueryHandler = new Mock<IVisitStatusQueryHandler<VisitStatus>>();
            _mockLogger = new Mock<ILogger<VisitStatusController>>();
            _mockLocalizer = new Mock<IStringLocalizer<VisitStatusController>>();

            // Setup localizer to return the key when requested
            _mockLocalizer.Setup(l => l[It.IsAny<string>()]).Returns((string key) => new LocalizedString(key, key));

            _controller = new VisitStatusController(
                _mockQueryHandler.Object,
                _mockLogger.Object,
                _mockLocalizer.Object
            );
        }

        [Test]
        public async Task Get_WhenVisitStatusesExist_ReturnsOkWithVisitStatuses()
        {
            // Arrange
            var mockVisitStatuses = new List<VisitStatus>
            {
                new VisitStatus { ID = Guid.NewGuid(), Visitstatus = "Status 1" },
                new VisitStatus { ID = Guid.NewGuid(), Visitstatus = "Status 2" }
            };

            _mockQueryHandler
                .Setup(q => q.GetAllVisitStatusAsync())
                .ReturnsAsync(mockVisitStatuses);

            // Act
            var result = await _controller.Get();

            // Assert
            Assert.That(result.Result, Is.InstanceOf<OkObjectResult>());
            var okResult = result.Result as OkObjectResult;
            Assert.That(okResult.StatusCode, Is.EqualTo(200));
            Assert.That(okResult.Value, Is.EqualTo(mockVisitStatuses));
        }

        [Test]
        public async Task Get_WhenNoVisitStatusesExist_ReturnsOk()
        {
            // Arrange
            _mockQueryHandler
                .Setup(q => q.GetAllVisitStatusAsync())
                .ReturnsAsync(new List<VisitStatus>());

            // Act
            var result = await _controller.Get();

            // Assert
            Assert.That(result.Result, Is.InstanceOf<OkObjectResult>());
            var okResult = result.Result as OkObjectResult;
            Assert.That(okResult.StatusCode, Is.EqualTo(200));
        }

        [Test]
        public async Task Get_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            _mockQueryHandler
                .Setup(q => q.GetAllVisitStatusAsync())
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.Get();

            // Assert
            Assert.That(result.Result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result.Result as ObjectResult;
            Assert.That(statusCodeResult.StatusCode, Is.EqualTo(500));
        }

        // GetById method is not implemented in VisitStatusController
        // This test has been removed

        // GetById method is not implemented in VisitStatusController
        // This test has been removed

        // GetById method is not implemented in VisitStatusController
        // This test has been removed
    }
}
