using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using InsuranceServiceApi.Controllers;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using MemberServiceBusinessLayer;
using System.Linq;

namespace MemberServiceApiControllerTestCases
{
    [TestFixture]
    public class InsuranceControllerTests
    {
        private Mock<IInsuranceQueryHandler<Insurance>> _mockQueryHandler;
        private Mock<IInsuranceCommandHandler<Insurance>> _mockCommandHandler;
        private Mock<ILogger<InsuranceController>> _mockLogger;
        private Mock<IStringLocalizer<InsuranceController>> _mockLocalizer;
        private InsuranceController _controller;

        [SetUp]
        public void Setup()
        {
            _mockQueryHandler = new Mock<IInsuranceQueryHandler<Insurance>>();
            _mockCommandHandler = new Mock<IInsuranceCommandHandler<Insurance>>();
            _mockLogger = new Mock<ILogger<InsuranceController>>();
            _mockLocalizer = new Mock<IStringLocalizer<InsuranceController>>();

            // Setup localizer to return the key when requested
            _mockLocalizer.Setup(l => l[It.IsAny<string>()]).Returns((string key) => new LocalizedString(key, key));

            _controller = new InsuranceController(
                _mockQueryHandler.Object,
                _mockCommandHandler.Object,
                _mockLogger.Object,
                _mockLocalizer.Object
            );
        }

        [Test]
        public async Task GetAllInsurances_WhenInsurancesExist_ReturnsOkWithInsurances()
        {
            // Arrange
            var mockInsurances = new List<Insurance>
            {
                new Insurance { InsuranceId = Guid.NewGuid(), PrimaryInsuranceProvider = "Provider 1" },
                new Insurance { InsuranceId = Guid.NewGuid(), PrimaryInsuranceProvider = "Provider 2" }
            };

            _mockQueryHandler
                .Setup(q => q.GetAllInsurancesAsync())
                .ReturnsAsync(mockInsurances);

            // Act
            var result = await _controller.GetAllInsurances();

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult?.StatusCode, Is.EqualTo(200));
            Assert.That(okResult?.Value, Is.EqualTo(mockInsurances));
        }

        [Test]
        public async Task GetAllInsurances_WhenNoInsurancesExist_ReturnsNotFound()
        {
            // Arrange
            _mockQueryHandler
                .Setup(q => q.GetAllInsurancesAsync())
                .ReturnsAsync(new List<Insurance>());

            // Act
            var result = await _controller.GetAllInsurances();

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult?.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task GetAllInsurances_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            _mockQueryHandler
                .Setup(q => q.GetAllInsurancesAsync())
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetAllInsurances();

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task GetInsuranceById_WhenInsuranceExists_ReturnsOkWithInsurance()
        {
            // Arrange
            var insuranceId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var mockInsurance = new Insurance
            {
                InsuranceId = insuranceId,
                PrimaryInsuranceProvider = "Test Provider",
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockQueryHandler
                .Setup(q => q.GetInsuranceByIdAsync(insuranceId, orgId, subscription))
                .ReturnsAsync(mockInsurance);

            // Act
            var result = await _controller.GetInsuranceById(insuranceId, orgId, subscription);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult?.StatusCode, Is.EqualTo(200));
            Assert.That(okResult?.Value, Is.EqualTo(mockInsurance));
        }

        [Test]
        public async Task GetInsuranceById_WhenInsuranceDoesNotExist_ReturnsNotFound()
        {
            // Arrange
            var insuranceId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockQueryHandler
                .Setup(q => q.GetInsuranceByIdAsync(insuranceId, orgId, subscription))
                .ReturnsAsync((Insurance)null);

            // Act
            var result = await _controller.GetInsuranceById(insuranceId, orgId, subscription);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult?.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task AddInsurance_WhenValidInsurance_ReturnsCreatedAtAction()
        {
            // Arrange
            var insuranceId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var insurance = new Insurance
            {
                InsuranceId = insuranceId,
                PrimaryInsuranceProvider = "Test Provider",
                PlanName = "Test Plan",
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockCommandHandler
                .Setup(c => c.AddInsuranceAsync(It.IsAny<List<Insurance>>(), orgId, subscription))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.AddInsurance(insurance);

            // Assert
            Assert.That(result, Is.InstanceOf<CreatedAtActionResult>());
            var createdResult = result as CreatedAtActionResult;
            Assert.That(createdResult?.StatusCode, Is.EqualTo(201));
            Assert.That(createdResult?.ActionName, Is.EqualTo(nameof(InsuranceController.GetInsuranceById)));
            Assert.That(createdResult?.RouteValues["id"], Is.EqualTo(insuranceId));
            Assert.That(createdResult?.Value, Is.EqualTo(insurance));
        }

        [Test]
        public async Task AddInsurance_WhenNullInsurance_ReturnsBadRequest()
        {
            // Act
            var result = await _controller.AddInsurance(null);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult?.StatusCode, Is.EqualTo(400));
        }

        [Test]
        public async Task AddInsurance_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var insuranceId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var insurance = new Insurance
            {
                InsuranceId = insuranceId,
                PrimaryInsuranceProvider = "Test Provider",
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockCommandHandler
                .Setup(c => c.AddInsuranceAsync(It.IsAny<List<Insurance>>(), orgId, subscription))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.AddInsurance(insurance);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task UpdateInsurance_WhenValidInsurance_ReturnsNoContent()
        {
            // Arrange
            var insuranceId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var insurance = new Insurance
            {
                InsuranceId = insuranceId,
                PrimaryInsuranceProvider = "Updated Provider",
                PlanName = "Updated Plan",
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockCommandHandler
                .Setup(c => c.UpdateInsuranceAsync(insurance, orgId, subscription))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.UpdateInsurance(insuranceId, insurance);

            // Assert
            Assert.That(result, Is.InstanceOf<NoContentResult>());
            var noContentResult = result as NoContentResult;
            Assert.That(noContentResult?.StatusCode, Is.EqualTo(204));
        }

        [Test]
        public async Task UpdateInsurance_WhenNullInsurance_ReturnsBadRequest()
        {
            // Arrange
            var insuranceId = Guid.NewGuid();

            // Act
            var result = await _controller.UpdateInsurance(insuranceId, null);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult?.StatusCode, Is.EqualTo(400));
        }

        [Test]
        public async Task UpdateInsurance_WhenIdMismatch_ReturnsBadRequest()
        {
            // Arrange
            var insuranceId = Guid.NewGuid();
            var differentId = Guid.NewGuid();
            var insurance = new Insurance
            {
                InsuranceId = differentId,
                PrimaryInsuranceProvider = "Test Provider"
            };

            // Act
            var result = await _controller.UpdateInsurance(insuranceId, insurance);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult?.StatusCode, Is.EqualTo(400));
        }

        [Test]
        public async Task UpdateInsurance_WhenInsuranceDoesNotExist_ReturnsNotFound()
        {
            // Arrange
            var insuranceId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var insurance = new Insurance
            {
                InsuranceId = insuranceId,
                PrimaryInsuranceProvider = "Test Provider",
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockCommandHandler
                .Setup(c => c.UpdateInsuranceAsync(insurance, orgId, subscription))
                .ThrowsAsync(new KeyNotFoundException());

            // Act
            var result = await _controller.UpdateInsurance(insuranceId, insurance);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult?.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task UpdateInsurance_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var insuranceId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var insurance = new Insurance
            {
                InsuranceId = insuranceId,
                PrimaryInsuranceProvider = "Test Provider",
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockCommandHandler
                .Setup(c => c.UpdateInsuranceAsync(insurance, orgId, subscription))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.UpdateInsurance(insuranceId, insurance);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task DeleteInsurance_WhenInsuranceExists_ReturnsNoContent()
        {
            // Arrange
            var insuranceId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockCommandHandler
                .Setup(c => c.DeleteInsuranceAsync(insuranceId, orgId, subscription))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.DeleteInsurance(insuranceId, orgId, subscription);

            // Assert
            Assert.That(result, Is.InstanceOf<NoContentResult>());
            var noContentResult = result as NoContentResult;
            Assert.That(noContentResult?.StatusCode, Is.EqualTo(204));
        }

        [Test]
        public async Task DeleteInsurance_WhenInsuranceDoesNotExist_ReturnsNotFound()
        {
            // Arrange
            var insuranceId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockCommandHandler
                .Setup(c => c.DeleteInsuranceAsync(insuranceId, orgId, subscription))
                .ThrowsAsync(new KeyNotFoundException());

            // Act
            var result = await _controller.DeleteInsurance(insuranceId, orgId, subscription);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult?.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task DeleteInsurance_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var insuranceId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockCommandHandler
                .Setup(c => c.DeleteInsuranceAsync(insuranceId, orgId, subscription))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.DeleteInsurance(insuranceId, orgId, subscription);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }
    }
}
