using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using MemberServiceApi.Controllers;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using MemberServiceBusinessLayer;

namespace MemberServiceApiControllerTestCases
{
    [TestFixture]
    public class LicensesControllerTests
    {
        private Mock<ILicenseQueryHandler<ProductLicense>> _mockQueryHandler;
        private Mock<ILicenseCommandHandler<ProductLicense>> _mockCommandHandler;
        private Mock<ILogger<LicensesController>> _mockLogger;
        private Mock<IStringLocalizer<LicensesController>> _mockLocalizer;
        private LicensesController _controller;

        [SetUp]
        public void Setup()
        {
            _mockQueryHandler = new Mock<ILicenseQueryHandler<ProductLicense>>();
            _mockCommandHandler = new Mock<ILicenseCommandHandler<ProductLicense>>();
            _mockLogger = new Mock<ILogger<LicensesController>>();
            _mockLocalizer = new Mock<IStringLocalizer<LicensesController>>();

            // Setup localizer to return the key when requested
            _mockLocalizer.Setup(l => l[It.IsAny<string>()]).Returns((string key) => new LocalizedString(key, key));

            _controller = new LicensesController(
                _mockCommandHandler.Object,
                _mockQueryHandler.Object,
                _mockLogger.Object,
                _mockLocalizer.Object
            );
        }

        [Test]
        public async Task Get_WhenLicensesExist_ReturnsOkWithLicenses()
        {
            // Arrange
            var mockLicenses = new List<ProductLicense>
            {
                new ProductLicense { Id = Guid.NewGuid(), ProductName = "License 1", Description = "License 1 Description", IsLicenseActivated = true, OrganizationId = Guid.NewGuid(), Subscription = false },
                new ProductLicense { Id = Guid.NewGuid(), ProductName = "License 2", Description = "License 2 Description", IsLicenseActivated = true, OrganizationId = Guid.NewGuid(), Subscription = false }
            };

            _mockQueryHandler
                .Setup(q => q.GetLicense())
                .ReturnsAsync(mockLicenses);

            // Act
            var result = await _controller.Get();

            // Assert
            Assert.That(result.Result, Is.InstanceOf<OkObjectResult>());
            var okResult = result.Result as OkObjectResult;
            Assert.That(okResult.StatusCode, Is.EqualTo(200));
            Assert.That(okResult.Value, Is.EqualTo(mockLicenses));
        }

        [Test]
        public async Task Get_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            _mockQueryHandler
                .Setup(q => q.GetLicense())
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.Get();

            // Assert
            Assert.That(result.Result, Is.InstanceOf<StatusCodeResult>());
            var statusCodeResult = result.Result as StatusCodeResult;
            Assert.That(statusCodeResult.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task GetById_WhenLicenseExists_ReturnsOkWithLicense()
        {
            // Arrange
            var licenseId = Guid.NewGuid();
            var mockLicense = new ProductLicense
            {
                Id = licenseId,
                ProductName = "License 1",
                Description = "License 1 Description",
                IsLicenseActivated = true,
                OrganizationId = Guid.NewGuid(),
                Subscription = false
            };

            var orgId = mockLicense.OrganizationId;
            var subscription = mockLicense.Subscription;

            _mockQueryHandler
                .Setup(q => q.GetLicenseById(licenseId, orgId, subscription))
                .ReturnsAsync(mockLicense);

            // Act
            var result = await _controller.GetById(licenseId, orgId, subscription);

            // Assert
            Assert.That(result.Result, Is.InstanceOf<OkObjectResult>());
            var okResult = result.Result as OkObjectResult;
            Assert.That(okResult.StatusCode, Is.EqualTo(200));
            Assert.That(okResult.Value, Is.EqualTo(mockLicense));
        }

        [Test]
        public async Task GetById_WhenLicenseDoesNotExist_ReturnsOk()
        {
            // Arrange
            var licenseId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = false;

            _mockQueryHandler
                .Setup(q => q.GetLicenseById(licenseId, orgId, subscription))
                .ReturnsAsync((ProductLicense)null);

            // Act
            var result = await _controller.GetById(licenseId, orgId, subscription);

            // Assert
            Assert.That(result.Result, Is.InstanceOf<OkObjectResult>());
            var okResult = result.Result as OkObjectResult;
            Assert.That(okResult.StatusCode, Is.EqualTo(200));
            Assert.That(okResult.Value, Is.Null);
        }

        [Test]
        public async Task GetById_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var licenseId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            var subscription = false;

            _mockQueryHandler
                .Setup(q => q.GetLicenseById(licenseId, orgId, subscription))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetById(licenseId, orgId, subscription);

            // Assert
            Assert.That(result.Result, Is.InstanceOf<StatusCodeResult>());
            var statusCodeResult = result.Result as StatusCodeResult;
            Assert.That(statusCodeResult.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task Registration_WhenValidLicense_ReturnsOk()
        {
            // Arrange
            var licenseId = Guid.NewGuid();
            var licenses = new List<ProductLicense>
            {
                new ProductLicense
                {
                    Id = licenseId,
                    ProductName = "License 1",
                    Description = "License 1 Description",
                    IsLicenseActivated = true,
                    OrganizationId = Guid.NewGuid(),
                    Subscription = false
                }
            };

            _mockCommandHandler
                .Setup(c => c.AddLicense(It.IsAny<List<ProductLicense>>()))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.Registration(licenses);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult?.StatusCode, Is.EqualTo(200));
        }

        [Test]
        public async Task Registration_WhenNullLicense_ReturnsBadRequest()
        {
            // Act
            var result = await _controller.Registration(null);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult?.StatusCode, Is.EqualTo(400));
        }

        [Test]
        public async Task Registration_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var licenses = new List<ProductLicense>
            {
                new ProductLicense
                {
                    Id = Guid.NewGuid(),
                    ProductName = "License 1",
                    Description = "License 1 Description",
                    IsLicenseActivated = true,
                    OrganizationId = Guid.NewGuid(),
                    Subscription = false
                }
            };

            _mockCommandHandler
                .Setup(c => c.AddLicense(It.IsAny<List<ProductLicense>>()))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.Registration(licenses);

            // Assert
            Assert.That(result, Is.InstanceOf<StatusCodeResult>());
            var statusCodeResult = result as StatusCodeResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task UpdateById_WhenValidLicense_ReturnsOk()
        {
            // Arrange
            var licenseId = Guid.NewGuid();
            var license = new ProductLicense
            {
                Id = licenseId,
                ProductName = "Updated License",
                Description = "Updated License Description",
                IsLicenseActivated = true,
                OrganizationId = Guid.NewGuid(),
                Subscription = false
            };

            var orgId = license.OrganizationId;
            var subscription = license.Subscription;

            _mockCommandHandler
                .Setup(c => c.UpdateLicense(license, orgId, subscription))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.UpdateById(licenseId, license);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult?.StatusCode, Is.EqualTo(200));
        }

        [Test]
        public async Task UpdateById_WhenIdMismatch_ReturnsBadRequest()
        {
            // Arrange
            var licenseId = Guid.NewGuid();
            var license = new ProductLicense
            {
                Id = Guid.NewGuid(), // Different ID
                ProductName = "Updated License",
                Description = "Updated License Description",
                IsLicenseActivated = true,
                OrganizationId = Guid.NewGuid(),
                Subscription = false
            };

            // Act
            var result = await _controller.UpdateById(licenseId, license);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult?.StatusCode, Is.EqualTo(400));
        }

        [Test]
        public async Task UpdateById_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var licenseId = Guid.NewGuid();
            var license = new ProductLicense
            {
                Id = licenseId,
                ProductName = "Updated License",
                Description = "Updated License Description",
                IsLicenseActivated = true,
                OrganizationId = Guid.NewGuid(),
                Subscription = false
            };

            var orgId = license.OrganizationId;
            var subscription = license.Subscription;

            _mockCommandHandler
                .Setup(c => c.UpdateLicense(license, orgId, subscription))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.UpdateById(licenseId, license);

            // Assert
            Assert.That(result, Is.InstanceOf<StatusCodeResult>());
            var statusCodeResult = result as StatusCodeResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task DeleteById_WhenLicenseExists_ReturnsOk()
        {
            // Arrange
            var licenseId = Guid.NewGuid();

            var orgId = Guid.NewGuid();
            var subscription = false;

            _mockCommandHandler
                .Setup(c => c.DeleteLicenseById(licenseId, orgId, subscription))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.DeleteById(licenseId, orgId, subscription);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult?.StatusCode, Is.EqualTo(200));
        }

        [Test]
        public async Task DeleteById_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var licenseId = Guid.NewGuid();

            var orgId = Guid.NewGuid();
            var subscription = false;

            _mockCommandHandler
                .Setup(c => c.DeleteLicenseById(licenseId, orgId, subscription))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.DeleteById(licenseId, orgId, subscription);

            // Assert
            Assert.That(result, Is.InstanceOf<StatusCodeResult>());
            var statusCodeResult = result as StatusCodeResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task DeleteByEntity_WhenLicenseExists_ReturnsOk()
        {
            // Arrange
            var license = new ProductLicense
            {
                Id = Guid.NewGuid(),
                ProductName = "License to Delete",
                Description = "License Description",
                IsLicenseActivated = true,
                OrganizationId = Guid.NewGuid(),
                Subscription = false
            };

            var orgId = license.OrganizationId;
            var subscription = license.Subscription;

            _mockCommandHandler
                .Setup(c => c.DeleteLicenseByEntity(license, orgId, subscription))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.DeleteByEntity(license);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult?.StatusCode, Is.EqualTo(200));
        }

        [Test]
        public async Task DeleteByEntity_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var license = new ProductLicense
            {
                Id = Guid.NewGuid(),
                ProductName = "License to Delete",
                Description = "License Description",
                IsLicenseActivated = true,
                OrganizationId = Guid.NewGuid(),
                Subscription = false
            };

            var orgId = license.OrganizationId;
            var subscription = license.Subscription;

            _mockCommandHandler
                .Setup(c => c.DeleteLicenseByEntity(license, orgId, subscription))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.DeleteByEntity(license);

            // Assert
            Assert.That(result, Is.InstanceOf<StatusCodeResult>());
            var statusCodeResult = result as StatusCodeResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task UpdateAccess_WhenValidLicenseAccess_ReturnsOk()
        {
            // Arrange
            var licenseAccessUpdates = new List<ProductLicense>
            {
                new ProductLicense
                {
                    Id = Guid.NewGuid(),
                    ProductName = "License 1",
                    Description = "License 1 Description",
                    IsLicenseActivated = true,
                    OrganizationId = Guid.NewGuid(),
                    Subscription = false
                }
            };

            _mockCommandHandler
                .Setup(c => c.UpdateLicenseAccessAsync(licenseAccessUpdates))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.UpdateAccess(licenseAccessUpdates);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult?.StatusCode, Is.EqualTo(200));
        }

        [Test]
        public async Task UpdateAccess_WhenLicenseAccessNotFound_ReturnsNotFound()
        {
            // Arrange
            var licenseAccessUpdates = new List<ProductLicense>
            {
                new ProductLicense
                {
                    Id = Guid.NewGuid(),
                    ProductName = "License 1",
                    Description = "License 1 Description",
                    IsLicenseActivated = true,
                    OrganizationId = Guid.NewGuid(),
                    Subscription = false
                }
            };

            _mockCommandHandler
                .Setup(c => c.UpdateLicenseAccessAsync(licenseAccessUpdates))
                .ReturnsAsync(false);

            // Act
            var result = await _controller.UpdateAccess(licenseAccessUpdates);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult?.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task UpdateAccess_WhenNullLicenseAccess_ReturnsBadRequest()
        {
            // Act
            var result = await _controller.UpdateAccess(null);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult?.StatusCode, Is.EqualTo(400));
        }
    }
}
