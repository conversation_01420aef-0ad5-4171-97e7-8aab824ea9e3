﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DotNetEnv;
using PracticeDataAccessLayer.Context;
using Interfaces.ShardManagement;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using PracticeDataAccessLayer.PracticeDataAccessLayerResources;
using PracticeDataAccessLayer.Implementation;

namespace PracticeNotesDataAccessLayer.Implementation
{
    public class ShardGenericRepository<T> : IShardGenericRepository<T> where T : class
    {
        private readonly ShardMapManagerService<PracticeDatabaseContext, PracticeDataAccessLayerStrings> _shardMapManagerService;
        private readonly IStringLocalizer<PracticeDataAccessLayerStrings> _localizer;
        private readonly ILogger<PracticeDatabaseContext> _logger;
        private readonly PracticeDatabaseContext _context;
        private readonly DbSet<T> _dbSet;
        private readonly string _shardMapName;
        const int two = 2, one = 1, zero = 0;

        public ShardGenericRepository(PracticeDatabaseContext context, ShardMapManagerService<PracticeDatabaseContext, PracticeDataAccessLayerStrings> shardMapManagerService, IStringLocalizer<PracticeDataAccessLayerStrings> localizer, ILogger<PracticeDatabaseContext> logger)
        {
            Env.Load();
            _shardMapManagerService = shardMapManagerService ?? throw new ArgumentNullException(nameof(shardMapManagerService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _context = context;
            _dbSet = context.Set<T>();
            _localizer = localizer;
            _shardMapName = Environment.GetEnvironmentVariable("shardMapName");
        }



        

        // Add
        public async Task AddAsync(IEnumerable<T> entities, Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return;
            await context.Set<T>().AddRangeAsync(entities);
            await context.SaveChangesAsync();
        }

        // Get All (Ordered)
        public async Task<IEnumerable<T>> GetAllAsync()
        {
            var allEntities = new List<T>();
            var allKeys = _shardMapManagerService.GetAllShardKeys(_shardMapName);
            foreach (var key in allKeys)
            {
                using (var context = _shardMapManagerService.GetDbContext(key, _shardMapName))
                {
                    var entities = await context.Set<T>().ToListAsync();
                    allEntities.AddRange(entities);
                }
            }
            return allEntities;
        }

        // Get (Simple List)
        public async Task<IEnumerable<T>> GetAsync(Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return Enumerable.Empty<T>();
            return await context.Set<T>().ToListAsync();
        }

        // Get by ID
        public async Task<T> GetByIdAsync(Guid id, Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return null;
            return await context.Set<T>().FindAsync(id);
        }

        // Update single entity
        public async Task UpdateAsync(T entity, Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return;
            context.Set<T>().Update(entity);
            await context.SaveChangesAsync();
        }

        // Update range
        public async Task UpdateRangeAsync(List<T> entities, Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return;
            context.Set<T>().UpdateRange(entities);
            await context.SaveChangesAsync();
        }

        // Delete by entity
        public async Task DeleteByEntityAsync(T entity, Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return;
            context.Set<T>().Remove(entity);
            await context.SaveChangesAsync();
        }

        // Delete by ID
        public async Task DeleteByIdAsync(Guid id, Guid OrgId, bool Subscription)
        {
            var entity = await GetByIdAsync(id, OrgId, Subscription);
            if (entity != null)
            {
                await DeleteByEntityAsync(entity, OrgId, Subscription);
            }
        }
    }
}