﻿using Interfaces.ShardManagement;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using PracticeDataAccessLayer.Context;
using PracticeDataAccessLayer.PracticeDataAccessLayerResources;
using System;
using System.Threading.Tasks;

namespace PracticeDataAccessLayer.Implementation
{
    public class UnitOfWork : IUnitOfWork
    {
        private readonly PracticeDatabaseContext _context;
        private readonly IStringLocalizer<PracticeDataAccessLayerStrings> _localizer;
        private readonly ILogger<PracticeDatabaseContext> _logger;
        private readonly ShardMapManagerService<PracticeDatabaseContext, PracticeDataAccessLayerStrings> _shardMapManagerService;
        public IPracticeRepository PracticeRepository { get; }
        public IProviderPatientRepository ProviderPatientRepository { get; }

        public UnitOfWork(PracticeDatabaseContext context, ShardMapManagerService<PracticeDatabaseContext, PracticeDataAccessLayerStrings> shardMapManagerService, IStringLocalizer<PracticeDataAccessLayerStrings> localizer,
                                 ILogger<PracticeDatabaseContext> logger)
        {
            _shardMapManagerService = shardMapManagerService ?? throw new ArgumentNullException(nameof(shardMapManagerService));
            _localizer = localizer ?? throw new ArgumentNullException(nameof(localizer));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _context = context ?? throw new ArgumentNullException(nameof(context));
            PracticeRepository = new PracticeRepository(_context,_shardMapManagerService, localizer, _logger);
            ProviderPatientRepository = new ProviderPatientRepository(_context);
        }

        public async Task<int> SaveAsync()
        {
            return await _context.SaveChangesAsync();
        }

        public void Dispose()
        {
            _context.Dispose();
        }
    }
}
