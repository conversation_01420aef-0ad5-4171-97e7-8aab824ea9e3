﻿using Contracts;
using DataAccessLayer.Context;
using DataAccessLayer.Implementation;
using Interfaces.ShardManagement;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace MemberServiceDataAccessLayer.Implementation
{
    public class VisitStatusRepository : ShardGenericRepository<VisitStatus>, IVisitStatusRepository
    {
        private readonly AccountDatabaseContext _context;

        public VisitStatusRepository(AccountDatabaseContext context, ShardMapManagerService<AccountDatabaseContext, AccountDatabaseContext> _shardMapManagerService, IStringLocalizer<AccountDatabaseContext> localizer,
            ILogger<AccountDatabaseContext> logger) : base(context, _shardMapManagerService, localizer, logger)
        {
            _context = context;
        }

    }
}
