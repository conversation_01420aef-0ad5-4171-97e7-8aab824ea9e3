using NUnit.Framework;
using Moq;
using MemberServiceBusinessLayer.QueryHandler;
using MemberServiceDataAccessLayer.Implementation;
using MemberServiceDataAccessLayer;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;

namespace MemberServiceApiBusinessLayerTestCases.QueryHandlerTests
{
    [TestFixture]
    public class VisitTypeQueryHandlerTests
    {
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<IVisitTypeRepository> _mockVisitTypeRepository;
        private VisitTypeQueryHandler _visitTypeQueryHandler;

        [SetUp]
        public void Setup()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockVisitTypeRepository = new Mock<IVisitTypeRepository>();

            _mockUnitOfWork.Setup(u => u.VisitTypeRepository).Returns(_mockVisitTypeRepository.Object);

            _visitTypeQueryHandler = new VisitTypeQueryHandler(_mockUnitOfWork.Object);
        }

        [Test]
        public async Task GetAllVisitTypesAsync_ShouldReturnAllVisitTypes()
        {
            // Arrange
            var expectedVisitTypes = new List<VisitType>
            {
                new VisitType { ID = Guid.NewGuid(), VisitName = "Visit Type 1", CPTCode = "12345" },
                new VisitType { ID = Guid.NewGuid(), VisitName = "Visit Type 2", CPTCode = "67890" }
            };

            _mockVisitTypeRepository.Setup(r => r.GetAllAsync()).ReturnsAsync(expectedVisitTypes);

            // Act
            var result = await _visitTypeQueryHandler.GetAllVisitTypesAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(2));
            Assert.That(result, Is.EquivalentTo(expectedVisitTypes));
            _mockVisitTypeRepository.Verify(r => r.GetAllAsync(), Times.Once);
        }

        [Test]
        public async Task GetByOrganizationIdAsync_ShouldReturnVisitTypes_WhenVisitTypesExist()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var expectedVisitTypes = new List<VisitType>
            {
                new VisitType { ID = Guid.NewGuid(), VisitName = "Visit Type 1", CPTCode = "12345", OrganizationId = orgId },
                new VisitType { ID = Guid.NewGuid(), VisitName = "Visit Type 2", CPTCode = "67890", OrganizationId = orgId }
            };

            _mockVisitTypeRepository.Setup(r => r.GetByOrganizationIdAsync(orgId)).ReturnsAsync(expectedVisitTypes);

            // Act
            var result = await _visitTypeQueryHandler.GetByOrganizationIdAsync(orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(2));
            Assert.That(result, Is.EquivalentTo(expectedVisitTypes));
            _mockVisitTypeRepository.Verify(r => r.GetByOrganizationIdAsync(orgId), Times.Once);
        }

        [Test]
        public async Task GetByOrganizationIdAsync_ShouldReturnEmptyList_WhenNoVisitTypesExist()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var emptyList = new List<VisitType>();

            _mockVisitTypeRepository.Setup(r => r.GetByOrganizationIdAsync(orgId)).ReturnsAsync(emptyList);

            // Act
            var result = await _visitTypeQueryHandler.GetByOrganizationIdAsync(orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(0));
            _mockVisitTypeRepository.Verify(r => r.GetByOrganizationIdAsync(orgId), Times.Once);
        }

        [Test]
        public void Constructor_ShouldThrowArgumentNullException_WhenUnitOfWorkIsNull()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new VisitTypeQueryHandler(null));
        }
    }
}
