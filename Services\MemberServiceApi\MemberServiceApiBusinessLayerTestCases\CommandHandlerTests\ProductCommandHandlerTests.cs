using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using MemberServiceBusinessLayer.CommandHandler;
using MemberServiceDataAccessLayer.Implementation;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MemberServiceApiBusinessLayerTestCases.CommandHandlerTests
{
    [TestFixture]
    public class ProductCommandHandlerTests
    {
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<IProductRepository> _mockProductRepository;
        private Mock<ILogger<ProductCommandHandler>> _mockLogger;
        private ProductCommandHandler _productCommandHandler;

        [SetUp]
        public void Setup()
        {
            _mockConfiguration = new Mock<IConfiguration>();
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockProductRepository = new Mock<IProductRepository>();
            _mockLogger = new Mock<ILogger<ProductCommandHandler>>();

            _mockUnitOfWork.Setup(u => u.ProductRepository).Returns(_mockProductRepository.Object);

            _productCommandHandler = new ProductCommandHandler(
                _mockConfiguration.Object,
                _mockUnitOfWork.Object,
                _mockLogger.Object
            );
        }

        [Test]
        public async Task AddProduct_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var products = new List<ProductDTO>
            {
                new ProductDTO { Id = Guid.NewGuid(), Name = "Product 1", Description = "Description 1" },
                new ProductDTO { Id = Guid.NewGuid(), Name = "Product 2", Description = "Description 2" }
            };

            _mockProductRepository
                .Setup(r => r.AddAsync(products, orgId, subscription))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _productCommandHandler.AddProduct(products, orgId, subscription);

            // Assert
            _mockProductRepository.Verify(r => r.AddAsync(products, orgId, subscription), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateProduct_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var product = new ProductDTO
            {
                Id = Guid.NewGuid(),
                Name = "Product 1",
                Description = "Description 1",
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockProductRepository
                .Setup(r => r.UpdateAsync(product, orgId, subscription))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _productCommandHandler.UpdateProduct(product, orgId, subscription);

            // Assert
            _mockProductRepository.Verify(r => r.UpdateAsync(product, orgId, subscription), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteProductById_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var productId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockProductRepository
                .Setup(r => r.DeleteByIdAsync(productId, orgId, subscription))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _productCommandHandler.DeleteProductById(productId, orgId, subscription);

            // Assert
            _mockProductRepository.Verify(r => r.DeleteByIdAsync(productId, orgId, subscription), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteProductByEntity_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var product = new ProductDTO
            {
                Id = Guid.NewGuid(),
                Name = "Product 1",
                Description = "Description 1",
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockProductRepository
                .Setup(r => r.DeleteByEntityAsync(product, orgId, subscription))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _productCommandHandler.DeleteProductByEntity(product, orgId, subscription);

            // Assert
            _mockProductRepository.Verify(r => r.DeleteByEntityAsync(product, orgId, subscription), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task AddProduct_WhenRepositoryThrowsException_ShouldPropagateException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var products = new List<ProductDTO>
            {
                new ProductDTO { Id = Guid.NewGuid(), Name = "Product 1", Description = "Description 1" }
            };

            var expectedException = new InvalidOperationException("Test exception");

            _mockProductRepository
                .Setup(r => r.AddAsync(products, orgId, subscription))
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<InvalidOperationException>(async () =>
                await _productCommandHandler.AddProduct(products, orgId, subscription));

            Assert.That(exception.Message, Is.EqualTo("Test exception"));
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Never);
        }
    }
}
