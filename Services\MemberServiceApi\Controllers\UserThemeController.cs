﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Contracts;
using MemberServiceBusinessLayer;
using Microsoft.Extensions.Localization;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Identity.Web.Resource;

namespace MemberServiceApi.Controllers
{
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes")]
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class UserThemeController : ControllerBase
    {
        private readonly IUserThemeCommandHandler<UserTheme> _userThemeCommandHandler;
        private readonly IUserThemeQueryHandler<UserTheme> _userThemeQueryHandler;
        private readonly ILogger<UserThemeController> _logger;
        private readonly IStringLocalizer<UserThemeController> _localizer;

        public UserThemeController(
            IUserThemeCommandHandler<UserTheme> userThemeCommandHandler,
            IUserThemeQueryHandler<UserTheme> userThemeQueryHandler,
            ILogger<UserThemeController> logger,
            IStringLocalizer<UserThemeController> localizer)
        {
            _userThemeCommandHandler = userThemeCommandHandler;
            _userThemeQueryHandler = userThemeQueryHandler;
            _logger = logger;
            _localizer = localizer;
        }

        /// <summary>
        /// Gets all user themes.
        /// </summary>
        /// <returns>A list of user themes.</returns>
        [HttpGet]
        public async Task<ActionResult<IEnumerable<UserTheme>>> Get()
        {
            try
            {
                var userThemes = await _userThemeQueryHandler.GetUserThemes();
                return Ok(userThemes);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                return StatusCode(500);
            }
        }

        /// <summary>
        /// Gets a user theme by ID.
        /// </summary>
        /// <param name="id">The ID of the user theme.</param>
        /// <returns>The user theme with the specified ID.</returns>
        [HttpGet("{id:Guid}/{OrgID:Guid}/{Subscription:bool}")]
        public async Task<ActionResult<UserTheme>> GetById(Guid id, Guid OrgID, bool Subscription)
        {
            try
            {
                var userTheme = await _userThemeQueryHandler.GetUserThemeById(id, OrgID, Subscription);
                return Ok(userTheme);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                return StatusCode(500);
            }
        }

        /// <summary>
        /// Updates a user theme by ID.
        /// </summary>
        /// <param name="id">The ID of the user theme.</param>
        /// <param name="userTheme">The updated user theme.</param>
        /// <returns>An IActionResult indicating the result of the operation.</returns>
        [HttpPut("{id:guid}")]
        public async Task<IActionResult> UpdateById(Guid id, [FromBody] UserTheme userTheme)
        {
            if (userTheme == null || userTheme.UserId != id)
            {
                return BadRequest(_localizer["InvalidUserTheme"]);
            }

            try
            {
                await _userThemeCommandHandler.UpdateUserTheme(userTheme, userTheme.OrganizationId, userTheme.Subscription);
                return Ok(_localizer["UpdateSuccessful"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["UpdateLogError"]);
                return StatusCode(500);
            }
        }

        /// <summary>
        /// Deletes a user theme by ID.
        /// </summary>
        /// <param name="id">The ID of the user theme.</param>
        /// <returns>An IActionResult indicating the result of the operation.</returns>
        [HttpDelete("{id:guid}/{OrgID:Guid}/{Subscription:bool}")]
        public async Task<IActionResult> DeleteById(Guid id, Guid OrgID, bool Subscription)
        {
            try
            {
                await _userThemeCommandHandler.DeleteUserThemeById(id, OrgID, Subscription);
                return Ok(_localizer["SuccessfulDeletion"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["DeleteLogError"]);
                return StatusCode(500);
            }
        }

        /// <summary>
        /// Adds a new user theme.
        /// </summary>
        /// <param name="userTheme">The user theme to add.</param>
        /// <returns>An IActionResult indicating the result of the operation.</returns>
        [HttpPost]
        public async Task<IActionResult> AddUserTheme([FromBody] UserTheme userTheme)
        {
            if (userTheme == null)
            {
                return BadRequest(_localizer["InvalidUserTheme"]);
            }

            try
            {
                await _userThemeCommandHandler.AddUserTheme(userTheme, userTheme.OrganizationId, userTheme.Subscription);
                return Ok(_localizer["AddSuccessful"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["AddLogError"]);
                return StatusCode(500);
            }
        }
    }
}
