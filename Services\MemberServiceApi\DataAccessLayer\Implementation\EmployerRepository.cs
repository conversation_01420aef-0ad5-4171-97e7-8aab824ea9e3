﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using DataAccessLayer.Context;
using Contracts;
using Microsoft.EntityFrameworkCore;
using Interfaces.ShardManagement;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;

namespace MemberServiceDataAccessLayer.Implementation
{
    public class EmployerRepository : ShardGenericRepository<Employer>, IEmployerRepository
    {
        private readonly AccountDatabaseContext _context;

        public EmployerRepository(AccountDatabaseContext context, ShardMapManagerService<AccountDatabaseContext, AccountDatabaseContext> _shardMapManagerService, IStringLocalizer<AccountDatabaseContext> localizer,
            ILogger<AccountDatabaseContext> logger) : base(context, _shardMapManagerService, localizer, logger)
        {
            _context = context;
        }


    }
}


