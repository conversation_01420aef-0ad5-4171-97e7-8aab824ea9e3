using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using MemberServiceApi.Controllers;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using MemberServiceBusinessLayer;
using System.Linq;

namespace MemberServiceApiControllerTestCases
{
    [TestFixture]
    public class ProductFeatureControllerTests
    {
        private Mock<IProductFeatureQueryHandler<ProductFeature>> _mockQueryHandler;
        private Mock<IProductFeatureCommandHandler<ProductFeature>> _mockCommandHandler;
        private Mock<ILogger<ProductFeatureController>> _mockLogger;
        private Mock<IStringLocalizer<ProductFeatureController>> _mockLocalizer;
        private ProductFeatureController _controller;

        [SetUp]
        public void Setup()
        {
            _mockQueryHandler = new Mock<IProductFeatureQueryHandler<ProductFeature>>();
            _mockCommandHandler = new Mock<IProductFeatureCommandHandler<ProductFeature>>();
            _mockLogger = new Mock<ILogger<ProductFeatureController>>();
            _mockLocalizer = new Mock<IStringLocalizer<ProductFeatureController>>();

            // Setup localizer to return the key when requested
            _mockLocalizer.Setup(l => l[It.IsAny<string>()]).Returns((string key) => new LocalizedString(key, key));

            _controller = new ProductFeatureController(
                _mockQueryHandler.Object,
                _mockCommandHandler.Object,
                _mockLogger.Object,
                _mockLocalizer.Object
            );
        }

        [Test]
        public async Task GetAllProductFeatures_WhenFeaturesExist_ReturnsOkWithFeatures()
        {
            // Arrange
            var mockFeatures = new List<ProductFeature>
            {
                new ProductFeature
                {
                    Id = Guid.NewGuid(),
                    FeatureName = "Advanced Reporting",
                    ProdId = Guid.NewGuid(),
                    ProdName = "Premium Product",
                    Status = true,
                    Created = DateTime.Now.AddDays(-30)
                },
                new ProductFeature
                {
                    Id = Guid.NewGuid(),
                    FeatureName = "Data Export",
                    ProdId = Guid.NewGuid(),
                    ProdName = "Premium Product",
                    Status = true,
                    Created = DateTime.Now.AddDays(-25)
                }
            };

            _mockQueryHandler
                .Setup(q => q.GetAllProductFeaturesAsync())
                .ReturnsAsync(mockFeatures);

            // Act
            var result = await _controller.GetAllProductFeatures();

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult?.StatusCode, Is.EqualTo(200));
            Assert.That(okResult?.Value, Is.EqualTo(mockFeatures));
        }

        [Test]
        public async Task GetAllProductFeatures_WhenNoFeaturesExist_ReturnsNotFound()
        {
            // Arrange
            _mockQueryHandler
                .Setup(q => q.GetAllProductFeaturesAsync())
                .ReturnsAsync(new List<ProductFeature>());

            // Act
            var result = await _controller.GetAllProductFeatures();

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult?.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task GetAllProductFeatures_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            _mockQueryHandler
                .Setup(q => q.GetAllProductFeaturesAsync())
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetAllProductFeatures();

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task GetProductFeatureById_WhenFeatureExists_ReturnsOkWithFeature()
        {
            // Arrange
            var featureId = Guid.NewGuid();
            var mockFeature = new ProductFeature
            {
                Id = featureId,
                FeatureName = "Advanced Reporting",
                ProdId = Guid.NewGuid(),
                ProdName = "Premium Product",
                Status = true,
                Created = DateTime.Now.AddDays(-30)
            };

            _mockQueryHandler
                .Setup(q => q.GetProductFeatureByIdAsync(featureId))
                .ReturnsAsync(mockFeature);

            // Act
            var result = await _controller.GetProductFeatureById(featureId);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult?.StatusCode, Is.EqualTo(200));
            Assert.That(okResult?.Value, Is.EqualTo(mockFeature));
        }

        [Test]
        public async Task GetProductFeatureById_WhenFeatureDoesNotExist_ReturnsNotFound()
        {
            // Arrange
            var featureId = Guid.NewGuid();

            _mockQueryHandler
                .Setup(q => q.GetProductFeatureByIdAsync(featureId))
                .ReturnsAsync((ProductFeature)null);

            // Act
            var result = await _controller.GetProductFeatureById(featureId);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult?.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task GetProductFeatureById_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var featureId = Guid.NewGuid();

            _mockQueryHandler
                .Setup(q => q.GetProductFeatureByIdAsync(featureId))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetProductFeatureById(featureId);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task AddProductFeature_WhenValidFeature_ReturnsCreatedAtAction()
        {
            // Arrange
            var featureId = Guid.NewGuid();
            var feature = new ProductFeature
            {
                Id = featureId,
                FeatureName = "New Feature",
                ProdId = Guid.NewGuid(),
                ProdName = "Product Name",
                Status = true,
                Created = DateTime.Now
            };

            _mockCommandHandler
                .Setup(c => c.AddProductFeatureAsync(feature))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.AddProductFeature(feature);

            // Assert
            Assert.That(result, Is.InstanceOf<CreatedAtActionResult>());
            var createdResult = result as CreatedAtActionResult;
            Assert.That(createdResult?.StatusCode, Is.EqualTo(201));
            Assert.That(createdResult?.ActionName, Is.EqualTo(nameof(ProductFeatureController.GetProductFeatureById)));
            Assert.That(createdResult?.RouteValues["id"], Is.EqualTo(featureId));
            Assert.That(createdResult?.Value, Is.EqualTo(feature));
        }

        [Test]
        public async Task AddProductFeature_WhenNullFeature_ReturnsBadRequest()
        {
            // Act
            var result = await _controller.AddProductFeature(null);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult?.StatusCode, Is.EqualTo(400));
        }

        [Test]
        public async Task AddProductFeature_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var featureId = Guid.NewGuid();
            var feature = new ProductFeature
            {
                Id = featureId,
                FeatureName = "New Feature",
                ProdId = Guid.NewGuid(),
                ProdName = "Product Name",
                Status = true,
                Created = DateTime.Now
            };

            _mockCommandHandler
                .Setup(c => c.AddProductFeatureAsync(feature))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.AddProductFeature(feature);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task UpdateProductFeature_WhenValidFeature_ReturnsNoContent()
        {
            // Arrange
            var featureId = Guid.NewGuid();
            var feature = new ProductFeature
            {
                Id = featureId,
                FeatureName = "Updated Feature",
                ProdId = Guid.NewGuid(),
                ProdName = "Product Name",
                Status = true,
                Created = DateTime.Now,
                Updated = DateTime.Now
            };

            _mockCommandHandler
                .Setup(c => c.UpdateProductFeatureAsync(feature))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.UpdateProductFeature(featureId, feature);

            // Assert
            Assert.That(result, Is.InstanceOf<NoContentResult>());
            var noContentResult = result as NoContentResult;
            Assert.That(noContentResult?.StatusCode, Is.EqualTo(204));
        }

        [Test]
        public async Task UpdateProductFeature_WhenNullFeature_ReturnsBadRequest()
        {
            // Arrange
            var featureId = Guid.NewGuid();

            // Act
            var result = await _controller.UpdateProductFeature(featureId, null);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult?.StatusCode, Is.EqualTo(400));
        }

        [Test]
        public async Task UpdateProductFeature_WhenIdMismatch_ReturnsBadRequest()
        {
            // Arrange
            var featureId = Guid.NewGuid();
            var differentId = Guid.NewGuid();
            var feature = new ProductFeature
            {
                Id = differentId,
                FeatureName = "Updated Feature",
                ProdId = Guid.NewGuid(),
                ProdName = "Product Name",
                Status = true,
                Created = DateTime.Now,
                Updated = DateTime.Now
            };

            // Act
            var result = await _controller.UpdateProductFeature(featureId, feature);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult?.StatusCode, Is.EqualTo(400));
        }

        [Test]
        public async Task UpdateProductFeature_WhenFeatureDoesNotExist_ReturnsNotFound()
        {
            // Arrange
            var featureId = Guid.NewGuid();
            var feature = new ProductFeature
            {
                Id = featureId,
                FeatureName = "Updated Feature",
                ProdId = Guid.NewGuid(),
                ProdName = "Product Name",
                Status = true,
                Created = DateTime.Now,
                Updated = DateTime.Now
            };

            _mockCommandHandler
                .Setup(c => c.UpdateProductFeatureAsync(feature))
                .ThrowsAsync(new KeyNotFoundException());

            // Act
            var result = await _controller.UpdateProductFeature(featureId, feature);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult?.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task UpdateProductFeature_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var featureId = Guid.NewGuid();
            var feature = new ProductFeature
            {
                Id = featureId,
                FeatureName = "Updated Feature",
                ProdId = Guid.NewGuid(),
                ProdName = "Product Name",
                Status = true,
                Created = DateTime.Now,
                Updated = DateTime.Now
            };

            _mockCommandHandler
                .Setup(c => c.UpdateProductFeatureAsync(feature))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.UpdateProductFeature(featureId, feature);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task DeleteProductFeature_WhenFeatureExists_ReturnsNoContent()
        {
            // Arrange
            var featureId = Guid.NewGuid();

            _mockCommandHandler
                .Setup(c => c.DeleteProductFeatureAsync(featureId))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.DeleteProductFeature(featureId);

            // Assert
            Assert.That(result, Is.InstanceOf<NoContentResult>());
            var noContentResult = result as NoContentResult;
            Assert.That(noContentResult?.StatusCode, Is.EqualTo(204));
        }

        [Test]
        public async Task DeleteProductFeature_WhenFeatureDoesNotExist_ReturnsNotFound()
        {
            // Arrange
            var featureId = Guid.NewGuid();

            _mockCommandHandler
                .Setup(c => c.DeleteProductFeatureAsync(featureId))
                .ThrowsAsync(new KeyNotFoundException());

            // Act
            var result = await _controller.DeleteProductFeature(featureId);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult?.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task DeleteProductFeature_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var featureId = Guid.NewGuid();

            _mockCommandHandler
                .Setup(c => c.DeleteProductFeatureAsync(featureId))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.DeleteProductFeature(featureId);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }
    }
}
