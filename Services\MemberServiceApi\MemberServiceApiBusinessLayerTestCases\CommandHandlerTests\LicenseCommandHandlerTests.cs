using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Configuration;
using MemberServiceBusinessLayer.CommandHandler;
using MemberServiceDataAccessLayer.Implementation;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MemberServiceApiBusinessLayerTestCases.CommandHandlerTests
{
    [TestFixture]
    public class LicenseCommandHandlerTests
    {
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<ILicenseRepository> _mockProductLicenseRepository;
        private LicenseCommandHandler _licenseCommandHandler;

        [SetUp]
        public void Setup()
        {
            _mockConfiguration = new Mock<IConfiguration>();
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockProductLicenseRepository = new Mock<ILicenseRepository>();

            _mockUnitOfWork.Setup(u => u.ProductLicenseRepository).Returns(_mockProductLicenseRepository.Object);

            _licenseCommandHandler = new LicenseCommandHandler(
                _mockConfiguration.Object,
                _mockUnitOfWork.Object
            );
        }

        [Test]
        public async Task AddLicense_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var licenses = new List<ProductLicense>
            {
                new ProductLicense
                {
                    Id = Guid.NewGuid(),
                    ProductName = "Test Product",
                    Description = "Test Description",
                    IsLicenseActivated = true,
                    OrganizationId = orgId,
                    Subscription = subscription
                }
            };

            _mockProductLicenseRepository
                .Setup(r => r.AddAsync(It.IsAny<List<ProductLicense>>(), orgId, subscription))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _licenseCommandHandler.AddLicense(licenses);

            // Assert
            _mockProductLicenseRepository.Verify(r => r.AddAsync(It.Is<List<ProductLicense>>(list =>
                list.Count == 1 && list[0] == licenses[0]), orgId, subscription), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateLicense_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var license = new ProductLicense
            {
                Id = Guid.NewGuid(),
                ProductName = "Test Product",
                Description = "Updated Description",
                IsLicenseActivated = true,
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockProductLicenseRepository
                .Setup(r => r.UpdateAsync(license, orgId, subscription))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _licenseCommandHandler.UpdateLicense(license, orgId, subscription);

            // Assert
            _mockProductLicenseRepository.Verify(r => r.UpdateAsync(license, orgId, subscription), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteLicenseById_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var licenseId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockProductLicenseRepository
                .Setup(r => r.DeleteByIdAsync(licenseId, orgId, subscription))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _licenseCommandHandler.DeleteLicenseById(licenseId, orgId, subscription);

            // Assert
            _mockProductLicenseRepository.Verify(r => r.DeleteByIdAsync(licenseId, orgId, subscription), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteLicenseByEntity_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var license = new ProductLicense
            {
                Id = Guid.NewGuid(),
                ProductName = "Test Product",
                Description = "Test Description",
                IsLicenseActivated = true,
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockProductLicenseRepository
                .Setup(r => r.DeleteByEntityAsync(license, orgId, subscription))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _licenseCommandHandler.DeleteLicenseByEntity(license, orgId, subscription);

            // Assert
            _mockProductLicenseRepository.Verify(r => r.DeleteByEntityAsync(license, orgId, subscription), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateLicenseAccessAsync_ShouldReturnTrue_WhenSuccessful()
        {
            // Arrange
            var licenseId = Guid.NewGuid();
            var licenses = new List<ProductLicense>
            {
                new ProductLicense
                {
                    Id = licenseId,
                    ProductName = "Test Product",
                    Description = "Test Description",
                    IsLicenseActivated = true,
                    OrganizationId = Guid.NewGuid(),
                    Subscription = false
                }
            };

            var existingLicenses = new List<ProductLicense>
            {
                new ProductLicense
                {
                    Id = licenseId,
                    ProductName = "Test Product",
                    Description = "Test Description",
                    IsLicenseActivated = false,
                    OrganizationId = Guid.NewGuid(),
                    Subscription = false
                }
            };

            _mockProductLicenseRepository
                .Setup(r => r.GetAllAsync())
                .ReturnsAsync(existingLicenses);

            _mockProductLicenseRepository
                .Setup(r => r.UpdateAsync(It.IsAny<ProductLicense>(), It.IsAny<Guid>(), It.IsAny<bool>()))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            var result = await _licenseCommandHandler.UpdateLicenseAccessAsync(licenses);

            // Assert
            Assert.That(result, Is.True);
            _mockProductLicenseRepository.Verify(r => r.GetAllAsync(), Times.Once);
            _mockProductLicenseRepository.Verify(r => r.UpdateAsync(It.IsAny<ProductLicense>(), It.IsAny<Guid>(), It.IsAny<bool>()), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }
    }
}
