﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DataAccessLayer.Context;
using Contracts;
using Microsoft.EntityFrameworkCore;
using Interfaces.ShardManagement;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;

namespace MemberServiceDataAccessLayer.Implementation
{
    public class FacilityRepository : ShardGenericRepository<Facility>, IFacilityRepository
    {
        private readonly ShardMapManagerService<AccountDatabaseContext, AccountDatabaseContext> _shardMapManagerService;
        private readonly AccountDatabaseContext _context;
        private readonly string _shardMapName;

        public FacilityRepository(AccountDatabaseContext context, ShardMapManagerService<AccountDatabaseContext, AccountDatabaseContext> shardMapManagerService, IStringLocalizer<AccountDatabaseContext> localizer,
            ILogger<AccountDatabaseContext> logger) : base(context, shardMapManagerService, localizer, logger)
        {
            _context = context;
            _shardMapManagerService = shardMapManagerService;
            _shardMapName = Environment.GetEnvironmentVariable("shardMapName");
        }

        public async Task<List<Facility>> GetFacilitiesByNameAsync(string name, Guid orgId, bool subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(subscription, orgId, _shardMapName);
            if (context == null) return new List<Facility>();

            var result = await context.Facility
                .Where(facility => EF.Functions.Like(facility.FacilityName, $"%{name}%"))
                .ToListAsync();

            return result;
        }

    }
}
