using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using MemberServiceApi.Controllers;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using MemberServiceBusinessLayer;
using System.Linq;

namespace MemberServiceApiControllerTestCases
{
    [TestFixture]
    public class UserThemeControllerTests
    {
        private Mock<IUserThemeQueryHandler<UserTheme>> _mockQueryHandler;
        private Mock<IUserThemeCommandHandler<UserTheme>> _mockCommandHandler;
        private Mock<ILogger<UserThemeController>> _mockLogger;
        private Mock<IStringLocalizer<UserThemeController>> _mockLocalizer;
        private UserThemeController _controller;

        [SetUp]
        public void Setup()
        {
            _mockQueryHandler = new Mock<IUserThemeQueryHandler<UserTheme>>();
            _mockCommandHandler = new Mock<IUserThemeCommandHandler<UserTheme>>();
            _mockLogger = new Mock<ILogger<UserThemeController>>();
            _mockLocalizer = new Mock<IStringLocalizer<UserThemeController>>();

            // Setup localizer to return the key when requested
            _mockLocalizer.Setup(l => l[It.IsAny<string>()]).Returns((string key) => new LocalizedString(key, key));

            _controller = new UserThemeController(
                _mockCommandHandler.Object,
                _mockQueryHandler.Object,
                _mockLogger.Object,
                _mockLocalizer.Object
            );
        }

        [Test]
        public async Task Get_WhenThemesExist_ReturnsOkWithThemes()
        {
            // Arrange
            var mockThemes = new List<UserTheme>
            {
                new UserTheme
                {
                    UserId = Guid.NewGuid(),
                    ThemeName = "Dark Theme",
                    OrganizationId = Guid.NewGuid(),
                    Subscription = false
                },
                new UserTheme
                {
                    UserId = Guid.NewGuid(),
                    ThemeName = "Light Theme",
                    OrganizationId = Guid.NewGuid(),
                    Subscription = true
                }
            };

            _mockQueryHandler
                .Setup(q => q.GetUserThemes())
                .ReturnsAsync(mockThemes);

            // Act
            var result = await _controller.Get();

            // Assert
            Assert.That(result.Result, Is.InstanceOf<OkObjectResult>());
            var okResult = result.Result as OkObjectResult;
            Assert.That(okResult?.StatusCode, Is.EqualTo(200));
            Assert.That(okResult?.Value, Is.EqualTo(mockThemes));
        }

        [Test]
        public async Task Get_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            _mockQueryHandler
                .Setup(q => q.GetUserThemes())
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.Get();

            // Assert
            Assert.That(result.Result, Is.InstanceOf<StatusCodeResult>());
            var statusCodeResult = result.Result as StatusCodeResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task GetById_WhenThemeExists_ReturnsOkWithTheme()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var mockTheme = new UserTheme
            {
                UserId = userId,
                ThemeName = "Dark Theme",
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockQueryHandler
                .Setup(q => q.GetUserThemeById(userId, orgId, subscription))
                .ReturnsAsync(mockTheme);

            // Act
            var result = await _controller.GetById(userId, orgId, subscription);

            // Assert
            Assert.That(result.Result, Is.InstanceOf<OkObjectResult>());
            var okResult = result.Result as OkObjectResult;
            Assert.That(okResult?.StatusCode, Is.EqualTo(200));
            Assert.That(okResult?.Value, Is.EqualTo(mockTheme));
        }

        [Test]
        public async Task GetById_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockQueryHandler
                .Setup(q => q.GetUserThemeById(userId, orgId, subscription))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetById(userId, orgId, subscription);

            // Assert
            Assert.That(result.Result, Is.InstanceOf<StatusCodeResult>());
            var statusCodeResult = result.Result as StatusCodeResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task UpdateById_WhenValidTheme_ReturnsOk()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var theme = new UserTheme
            {
                UserId = userId,
                ThemeName = "Updated Theme",
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockCommandHandler
                .Setup(c => c.UpdateUserTheme(theme, orgId, subscription))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.UpdateById(userId, theme);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult?.StatusCode, Is.EqualTo(200));
        }

        [Test]
        public async Task UpdateById_WhenNullTheme_ReturnsBadRequest()
        {
            // Arrange
            var userId = Guid.NewGuid();

            // Act
            var result = await _controller.UpdateById(userId, null);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult?.StatusCode, Is.EqualTo(400));
        }

        [Test]
        public async Task UpdateById_WhenIdMismatch_ReturnsBadRequest()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var differentId = Guid.NewGuid();
            var theme = new UserTheme
            {
                UserId = differentId,
                ThemeName = "Updated Theme",
                OrganizationId = Guid.NewGuid(),
                Subscription = false
            };

            // Act
            var result = await _controller.UpdateById(userId, theme);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult?.StatusCode, Is.EqualTo(400));
        }

        [Test]
        public async Task UpdateById_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var theme = new UserTheme
            {
                UserId = userId,
                ThemeName = "Updated Theme",
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockCommandHandler
                .Setup(c => c.UpdateUserTheme(theme, orgId, subscription))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.UpdateById(userId, theme);

            // Assert
            Assert.That(result, Is.InstanceOf<StatusCodeResult>());
            var statusCodeResult = result as StatusCodeResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task DeleteById_WhenThemeExists_ReturnsOk()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockCommandHandler
                .Setup(c => c.DeleteUserThemeById(userId, orgId, subscription))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.DeleteById(userId, orgId, subscription);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult?.StatusCode, Is.EqualTo(200));
        }

        [Test]
        public async Task DeleteById_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockCommandHandler
                .Setup(c => c.DeleteUserThemeById(userId, orgId, subscription))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.DeleteById(userId, orgId, subscription);

            // Assert
            Assert.That(result, Is.InstanceOf<StatusCodeResult>());
            var statusCodeResult = result as StatusCodeResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task AddUserTheme_WhenValidTheme_ReturnsOk()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var theme = new UserTheme
            {
                UserId = userId,
                ThemeName = "New Theme",
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockCommandHandler
                .Setup(c => c.AddUserTheme(theme, orgId, subscription))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.AddUserTheme(theme);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult?.StatusCode, Is.EqualTo(200));
        }

        [Test]
        public async Task AddUserTheme_WhenNullTheme_ReturnsBadRequest()
        {
            // Act
            var result = await _controller.AddUserTheme(null);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult?.StatusCode, Is.EqualTo(400));
        }

        [Test]
        public async Task AddUserTheme_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var theme = new UserTheme
            {
                UserId = userId,
                ThemeName = "New Theme",
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockCommandHandler
                .Setup(c => c.AddUserTheme(theme, orgId, subscription))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.AddUserTheme(theme);

            // Assert
            Assert.That(result, Is.InstanceOf<StatusCodeResult>());
            var statusCodeResult = result as StatusCodeResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }
    }
}
