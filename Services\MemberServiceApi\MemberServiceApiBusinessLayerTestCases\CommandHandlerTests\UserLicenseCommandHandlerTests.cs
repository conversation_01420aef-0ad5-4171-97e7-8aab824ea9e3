using NUnit.Framework;
using Moq;
using MemberServiceBusinessLayer.CommandHandler;
using MemberServiceDataAccessLayer.Implementation;
using Contracts;
using System;
using System.Threading.Tasks;

namespace MemberServiceApiBusinessLayerTestCases.CommandHandlerTests
{
    [TestFixture]
    public class UserLicenseCommandHandlerTests
    {
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<IUserLicenseRepository> _mockUserLicenseRepository;
        private UserLicenseCommandHandler _userLicenseCommandHandler;

        [SetUp]
        public void Setup()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockUserLicenseRepository = new Mock<IUserLicenseRepository>();

            _mockUnitOfWork.Setup(u => u.UserLicenseRepository).Returns(_mockUserLicenseRepository.Object);

            _userLicenseCommandHandler = new UserLicenseCommandHandler(_mockUnitOfWork.Object);
        }

        [Test]
        public async Task AddUserLicenseAsync_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var userLicense = new UserLicense
            {
                Id = Guid.NewGuid(),
                PlanId = Guid.NewGuid(),
                OrganizationId = Guid.NewGuid(),
                ProductId = Guid.NewGuid(),
                Seats = 10,
                ActiveUsers = 0,
                CreatedDate = DateTime.Now,
                CreatedBy = Guid.NewGuid(),
                Status = true,
                ExpiryDate = DateTime.Now.AddYears(1)
            };

            _mockUserLicenseRepository
                .Setup(r => r.AddAsync(userLicense))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _userLicenseCommandHandler.AddUserLicenseAsync(userLicense);

            // Assert
            _mockUserLicenseRepository.Verify(r => r.AddAsync(userLicense), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateUserLicenseAsync_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var userLicense = new UserLicense
            {
                Id = Guid.NewGuid(),
                PlanId = Guid.NewGuid(),
                OrganizationId = Guid.NewGuid(),
                ProductId = Guid.NewGuid(),
                Seats = 10,
                ActiveUsers = 5,
                CreatedDate = DateTime.Now.AddDays(-30),
                UpdatedDate = DateTime.Now,
                CreatedBy = Guid.NewGuid(),
                UpdatedBy = Guid.NewGuid(),
                Status = true,
                ExpiryDate = DateTime.Now.AddYears(1)
            };

            _mockUserLicenseRepository
                .Setup(r => r.UpdateAsync(userLicense))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _userLicenseCommandHandler.UpdateUserLicenseAsync(userLicense);

            // Assert
            _mockUserLicenseRepository.Verify(r => r.UpdateAsync(userLicense), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteUserLicenseAsync_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var userLicenseId = Guid.NewGuid();

            _mockUserLicenseRepository
                .Setup(r => r.DeleteByIdAsync(userLicenseId))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _userLicenseCommandHandler.DeleteUserLicenseAsync(userLicenseId);

            // Assert
            _mockUserLicenseRepository.Verify(r => r.DeleteByIdAsync(userLicenseId), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task IncrementActiveUserCountAsync_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            var userLicense = new UserLicense
            {
                Id = Guid.NewGuid(),
                OrganizationId = organizationId,
                ActiveUsers = 5,
                Seats = 10
            };

            _mockUserLicenseRepository
                .Setup(r => r.GetByOrganizationIdAsync(organizationId))
                .ReturnsAsync(userLicense);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _userLicenseCommandHandler.IncrementActiveUserCountAsync(organizationId);

            // Assert
            Assert.That(userLicense.ActiveUsers, Is.EqualTo(6));
            _mockUserLicenseRepository.Verify(r => r.GetByOrganizationIdAsync(organizationId), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task ResetActiveUsersAsync_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            var userLicense = new UserLicense
            {
                Id = Guid.NewGuid(),
                OrganizationId = organizationId,
                ActiveUsers = 1
            };

            _mockUserLicenseRepository
                .Setup(r => r.GetByOrganizationIdAsync(organizationId))
                .ReturnsAsync(userLicense);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _userLicenseCommandHandler.ResetActiveUsersAsync(organizationId);

            // Assert
            Assert.That(userLicense.ActiveUsers, Is.EqualTo(0));
            _mockUserLicenseRepository.Verify(r => r.GetByOrganizationIdAsync(organizationId), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public void AddUserLicenseAsync_ShouldThrowArgumentNullException_WhenLicenseIsNull()
        {
            // Act & Assert
            Assert.ThrowsAsync<ArgumentNullException>(async () =>
                await _userLicenseCommandHandler.AddUserLicenseAsync(null));

            _mockUserLicenseRepository.Verify(r => r.AddAsync(It.IsAny<UserLicense>()), Times.Never);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Never);
        }

        [Test]
        public void UpdateUserLicenseAsync_ShouldThrowArgumentNullException_WhenLicenseIsNull()
        {
            // Act & Assert
            Assert.ThrowsAsync<ArgumentNullException>(async () =>
                await _userLicenseCommandHandler.UpdateUserLicenseAsync(null));

            _mockUserLicenseRepository.Verify(r => r.UpdateAsync(It.IsAny<UserLicense>()), Times.Never);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Never);
        }
    }
}
