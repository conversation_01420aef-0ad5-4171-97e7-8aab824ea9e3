using NUnit.Framework;
using Moq;
using MemberServiceBusinessLayer.CommandHandler;
using MemberServiceDataAccessLayer.Implementation;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MemberServiceApiBusinessLayerTestCases.CommandHandlerTests
{
    [TestFixture]
    public class FacilityCommandHandlerTests
    {
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<IFacilityRepository> _mockFacilityRepository;
        private FacilityCommandHandler _facilityCommandHandler;

        [SetUp]
        public void Setup()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockFacilityRepository = new Mock<IFacilityRepository>();

            _mockUnitOfWork.Setup(u => u.FacilityRepository).Returns(_mockFacilityRepository.Object);

            _facilityCommandHandler = new FacilityCommandHandler(_mockUnitOfWork.Object);
        }

        [Test]
        public async Task AddFacilityAsync_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var facilities = new List<Facility>
            {
                new Facility { FacilityId = Guid.NewGuid(), FacilityName = "Facility 1" },
                new Facility { FacilityId = Guid.NewGuid(), FacilityName = "Facility 2" }
            };

            _mockFacilityRepository
                .Setup(r => r.AddAsync(facilities, orgId, subscription))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _facilityCommandHandler.AddFacilityAsync(facilities, subscription, orgId);

            // Assert
            _mockFacilityRepository.Verify(r => r.AddAsync(facilities, orgId, subscription), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateFacilityAsync_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var facility = new Facility
            {
                FacilityId = Guid.NewGuid(),
                FacilityName = "Facility 1",
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockFacilityRepository
                .Setup(r => r.UpdateAsync(facility, orgId, subscription))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _facilityCommandHandler.UpdateFacilityAsync(facility, subscription, orgId);

            // Assert
            _mockFacilityRepository.Verify(r => r.UpdateAsync(facility, orgId, subscription), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteFacilityAsync_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var facilityId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockFacilityRepository
                .Setup(r => r.DeleteByIdAsync(facilityId, orgId, subscription))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _facilityCommandHandler.DeleteFacilityAsync(subscription, facilityId, orgId);

            // Assert
            _mockFacilityRepository.Verify(r => r.DeleteByIdAsync(facilityId, orgId, subscription), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task AddFacilityAsync_WhenRepositoryThrowsException_ShouldPropagateException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var facilities = new List<Facility>
            {
                new Facility { FacilityId = Guid.NewGuid(), FacilityName = "Facility 1" }
            };

            var expectedException = new InvalidOperationException("Test exception");

            _mockFacilityRepository
                .Setup(r => r.AddAsync(facilities, orgId, subscription))
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<InvalidOperationException>(async () =>
                await _facilityCommandHandler.AddFacilityAsync(facilities, subscription, orgId));

            Assert.That(exception.Message, Is.EqualTo("Test exception"));
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Never);
        }

        [Test]
        public async Task UpdateFacilityAsync_WhenRepositoryThrowsException_ShouldPropagateException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var facility = new Facility
            {
                FacilityId = Guid.NewGuid(),
                FacilityName = "Facility 1",
                OrganizationId = orgId,
                Subscription = subscription
            };

            var expectedException = new InvalidOperationException("Test exception");

            _mockFacilityRepository
                .Setup(r => r.UpdateAsync(facility, orgId, subscription))
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<InvalidOperationException>(async () =>
                await _facilityCommandHandler.UpdateFacilityAsync(facility, subscription, orgId));

            Assert.That(exception.Message, Is.EqualTo("Test exception"));
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Never);
        }
    }
}
