using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using MemberServiceApi.Controllers;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using MemberServiceBusinessLayer;
using System.Linq;

namespace MemberServiceApiControllerTestCases
{
    [TestFixture]
    public class PagePathControllerTests
    {
        private Mock<IPagePathQueryHandler<PagePath>> _mockQueryHandler;
        private Mock<IPagePathCommandHandler<PagePath>> _mockCommandHandler;
        private Mock<ILogger<PagePathController>> _mockLogger;
        private Mock<IStringLocalizer<PagePathController>> _mockLocalizer;
        private PagePathController _controller;

        [SetUp]
        public void Setup()
        {
            _mockQueryHandler = new Mock<IPagePathQueryHandler<PagePath>>();
            _mockCommandHandler = new Mock<IPagePathCommandHandler<PagePath>>();
            _mockLogger = new Mock<ILogger<PagePathController>>();
            _mockLocalizer = new Mock<IStringLocalizer<PagePathController>>();

            _controller = new PagePathController(
                _mockQueryHandler.Object,
                _mockCommandHandler.Object,
                _mockLogger.Object,
                _mockLocalizer.Object
            );
        }

        [Test]
        public async Task GetAllPagePaths_WhenPagePathsExist_ReturnsOkWithPagePaths()
        {
            // Arrange
            var mockPagePaths = new List<PagePath>
            {
                new PagePath { PageId = Guid.NewGuid(), PagePathValue = "/home" },
                new PagePath { PageId = Guid.NewGuid(), PagePathValue = "/dashboard" }
            };

            _mockQueryHandler
                .Setup(q => q.GetAllPagePathsAsync())
                .ReturnsAsync(mockPagePaths);

            // Act
            var result = await _controller.GetAllPagePaths();

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult?.StatusCode, Is.EqualTo(200));
            Assert.That(okResult?.Value, Is.EqualTo(mockPagePaths));
        }

        [Test]
        public async Task GetAllPagePaths_WhenNoPagePathsExist_ReturnsNotFound()
        {
            // Arrange
            _mockQueryHandler
                .Setup(q => q.GetAllPagePathsAsync())
                .ReturnsAsync(new List<PagePath>());

            // Act
            var result = await _controller.GetAllPagePaths();

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult?.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task GetAllPagePaths_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            _mockQueryHandler
                .Setup(q => q.GetAllPagePathsAsync())
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetAllPagePaths();

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task GetPagePathById_WhenPagePathExists_ReturnsOkWithPagePath()
        {
            // Arrange
            var pagePathId = Guid.NewGuid();
            var mockPagePath = new PagePath
            {
                PageId = pagePathId,
                PagePathValue = "/home"
            };

            _mockQueryHandler
                .Setup(q => q.GetPagePathByIdAsync(pagePathId))
                .ReturnsAsync(mockPagePath);

            // Act
            var result = await _controller.GetPagePathById(pagePathId);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult?.StatusCode, Is.EqualTo(200));
            Assert.That(okResult?.Value, Is.EqualTo(mockPagePath));
        }

        [Test]
        public async Task GetPagePathById_WhenPagePathDoesNotExist_ReturnsNotFound()
        {
            // Arrange
            var pagePathId = Guid.NewGuid();

            _mockQueryHandler
                .Setup(q => q.GetPagePathByIdAsync(pagePathId))
                .ReturnsAsync((PagePath)null);

            // Act
            var result = await _controller.GetPagePathById(pagePathId);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult?.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task GetPagePathById_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var pagePathId = Guid.NewGuid();

            _mockQueryHandler
                .Setup(q => q.GetPagePathByIdAsync(pagePathId))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetPagePathById(pagePathId);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task AddPagePath_WhenValidPagePath_ReturnsCreatedAtAction()
        {
            // Arrange
            var pagePathId = Guid.NewGuid();
            var pagePath = new PagePath
            {
                PageId = pagePathId,
                PagePathValue = "/new-path"
            };

            _mockCommandHandler
                .Setup(c => c.AddPagePathAsync(pagePath))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.AddPagePath(pagePath);

            // Assert
            Assert.That(result, Is.InstanceOf<CreatedAtActionResult>());
            var createdResult = result as CreatedAtActionResult;
            Assert.That(createdResult?.StatusCode, Is.EqualTo(201));
            Assert.That(createdResult?.ActionName, Is.EqualTo(nameof(PagePathController.GetPagePathById)));
            Assert.That(createdResult?.RouteValues["id"], Is.EqualTo(pagePathId));
            Assert.That(createdResult?.Value, Is.EqualTo(pagePath));
        }

        [Test]
        public async Task AddPagePath_WhenNullPagePath_ReturnsBadRequest()
        {
            // Act
            var result = await _controller.AddPagePath(null);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult?.StatusCode, Is.EqualTo(400));
        }

        [Test]
        public async Task AddPagePath_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var pagePathId = Guid.NewGuid();
            var pagePath = new PagePath
            {
                PageId = pagePathId,
                PagePathValue = "/new-path"
            };

            _mockCommandHandler
                .Setup(c => c.AddPagePathAsync(pagePath))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.AddPagePath(pagePath);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task UpdatePagePath_WhenValidPagePath_ReturnsNoContent()
        {
            // Arrange
            var pagePathId = Guid.NewGuid();
            var pagePath = new PagePath
            {
                PageId = pagePathId,
                PagePathValue = "/updated-path"
            };

            _mockCommandHandler
                .Setup(c => c.UpdatePagePathAsync(pagePath))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.UpdatePagePath(pagePathId, pagePath);

            // Assert
            Assert.That(result, Is.InstanceOf<NoContentResult>());
            var noContentResult = result as NoContentResult;
            Assert.That(noContentResult?.StatusCode, Is.EqualTo(204));
        }

        [Test]
        public async Task UpdatePagePath_WhenNullPagePath_ReturnsBadRequest()
        {
            // Arrange
            var pagePathId = Guid.NewGuid();

            // Act
            var result = await _controller.UpdatePagePath(pagePathId, null);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult?.StatusCode, Is.EqualTo(400));
        }

        [Test]
        public async Task UpdatePagePath_WhenIdMismatch_ReturnsBadRequest()
        {
            // Arrange
            var pagePathId = Guid.NewGuid();
            var differentId = Guid.NewGuid();
            var pagePath = new PagePath
            {
                PageId = differentId,
                PagePathValue = "/updated-path"
            };

            // Act
            var result = await _controller.UpdatePagePath(pagePathId, pagePath);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult?.StatusCode, Is.EqualTo(400));
        }

        [Test]
        public async Task UpdatePagePath_WhenPagePathDoesNotExist_ReturnsNotFound()
        {
            // Arrange
            var pagePathId = Guid.NewGuid();
            var pagePath = new PagePath
            {
                PageId = pagePathId,
                PagePathValue = "/updated-path"
            };

            _mockCommandHandler
                .Setup(c => c.UpdatePagePathAsync(pagePath))
                .ThrowsAsync(new KeyNotFoundException());

            // Act
            var result = await _controller.UpdatePagePath(pagePathId, pagePath);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult?.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task UpdatePagePath_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var pagePathId = Guid.NewGuid();
            var pagePath = new PagePath
            {
                PageId = pagePathId,
                PagePathValue = "/updated-path"
            };

            _mockCommandHandler
                .Setup(c => c.UpdatePagePathAsync(pagePath))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.UpdatePagePath(pagePathId, pagePath);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task DeletePagePath_WhenPagePathExists_ReturnsNoContent()
        {
            // Arrange
            var pagePathId = Guid.NewGuid();

            _mockCommandHandler
                .Setup(c => c.DeletePagePathAsync(pagePathId))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.DeletePagePath(pagePathId);

            // Assert
            Assert.That(result, Is.InstanceOf<NoContentResult>());
            var noContentResult = result as NoContentResult;
            Assert.That(noContentResult?.StatusCode, Is.EqualTo(204));
        }

        [Test]
        public async Task DeletePagePath_WhenPagePathDoesNotExist_ReturnsNotFound()
        {
            // Arrange
            var pagePathId = Guid.NewGuid();

            _mockCommandHandler
                .Setup(c => c.DeletePagePathAsync(pagePathId))
                .ThrowsAsync(new KeyNotFoundException());

            // Act
            var result = await _controller.DeletePagePath(pagePathId);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult?.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task DeletePagePath_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var pagePathId = Guid.NewGuid();

            _mockCommandHandler
                .Setup(c => c.DeletePagePathAsync(pagePathId))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.DeletePagePath(pagePathId);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }
    }
}
