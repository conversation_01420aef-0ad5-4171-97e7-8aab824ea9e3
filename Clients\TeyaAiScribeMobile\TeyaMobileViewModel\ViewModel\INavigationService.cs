﻿using Microsoft.AspNetCore.Components;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaMobileViewModel.ViewModel
{
    public interface INavigationService
    {
        Task NavigateToAsync(string url, bool addToHistory = true);
        Task GoBackAsync();
        bool CanGoBack { get; }
        event Action? NavigationStateChanged;
        void Initialize(NavigationManager navigationManager);
    }
}
