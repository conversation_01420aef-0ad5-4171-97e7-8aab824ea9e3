﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Contracts;

namespace MemberServiceDataAccessLayer.Implementation
{
    public interface IProductFeatureRepository
    {
        Task AddAsync(ProductFeature feature);
        Task UpdateAsync(ProductFeature feature);
        Task DeleteByIdAsync(Guid id);
        Task<ProductFeature> GetByIdAsync(Guid id);
        Task<List<ProductFeature>> GetAllAsync();
    }
}
