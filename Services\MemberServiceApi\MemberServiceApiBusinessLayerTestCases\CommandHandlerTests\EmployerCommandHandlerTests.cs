using NUnit.Framework;
using Moq;
using MemberServiceBusinessLayer.CommandHandler;
using MemberServiceDataAccessLayer.Implementation;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MemberServiceApiBusinessLayerTestCases.CommandHandlerTests
{
    [TestFixture]
    public class EmployerCommandHandlerTests
    {
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<IEmployerRepository> _mockEmployerRepository;
        private EmployerCommandHandler _employerCommandHandler;

        [SetUp]
        public void Setup()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockEmployerRepository = new Mock<IEmployerRepository>();

            _mockUnitOfWork.Setup(u => u.EmployerRepository).Returns(_mockEmployerRepository.Object);

            _employerCommandHandler = new EmployerCommandHandler(_mockUnitOfWork.Object);
        }

        [Test]
        public async Task AddEmployerAsync_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var employers = new List<Employer>
            {
                new Employer
                {
                    EmployerId = Guid.NewGuid(),
                    EmployerName = "Employer 1",
                    OrganizationId = orgId,
                    Subscription = subscription
                },
                new Employer
                {
                    EmployerId = Guid.NewGuid(),
                    EmployerName = "Employer 2",
                    OrganizationId = orgId,
                    Subscription = subscription
                }
            };

            _mockEmployerRepository
                .Setup(r => r.AddAsync(employers, orgId, subscription))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _employerCommandHandler.AddEmployerAsync(employers, orgId, subscription);

            // Assert
            _mockEmployerRepository.Verify(r => r.AddAsync(employers, orgId, subscription), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateEmployerAsync_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var employer = new Employer
            {
                EmployerId = Guid.NewGuid(),
                EmployerName = "Updated Employer",
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockEmployerRepository
                .Setup(r => r.UpdateAsync(employer, orgId, subscription))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _employerCommandHandler.UpdateEmployerAsync(employer, orgId, subscription);

            // Assert
            _mockEmployerRepository.Verify(r => r.UpdateAsync(employer, orgId, subscription), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteEmployerAsync_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var employerId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockEmployerRepository
                .Setup(r => r.DeleteByIdAsync(employerId, orgId, subscription))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _employerCommandHandler.DeleteEmployerAsync(employerId, orgId, subscription);

            // Assert
            _mockEmployerRepository.Verify(r => r.DeleteByIdAsync(employerId, orgId, subscription), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }
    }
}
