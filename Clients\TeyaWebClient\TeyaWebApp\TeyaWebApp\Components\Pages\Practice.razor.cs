using Microsoft.AspNetCore.Components;
using Microsoft.CognitiveServices.Speech.Transcription;
using Microsoft.Extensions.Logging;
using MudBlazor;
using Syncfusion.Blazor;
using Syncfusion.Blazor.DropDowns;
using Syncfusion.Blazor.Grids;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Services;
using TeyaWebApp.ViewModel;
using Unity;

namespace TeyaWebApp.Components.Pages
{
    public partial class Practice : ComponentBase
    {
        private string searchString;
        private MudDialog _addTaskDialog;
        private Tasks newTask;
        private Tasks newVal;
        private List<Organization> organization;
        private Tasks Task;
        private Tasks currentTask;
        private List<Tasks> filteredTasks;
        private List<ProviderPatient> filteredProvider;
        private List<Tasks> TotalTasks;
        private List<ProviderPatient> AllProvider;
        private List<ProviderPatient> AllPatient;
        private List<ProviderPatient> filteredUsers;
        private string selectedMember;
        private string selectedPatientId;
        private bool isDialogOpen = false;
        private bool Subscription = false;
        [Inject] private IDialogService DialogService { get; set; }
        private string searchTerm { get; set; }
        private Guid OrgID { get; set; }

        private TemplateData ProviderData;
        private Guid activeUser;
        private Member member;
        private bool showNoResultsMessage;
        private const int MaxLength = 50;
        [CascadingParameter] MudDialogInstance MudDialog { get; set; } = default!;

        [Inject] private ActiveUser User { get; set; }
        [Inject] private ILogger<Practice> Logger { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }

        public Practice()
        {
            newTask = new Tasks();
            newVal = new Tasks();
            Task = new Tasks();
            currentTask = new Tasks();
            organization = new List<Organization>();
            filteredTasks = new List<Tasks>();
            filteredProvider = new List<ProviderPatient>();
            TotalTasks = new List<Tasks>();
            AllProvider = new List<ProviderPatient>();
            AllPatient = new List<ProviderPatient>();
            filteredUsers = new List<ProviderPatient>();
            ProviderData = new TemplateData();
            member = new Member();
            showNoResultsMessage = false;
        }
        protected override async Task OnInitializedAsync()
        {
            try
            {
                if (Guid.TryParse(User.id, out activeUser))
                {

                    organization = await OrganizationService.GetOrganizationsByNameAsync(User.OrganizationName);

                    var activeUserLicense = await UserLicenseService.GetUserLicenseByOrganizationIdAsync(organization[0].OrganizationId);
                    var planType = await PlanTypeService.GetPlanTypeByIdAsync(activeUserLicense.PlanId);
                    Subscription = planType.PlanName == "Enterprise";
                    AllProvider = await MemberService.GetProviderPatientByOrganizationIdAsync(organization[0].OrganizationId, Subscription);
                    AllPatient = await MemberService.GetPatientsByOrganizationIdAsync(organization[0].OrganizationId, Subscription);

                    OrgID = (organization[0].OrganizationId);
                    TotalTasks = await PracticeService.GetTasksAsync();
                    TotalTasks = TotalTasks.Where(task => AllProvider.Any(provider => provider.SSN == task.SSN)).ToList();
                    filteredTasks = TotalTasks;
                    await InvokeAsync(StateHasChanged);
                    StateHasChanged();
                }
                else
                {
                    Logger.LogError("Invalid user ID: " + User.id);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError($"Error fetching data: {ex.Message}");
            }
        }

        private async Task<IEnumerable<string>> SearchProvider(string searchTerm, CancellationToken cancellationToken)
        {
            if (searchTerm == null || string.IsNullOrWhiteSpace(searchTerm))
            {
                filteredTasks = TotalTasks;
                selectedMember = null;
                showNoResultsMessage = false;
                StateHasChanged();
                return new List<string> { };
            }
            else
            {
                var filtered = AllProvider
                            .Where(temp => temp.Username != null && temp.SSN != null &&
                                       temp.Username.Contains(searchTerm, StringComparison.InvariantCultureIgnoreCase))
                            .Select(temp => temp.Username + " " + temp.SSN.ToString())
                            .ToList();

                if (!filtered.Any())
                {
                    showNoResultsMessage = true;
                    return new List<string> { "No Provider Found" }; 
                }

                showNoResultsMessage = false;
                return filtered;
            }
        }

        private void CloseDialog()
        {
            isDialogOpen = false;
        }
        public SfGrid<Tasks> taskGrid { get; set; }

        private async Task<IEnumerable<string>> SearchTasks(string searchTerm, CancellationToken cancellationToken)
        {
            if (searchTerm == null || string.IsNullOrWhiteSpace(searchTerm))
            {
                StateHasChanged();
                return new List<string> { };
            }
            else
            {
                var filteredPatientNames = AllPatient
                                            .Where(temp => temp.Username != null && temp.SSN != null &&
                                                           temp.Username.Contains(searchTerm, StringComparison.InvariantCultureIgnoreCase))
                                            .Select(temp => temp.Username + " " + temp.SSN.ToString())
                                            .ToList();

                if (!filteredPatientNames.Any())
                {
                    showNoResultsMessage = true;
                    return new List<string> { "No Patient Found" };
                }

                showNoResultsMessage = false;
                return filteredPatientNames;
            }
        }
        private void OnProviderSelected(string providerPatient)
        {
            selectedMember = providerPatient;
            FilterPatientsByProvider();
            StateHasChanged();
        }

        private void OnTasksSelected(string SSN)
        {
            selectedPatientId = SSN;
            StateHasChanged();
        }
        private List<string> ValidateTask(Tasks task)
        {
            var errors = new List<string>();
            DateTime currentDate = DateTime.Now;
            if (string.IsNullOrWhiteSpace(task.PatientName) || task.PatientName.Length > MaxLength || task.PatientName.Any(char.IsDigit))
            {
                errors.Add(Localizer["PatientName must not be empty, exceed MaxLength characters, or contain numeric values."]);
            }
            if (string.IsNullOrWhiteSpace(task.SSN))
            {
                errors.Add(Localizer["SSN must not be empty, exceed MaxLength characters, or contain numeric values."]);
            }
            if (string.IsNullOrWhiteSpace(task.TaskType) || task.TaskType.Length > MaxLength || task.TaskType.Any(char.IsDigit))
            {
                errors.Add(Localizer["TaskType must not be empty."]);
            }
            if (string.IsNullOrWhiteSpace(task.Subject) || task.Subject.Length > MaxLength || task.Subject.Any(char.IsDigit))
            {
                errors.Add(Localizer["Subject must not be empty."]);
            }
            if (string.IsNullOrWhiteSpace(task.AssignedTo) || task.AssignedTo.Length > MaxLength || task.AssignedTo.Any(char.IsDigit))
            {
                errors.Add(Localizer["AssignedTo must not be empty, exceed MaxLength characters, or contain numeric values."]);
            }
            if (string.IsNullOrWhiteSpace(task.Status) || task.Status.Length > MaxLength || task.Status.Any(char.IsDigit))
            {
                errors.Add(Localizer["Status must not be empty."]);
            }
            if (string.IsNullOrWhiteSpace(task.Priority) || task.Priority.Length > MaxLength || task.Priority.Any(char.IsDigit))
            {
                errors.Add(Localizer["Priority must not be empty."]);
            }
            if (string.IsNullOrWhiteSpace(task.CreatedBy) || task.CreatedBy.Length > MaxLength || task.CreatedBy.Any(char.IsDigit))
            {
                errors.Add(Localizer["CreatedBy must not be empty."]);
            }
            if (string.IsNullOrWhiteSpace(task.Notes) || task.Notes.Length > MaxLength || task.Notes.Any(char.IsDigit))
            {
                errors.Add(Localizer["Notes must not be empty."]);
            }
            if (task.CreationDate == null || task.CreationDate != DateTime.Now.Date)
            {
                errors.Add(Localizer["Creation Date must be a valid date."]);
            }
            if (task.StartDate == null || task.StartDate > DateTime.Now.Date)
            {
                errors.Add(Localizer["Start Date must be a valid date."]);
            }
            if (task.DueDate == null || task.DueDate <= DateTime.Now.Date)
            {
                errors.Add(Localizer["Due Date must be a valid date."]);
            }
            if (task.LastDueDate == null || task.LastDueDate <= DateTime.Now.Date)
            {
                errors.Add(Localizer["Last Due Date must be a valid date."]);
            }
            if (task.LastVisitDate == null || task.LastVisitDate >= DateTime.Now.Date)
            {
                errors.Add(Localizer["Last Visit Date must be a valid date."]);
            }
            return errors;
        }
        private async Task ShowTaskDialog(Tasks task)
        {
            currentTask = task;
            isDialogOpen = true;
            //var parameters = new DialogParameters { ["Tasks"] = task };
            //var options = new DialogOptions { MaxWidth = MaxWidth.Medium, FullWidth = true };
            //await DialogService.Show<Practice>("Task Details", parameters, options).Result;
        }
        private async void FilterPatientsByProvider()
        {
            if (selectedMember == null || string.IsNullOrWhiteSpace(selectedMember))
            {
                filteredTasks = TotalTasks;
            }
            else
            {
                string[] parts = selectedMember.Split(' ');
                filteredTasks = TotalTasks;
                filteredTasks = filteredTasks.Where(task => task.SSN == parts[parts.Length-1]).ToList();
            }
            await InvokeAsync(StateHasChanged);
            StateHasChanged();
        }

        private void OpenAddTaskDialog()
        {
            newTask = new Tasks();
            newTask.CreatedBy = User.displayName;
            _addTaskDialog.ShowAsync();
        }

        private void CloseAddTaskDialog()
        {
            _addTaskDialog.CloseAsync();
        }

        private void CancelChanges()
        {
            CloseAddTaskDialog();
            selectedPatientId = null;
            StateHasChanged();
        }

        public async void ActionBeginHandler(Syncfusion.Blazor.Grids.ActionEventArgs<Tasks> Args)
        {
            if (Args.RequestType.Equals(Syncfusion.Blazor.Grids.Action.Save))
            {
                var errors = ValidateGrid(Args.Data);
                if (errors.Count > 0)
                {
                    return;
                }
                await PracticeService.UpdateTasksAsync(Args.Data, OrgID, false);
            }
            if (Args.RequestType.Equals(Syncfusion.Blazor.Grids.Action.Delete))
            {
                bool? result = await DialogService.ShowMessageBox(
                      @Localizer["Confirm Delete"],
                      @Localizer["Do you want to Delete this entry?"],
                      yesText: @Localizer["Yes"],
                      noText: @Localizer["No"]);

                if (result != true)
                {
                    return;
                }
                await PracticeService.DeleteTasksAsync(Args.Data.Id, OrgID, false);
            }
            if (Args.RequestType.Equals(Syncfusion.Blazor.Grids.Action.Save) || Args.RequestType.Equals(Syncfusion.Blazor.Grids.Action.Delete))
            {
                TotalTasks = await PracticeService.GetTasksAsync();
                TotalTasks = TotalTasks.Where(task => AllProvider.Any(provider => provider.SSN == task.SSN)).ToList();
                filteredTasks = TotalTasks;
            }
            StateHasChanged();
            await InvokeAsync(StateHasChanged);
        }

        public class RecurringAction
        {
            public bool Name { get; set; }
            public string Code { get; set; }
        }

        List<RecurringAction> recurringActions = new List<RecurringAction>
    {
        new RecurringAction() { Name = true, Code = "Yes" },
        new RecurringAction() { Name = false, Code = "No" },
    };
        private async Task SaveTaskAsync()
        {
            if (selectedPatientId == null || string.IsNullOrWhiteSpace(selectedPatientId))
            {
                Snackbar.Add(@Localizer["Please enter a patient name!! "], Severity.Warning);
                return;
            }
            else
            {
                string[] parts = selectedPatientId.Split(' ');

                var selectedPatientPCPId = AllPatient
                                        .Where(temp => temp.Id != null &&
                                                    temp.SSN == parts[parts.Length-1])
                                                    .Select(temp => temp.PCPId)
                                                    .FirstOrDefault();

                var selectedProviderSSN = AllProvider
                                        .Where(temp => temp.Id != null &&
                                                    temp.Id == selectedPatientPCPId)
                                                    .Select(temp => temp.SSN)
                                                    .FirstOrDefault();
                newTask.Id = Guid.NewGuid();
                newTask.PatientName = parts[0];
                newTask.SSN = selectedProviderSSN;
                newTask.OrganizationId = OrgID;

                var errors = ValidateTask(newTask);
                if (errors.Count > 0)
                {
                    Snackbar.Add(@Localizer[errors[0]], Severity.Warning);
                    return;
                }

                await PracticeService.CreateTasksAsync(new List<Tasks> { newTask }, OrgID, false);
                TotalTasks = await PracticeService.GetTasksAsync();
                TotalTasks = TotalTasks.Where(task => AllProvider.Any(provider => provider.SSN == task.SSN)).ToList();
                filteredTasks = TotalTasks;
                selectedPatientId = null;
                await InvokeAsync(StateHasChanged);
                StateHasChanged();
                CloseAddTaskDialog();
            }
        }

        private List<string> ValidateGrid(Tasks task)
        {
            var errors = new List<string>();
            DateTime currentDate = DateTime.Now;

            if (string.IsNullOrWhiteSpace(task.PatientName) || task.PatientName.Length > MaxLength || task.PatientName.Any(char.IsDigit))
            {
                errors.Add(Localizer["PatientName must not be empty, exceed MaxLength characters, or contain numeric values."]);
            }
            if (string.IsNullOrWhiteSpace(task.AssignedTo) || task.AssignedTo.Length > MaxLength || task.AssignedTo.Any(char.IsDigit))
            {
                errors.Add(Localizer["AssignedTo must not be empty, exceed MaxLength characters, or contain numeric values."]);
            }
            if (string.IsNullOrWhiteSpace(task.Subject) || task.Subject.Length > MaxLength || task.Subject.Any(char.IsDigit))
            {
                errors.Add(Localizer["Subject must not be empty."]);
            }
            if (string.IsNullOrWhiteSpace(task.TaskType) || task.TaskType.Length > MaxLength || task.TaskType.Any(char.IsDigit))
            {
                errors.Add(Localizer["TaskType must not be empty."]);
            }
            if (string.IsNullOrWhiteSpace(task.Status) || task.Status.Length > MaxLength || task.Status.Any(char.IsDigit))
            {
                errors.Add(Localizer["Status must not be empty."]);
            }
            if (task.DueDate == null)
            {
                errors.Add(Localizer["DueDateValidation"]);
            }
            return errors;
        }
    }
}
   