using NUnit.Framework;
using Moq;
using MemberServiceBusinessLayer.CommandHandler;
using MemberServiceDataAccessLayer.Implementation;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace MemberServiceApiBusinessLayerTestCases.CommandHandlerTests
{
    [TestFixture]
    public class UserThemeCommandHandlerTests
    {
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<IUserThemeRepository> _mockUserThemeRepository;
        private Mock<ILogger<UserThemeCommandHandler>> _mockLogger;
        private UserThemeCommandHandler _userThemeCommandHandler;

        [SetUp]
        public void Setup()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockUserThemeRepository = new Mock<IUserThemeRepository>();
            _mockLogger = new Mock<ILogger<UserThemeCommandHandler>>();

            _mockUnitOfWork.Setup(u => u.UserThemeRepository).Returns(_mockUserThemeRepository.Object);

            _userThemeCommandHandler = new UserThemeCommandHandler(
                _mockUnitOfWork.Object,
                _mockLogger.Object
            );
        }

        [Test]
        public async Task AddUserTheme_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var userTheme = new UserTheme 
            { 
                UserId = userId, 
                ThemeName = "Dark Theme",
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockUserThemeRepository
                .Setup(r => r.AddAsync(It.IsAny<List<UserTheme>>(), orgId, subscription))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _userThemeCommandHandler.AddUserTheme(userTheme, orgId, subscription);

            // Assert
            _mockUserThemeRepository.Verify(r => r.AddAsync(It.Is<List<UserTheme>>(list => 
                list.Count == 1 && list[0] == userTheme), orgId, subscription), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateUserTheme_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var userTheme = new UserTheme 
            { 
                UserId = userId, 
                ThemeName = "Light Theme",
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockUserThemeRepository
                .Setup(r => r.UpdateAsync(userTheme, orgId, subscription))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _userThemeCommandHandler.UpdateUserTheme(userTheme, orgId, subscription);

            // Assert
            _mockUserThemeRepository.Verify(r => r.UpdateAsync(userTheme, orgId, subscription), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteUserThemeById_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var userId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockUserThemeRepository
                .Setup(r => r.DeleteByIdAsync(userId, orgId, subscription))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _userThemeCommandHandler.DeleteUserThemeById(userId, orgId, subscription);

            // Assert
            _mockUserThemeRepository.Verify(r => r.DeleteByIdAsync(userId, orgId, subscription), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }
    }
}
