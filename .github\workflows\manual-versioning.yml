name: Manual Versioning

on:
  workflow_dispatch:
    inputs:
      version_type:
        description: 'Type of versioning to perform'
        required: true
        default: 'auto'
        type: choice
        options:
          - auto
          - major
          - minor
          - patch
      target_projects:
        description: 'Specific projects to version (comma-separated paths, leave empty for all)'
        required: false
        type: string
      custom_version:
        description: 'Custom version (e.g., 1.2.3, leave empty for auto-generated)'
        required: false
        type: string

permissions:
  id-token: write
  contents: write
  actions: read
  pull-requests: write

jobs:
  manual-versioning:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Generate version
        id: version
        run: |
          if [ -n "${{ github.event.inputs.custom_version }}" ]; then
            VERSION="${{ github.event.inputs.custom_version }}"
            echo "Using custom version: $VERSION"
          else
            case "${{ github.event.inputs.version_type }}" in
              "major")
                VERSION="v$(date +'%Y').0.0"
                ;;
              "minor")
                VERSION="v$(date +'%Y.%m').0"
                ;;
              "patch")
                VERSION="v$(date +'%Y.%m.%d')"
                ;;
              *)
                VERSION="v$(date +'%Y.%m.%d.%H%M')"
                ;;
            esac
            echo "Generated version: $VERSION"
          fi
          
          TICKET_NUMBER=$(date +'%Y%m%d%H%M')
          echo "VERSION=$VERSION" >> $GITHUB_OUTPUT
          echo "TICKET_NUMBER=$TICKET_NUMBER" >> $GITHUB_OUTPUT

      - name: Determine target projects
        id: projects
        run: |
          if [ -n "${{ github.event.inputs.target_projects }}" ]; then
            # Use specified projects
            PROJECTS=$(echo "${{ github.event.inputs.target_projects }}" | tr ',' ' ')
            echo "Using specified projects: $PROJECTS"
          else
            # Find all main projects
            PROJECTS=$(find . -name "*.csproj" -path "*/Services/*" -o -path "*/Clients/*" -o -path "*/Infrastructure/*" | grep -v Test | head -20)
            echo "Using all main projects"
          fi
          
          echo "projects=$PROJECTS" >> $GITHUB_OUTPUT
          echo "Projects to version: $PROJECTS"

      - name: Update project versions
        run: |
          VERSION=${{ steps.version.outputs.VERSION }}
          PROJECTS="${{ steps.projects.outputs.projects }}"
          
          echo "Setting version $VERSION in specified .csproj files..."
          
          for csproj in $PROJECTS; do
            if [ -f "$csproj" ]; then
              echo "Updating $csproj"
              
              # Check if Version element exists
              if grep -q "<Version>" "$csproj"; then
                # Update existing Version element
                sed -i "s|<Version>.*</Version>|<Version>$VERSION</Version>|" "$csproj"
              else
                # Add Version element to the first PropertyGroup
                sed -i "0,/<\/PropertyGroup>/s|<\/PropertyGroup>|    <Version>$VERSION</Version>\n  </PropertyGroup>|" "$csproj"
              fi
              
              echo "✅ Updated $csproj with version $VERSION"
            else
              echo "⚠️ Warning: $csproj not found"
            fi
          done

      - name: Create Pull Request
        uses: peter-evans/create-pull-request@v5
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          commit-message: "ci: manual version bump to ${{ steps.version.outputs.VERSION }}"
          branch: "DEV-${{ steps.version.outputs.TICKET_NUMBER }}-manual-version-bump"
          base: main
          title: "🔖 Manual Version Bump to ${{ steps.version.outputs.VERSION }}"
          body: |
            ## Manual Version Bump
            
            This PR contains a manual version bump to `${{ steps.version.outputs.VERSION }}`.
            
            ### Configuration:
            - **Version Type**: ${{ github.event.inputs.version_type }}
            - **Custom Version**: ${{ github.event.inputs.custom_version || 'N/A' }}
            - **Target Projects**: ${{ github.event.inputs.target_projects || 'All main projects' }}
            
            ### Projects Updated:
            ```
            ${{ steps.projects.outputs.projects }}
            ```
            
            ### Triggered By:
            - **Actor**: ${{ github.actor }}
            - **Workflow**: Manual Versioning
            - **Timestamp**: $(date -u)
            
            ---
            
            ⚠️ **Note**: This is a manually triggered versioning PR. Please review the changes before merging.
          labels: |
            manual
            version-bump
            ci/cd
          assignees: ${{ github.actor }}

      - name: Notify MS Teams
        run: |
          curl -H "Content-Type: application/json" \
          -d '{
            "text": "🔖 Manual versioning PR created!\n- Version: ${{ steps.version.outputs.VERSION }}\n- Type: ${{ github.event.inputs.version_type }}\n- Branch: DEV-${{ steps.version.outputs.TICKET_NUMBER }}-manual-version-bump\n- Projects: ${{ steps.projects.outputs.projects }}\n- Triggered by: ${{ github.actor }}"
          }' \
          ${{ secrets.TEAMS_WEBHOOK_URL }}
