using NUnit.Framework;
using NUnit.Framework.Legacy;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using MemberServiceApi.Controllers;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using MemberServiceBusinessLayer;

namespace MemberServiceApiControllerTestCases
{
    [TestFixture]
    public class AddressControllerTests
    {
        private Mock<IAddressesQueryHandler<Address>> _mockQueryHandler = null!;
        private Mock<IAddressesCommandHandler<Address>> _mockCommandHandler = null!;
        private Mock<ILogger<AddressController>> _mockLogger = null!;
        private Mock<IStringLocalizer<AddressController>> _mockLocalizer = null!;
        private AddressController _controller = null!;

        [SetUp]
        public void Setup()
        {
            _mockQueryHandler = new Mock<IAddressesQueryHandler<Address>>();
            _mockCommandHandler = new Mock<IAddressesCommandHandler<Address>>();
            _mockLogger = new Mock<ILogger<AddressController>>();
            _mockLocalizer = new Mock<IStringLocalizer<AddressController>>();

            // Setup localizer to return the key when requested
            _mockLocalizer.Setup(l => l[It.IsAny<string>()]).Returns((string key) => new LocalizedString(key, key));

            _controller = new AddressController(
                _mockQueryHandler.Object,
                _mockCommandHandler.Object,
                _mockLogger.Object,
                _mockLocalizer.Object
            );
        }

        [Test]
        public async Task GetAllAddresses_WhenAddressesExist_ReturnsOkWithAddresses()
        {
            // Arrange
            var mockAddresses = new List<Address>
            {
                new Address { AddressId = Guid.NewGuid(), AddressLine1 = "123 Main St", City = "Anytown", State = "CA" },
                new Address { AddressId = Guid.NewGuid(), AddressLine1 = "456 Oak Ave", City = "Somewhere", State = "NY" }
            };

            _mockQueryHandler
                .Setup(q => q.GetAllAddressesAsync())
                .ReturnsAsync(mockAddresses);

            // Act
            var result = await _controller.GetAllAddresses();

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult?.StatusCode, Is.EqualTo(200));
            Assert.That(okResult?.Value, Is.EqualTo(mockAddresses));
        }

        [Test]
        public async Task GetAllAddresses_WhenNoAddressesExist_ReturnsNotFound()
        {
            // Arrange
            _mockQueryHandler
                .Setup(q => q.GetAllAddressesAsync())
                .ReturnsAsync(new List<Address>());

            // Act
            var result = await _controller.GetAllAddresses();

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task GetAllAddresses_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            _mockQueryHandler
                .Setup(q => q.GetAllAddressesAsync())
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetAllAddresses();

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task GetAddressById_WhenAddressExists_ReturnsOkWithAddress()
        {
            // Arrange
            var addressId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var mockAddress = new Address
            {
                AddressId = addressId,
                AddressLine1 = "123 Main St",
                City = "Anytown",
                State = "CA",
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockQueryHandler
                .Setup(q => q.GetAddressByIdAsync(addressId, orgId, subscription))
                .ReturnsAsync(mockAddress);

            // Act
            var result = await _controller.GetAddressById(addressId, orgId, subscription);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult.StatusCode, Is.EqualTo(200));
            Assert.That(okResult.Value, Is.EqualTo(mockAddress));
        }

        [Test]
        public async Task GetAddressById_WhenAddressDoesNotExist_ReturnsNotFound()
        {
            // Arrange
            var addressId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockQueryHandler
                .Setup(q => q.GetAddressByIdAsync(addressId, orgId, subscription))
                .ReturnsAsync((Address)null);

            // Act
            var result = await _controller.GetAddressById(addressId, orgId, subscription);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task AddAddress_WhenValidAddress_ReturnsCreatedAtAction()
        {
            // Arrange
            var addressId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var address = new Address
            {
                AddressId = addressId,
                AddressLine1 = "123 Main St",
                City = "Anytown",
                State = "CA",
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockCommandHandler
                .Setup(c => c.AddAddressAsync(It.IsAny<List<Address>>(), subscription, orgId))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.AddAddress(address);

            // Assert
            Assert.That(result, Is.InstanceOf<CreatedAtActionResult>());
            var createdResult = result as CreatedAtActionResult;
            Assert.That(createdResult.StatusCode, Is.EqualTo(201));
            Assert.That(createdResult.ActionName, Is.EqualTo(nameof(AddressController.GetAddressById)));
            Assert.That(createdResult.RouteValues["id"], Is.EqualTo(addressId));
            Assert.That(createdResult.Value, Is.Not.Null);

            // The controller returns an anonymous object with specific fields, not the full Address object
            var returnedValue = createdResult.Value;
            var addressIdProperty = returnedValue.GetType().GetProperty("AddressId");
            var returnedAddressId = addressIdProperty?.GetValue(returnedValue);
            Assert.That(returnedAddressId, Is.EqualTo(address.AddressId));
        }

        [Test]
        public async Task AddAddress_WhenNullAddress_ReturnsBadRequest()
        {
            // Act
            var result = await _controller.AddAddress(null);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult.StatusCode, Is.EqualTo(400));
        }

        [Test]
        public async Task AddAddress_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var address = new Address
            {
                AddressId = Guid.NewGuid(),
                AddressLine1 = "123 Main St",
                City = "Anytown",
                State = "CA"
            };

            _mockCommandHandler
                .Setup(c => c.AddAddressAsync(It.IsAny<List<Address>>(), It.IsAny<bool>(), It.IsAny<Guid>()))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.AddAddress(address);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task DeleteAddress_WhenAddressExists_ReturnsNoContent()
        {
            // Arrange
            var addressId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockCommandHandler
                .Setup(c => c.DeleteAddressAsync(addressId, subscription, orgId))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.DeleteAddress(addressId, orgId, subscription);

            // Assert
            Assert.That(result, Is.InstanceOf<NoContentResult>());
            var noContentResult = result as NoContentResult;
            Assert.That(noContentResult.StatusCode, Is.EqualTo(204));
        }

        [Test]
        public async Task DeleteAddress_WhenAddressDoesNotExist_ReturnsNotFound()
        {
            // Arrange
            var addressId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockCommandHandler
                .Setup(c => c.DeleteAddressAsync(addressId, subscription, orgId))
                .ThrowsAsync(new KeyNotFoundException());

            // Act
            var result = await _controller.DeleteAddress(addressId, orgId, subscription);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task DeleteAddress_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var addressId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockCommandHandler
                .Setup(c => c.DeleteAddressAsync(addressId, subscription, orgId))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.DeleteAddress(addressId, orgId, subscription);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task UpdateAddress_WhenValidAddress_ReturnsNoContent()
        {
            // Arrange
            var addressId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var address = new Address
            {
                AddressId = addressId,
                AddressLine1 = "123 Main St",
                City = "Anytown",
                State = "CA",
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockCommandHandler
                .Setup(c => c.UpdateAddressAsync(address, subscription, orgId))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.UpdateAddress(addressId, address);

            // Assert
            Assert.That(result, Is.InstanceOf<NoContentResult>());
            var noContentResult = result as NoContentResult;
            Assert.That(noContentResult?.StatusCode, Is.EqualTo(204));
        }

        [Test]
        public async Task UpdateAddress_WhenNullAddress_ReturnsBadRequest()
        {
            // Arrange
            var addressId = Guid.NewGuid();

            // Act
            var result = await _controller.UpdateAddress(addressId, null!);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult?.StatusCode, Is.EqualTo(400));
        }

        [Test]
        public async Task UpdateAddress_WhenIdMismatch_ReturnsBadRequest()
        {
            // Arrange
            var addressId = Guid.NewGuid();
            var differentId = Guid.NewGuid();
            var address = new Address
            {
                AddressId = differentId,
                AddressLine1 = "123 Main St",
                City = "Anytown",
                State = "CA"
            };

            // Act
            var result = await _controller.UpdateAddress(addressId, address);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult?.StatusCode, Is.EqualTo(400));
        }

        [Test]
        public async Task UpdateAddress_WhenAddressDoesNotExist_ReturnsNotFound()
        {
            // Arrange
            var addressId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var address = new Address
            {
                AddressId = addressId,
                AddressLine1 = "123 Main St",
                City = "Anytown",
                State = "CA",
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockCommandHandler
                .Setup(c => c.UpdateAddressAsync(address, subscription, orgId))
                .ThrowsAsync(new KeyNotFoundException());

            // Act
            var result = await _controller.UpdateAddress(addressId, address);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult?.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task UpdateAddress_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var addressId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var address = new Address
            {
                AddressId = addressId,
                AddressLine1 = "123 Main St",
                City = "Anytown",
                State = "CA",
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockCommandHandler
                .Setup(c => c.UpdateAddressAsync(address, subscription, orgId))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.UpdateAddress(addressId, address);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }
    }
}
