﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Contracts;
using DataAccessLayer.Context;
using Interfaces.ShardManagement;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;

namespace MemberServiceDataAccessLayer.Implementation
{
    /// <summary>
    /// Repository implementation for handling Roleslist-related database operations.
    /// </summary>
    public class RoleslistRepository : GenericRepository<Rolesdata>, IRoleslistRepository
    {
        private readonly AccountDatabaseContext _context;

        /// <summary>
        /// Initializes a new instance of the <see cref="RoleslistRepository"/> class.
        /// </summary>
        /// <param name="context">Database context for Roleslist-related operations.</param>
        public RoleslistRepository(AccountDatabaseContext context) : base(context)
        {
            _context = context;
        }
    }
}
