using NUnit.Framework;
using Moq;
using MemberServiceBusinessLayer.QueryHandler;
using MemberServiceDataAccessLayer.Implementation;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;

namespace MemberServiceApiBusinessLayerTestCases.QueryHandlerTests
{
    [TestFixture]
    public class GuardianQueryHandlerTests
    {
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<IGuardianRepository> _mockGuardianRepository;
        private GuardianQueryHandler _guardianQueryHandler;

        [SetUp]
        public void Setup()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockGuardianRepository = new Mock<IGuardianRepository>();

            _mockUnitOfWork.Setup(u => u.GuardianRepository).Returns(_mockGuardianRepository.Object);

            _guardianQueryHandler = new GuardianQueryHandler(_mockUnitOfWork.Object);
        }

        [Test]
        public async Task GetGuardianByIdAsync_ShouldReturnGuardian_WhenGuardianExists()
        {
            // Arrange
            var guardianId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var expectedGuardian = new Guardian 
            { 
                GuardianId = guardianId, 
                GuardianName = "John Doe",
                GuardianRelationship = "Parent",
                GuardianSex = "Male",
                GuardianCity = "Anytown",
                GuardianState = "CA",
                GuardianCountry = "USA",
                GuardianPhone = "************",
                GuardianEmail = "<EMAIL>",
                GuardianAddress = "123 Main St",
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockGuardianRepository
                .Setup(r => r.GetByIdAsync(guardianId, orgId, subscription))
                .ReturnsAsync(expectedGuardian);

            // Act
            var result = await _guardianQueryHandler.GetGuardianByIdAsync(guardianId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.GuardianId, Is.EqualTo(guardianId));
            Assert.That(result.GuardianName, Is.EqualTo("John Doe"));
            Assert.That(result.GuardianRelationship, Is.EqualTo("Parent"));
            _mockGuardianRepository.Verify(r => r.GetByIdAsync(guardianId, orgId, subscription), Times.Once);
        }

        [Test]
        public async Task GetGuardianByIdAsync_ShouldReturnNull_WhenGuardianDoesNotExist()
        {
            // Arrange
            var guardianId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockGuardianRepository
                .Setup(r => r.GetByIdAsync(guardianId, orgId, subscription))
                .ReturnsAsync((Guardian)null);

            // Act
            var result = await _guardianQueryHandler.GetGuardianByIdAsync(guardianId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Null);
            _mockGuardianRepository.Verify(r => r.GetByIdAsync(guardianId, orgId, subscription), Times.Once);
        }

        [Test]
        public async Task GetAllGuardianAsync_ShouldReturnAllGuardians()
        {
            // Arrange
            var expectedGuardians = new List<Guardian>
            {
                new Guardian 
                { 
                    GuardianId = Guid.NewGuid(), 
                    GuardianName = "John Doe",
                    GuardianRelationship = "Parent"
                },
                new Guardian 
                { 
                    GuardianId = Guid.NewGuid(), 
                    GuardianName = "Jane Smith",
                    GuardianRelationship = "Aunt"
                }
            };

            _mockGuardianRepository.Setup(r => r.GetAllAsync()).ReturnsAsync(expectedGuardians);

            // Act
            var result = await _guardianQueryHandler.GetAllGuardianAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result, Is.EquivalentTo(expectedGuardians));
            _mockGuardianRepository.Verify(r => r.GetAllAsync(), Times.Once);
        }

        [Test]
        public void Constructor_ShouldThrowArgumentNullException_WhenUnitOfWorkIsNull()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new GuardianQueryHandler(null));
        }
    }
}
