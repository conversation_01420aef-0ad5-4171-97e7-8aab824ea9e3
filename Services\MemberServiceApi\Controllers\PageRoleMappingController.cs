using Contracts;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MemberServiceBusinessLayer;

namespace MemberServiceApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class PageRoleMappingController : ControllerBase
    {
        private readonly IPageRoleMappingQueryHandler<PageRoleMapping> _pageRoleMappingQueryHandler;
        private readonly IPageRoleMappingCommandHandler<PageRoleMapping> _pageRoleMappingCommandHandler;
        private readonly ILogger<PageRoleMappingController> _logger;
        private readonly IStringLocalizer<PageRoleMappingController> _localizer;

        public PageRoleMappingController(
            IPageRoleMappingQueryHandler<PageRoleMapping> pageRoleMappingQueryHandler,
            IPageRoleMappingCommandHandler<PageRoleMapping> pageRoleMappingCommandHandler,
            ILogger<PageRoleMappingController> logger,
            IStringLocalizer<PageRoleMappingController> localizer)
        {
            _pageRoleMappingQueryHandler = pageRoleMappingQueryHandler ?? throw new ArgumentNullException(nameof(pageRoleMappingQueryHandler));
            _pageRoleMappingCommandHandler = pageRoleMappingCommandHandler ?? throw new ArgumentNullException(nameof(pageRoleMappingCommandHandler));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _localizer = localizer ?? throw new ArgumentNullException(nameof(localizer));
        }

        /// <summary>
        /// Gets the PageRoleMapping by ID.
        /// </summary>
        /// <param name="id">The ID of the PageRoleMapping.</param>
        /// <returns>The PageRoleMapping with the specified ID.</returns>
        [HttpGet("{id:Guid}/{OrgID:Guid}/{Subscription:bool}")]
        public async Task<IActionResult> GetPageRoleMappingById(Guid id, Guid OrgID, bool Subscription)
        {
            IActionResult response;
            try
            {
                _logger.LogInformation(_localizer["FetchingPageRoleMappingWithID"], id);
                var pageRoleMapping = await _pageRoleMappingQueryHandler.GetPageRoleMappingByIdAsync(id, OrgID, Subscription);
                if (pageRoleMapping == null)
                {
                    _logger.LogWarning(_localizer["PageRoleMappingNotFound"], id);
                    response = NotFound(_localizer["PageRoleMappingNotFoundMessage"]);
                }
                else
                {
                    _logger.LogInformation(_localizer["PageRoleMappingFetchedSuccessfully"], id);
                    response = Ok(pageRoleMapping);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingPageRoleMapping"], id);
                response = StatusCode(500, _localizer["InternalServerErrorMessage"]);
            }
            return response;
        }

        /// <summary>
        /// Gets the pages by Role ID.
        /// </summary>
        /// <param name="RoleId">The Role ID.</param>
        /// <returns>The pages associated with the specified Role ID.</returns>
        [HttpGet("role/{RoleId}/{OrgID:Guid}/{Subscription:bool}")]
        public async Task<IActionResult> GetPagesByRoleId(Guid RoleId, Guid OrgID, bool Subscription)
        {
            IActionResult response;
            try
            {
                _logger.LogInformation(_localizer["FetchingPagesWithRoleID"], RoleId);
                var pages = await _pageRoleMappingQueryHandler.GetPagesByRoleIdAsync(RoleId, OrgID, Subscription);
                if (pages == null || pages.Count == 0)
                {
                    _logger.LogWarning(_localizer["PagesNotFound"], RoleId);
                    response = NotFound(_localizer["PagesNotFoundMessage"]);
                }
                else
                {
                    _logger.LogInformation(_localizer["PagesFetchedSuccessfully"], RoleId);
                    response = Ok(pages);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingPages"], RoleId);
                response = StatusCode(500, _localizer["InternalServerErrorMessage"]);
            }
            return response;
        }

        /// <summary>
        /// Adds a new PageRoleMapping.
        /// </summary>
        /// <param name="pageRoleMapping">The PageRoleMapping to add.</param>
        /// <returns>The created PageRoleMapping.</returns>
        [HttpPost]
        public async Task<IActionResult> AddPageRoleMapping([FromBody] PageRoleMapping pageRoleMapping)
        {
            IActionResult response;
            if (pageRoleMapping == null)
            {
                _logger.LogWarning(_localizer["InvalidPageRoleMappingData"]);
                response = BadRequest(_localizer["PageRoleMappingInvalidMessage"]);
            }
            else
            {
                try
                {
                    _logger.LogInformation(_localizer["AddingNewPageRoleMapping"]);
                    await _pageRoleMappingCommandHandler.AddPageRoleMappingAsync(pageRoleMapping, pageRoleMapping.OrganizationID, pageRoleMapping.Subscription);
                    _logger.LogInformation(_localizer["PageRoleMappingAddedSuccessfully"], pageRoleMapping.Id);
                    response = CreatedAtAction(nameof(GetPageRoleMappingById), new { id = pageRoleMapping.Id }, pageRoleMapping);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["ErrorAddingPageRoleMapping"]);
                    response = StatusCode(500, _localizer["InternalServerErrorMessage"]);
                }
            }
            return response;
        }

        /// <summary>
        /// Updates an existing PageRoleMapping.
        /// </summary>
        /// <param name="id">The ID of the PageRoleMapping to update.</param>
        /// <param name="pageRoleMapping">The updated PageRoleMapping.</param>
        /// <returns>No content if the update is successful.</returns>
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdatePageRoleMapping(Guid id, [FromBody] PageRoleMapping pageRoleMapping)
        {
            IActionResult response;
            if (pageRoleMapping == null || id != pageRoleMapping.Id)
            {
                _logger.LogWarning(_localizer["InvalidDataForUpdate"], id);
                response = BadRequest(_localizer["PageRoleMappingInvalidMessage"]);
            }
            else
            {
                try
                {
                    _logger.LogInformation(_localizer["UpdatingPageRoleMappingWithID"], id);
                    await _pageRoleMappingCommandHandler.UpdatePageRoleMappingAsync(pageRoleMapping, pageRoleMapping.OrganizationID, pageRoleMapping.Subscription);
                    _logger.LogInformation(_localizer["PageRoleMappingUpdatedSuccessfully"], id);
                    response = NoContent();
                }
                catch (KeyNotFoundException)
                {
                    _logger.LogWarning(_localizer["PageRoleMappingNotFoundForUpdate"], id);
                    response = NotFound(_localizer["PageRoleMappingNotFoundMessage"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["ErrorUpdatingPageRoleMapping"]);
                    response = StatusCode(500, _localizer["InternalServerErrorMessage"]);
                }
            }
            return response;
        }

        /// <summary>
        /// Deletes a PageRoleMapping by ID.
        /// </summary>
        /// <param name="id">The ID of the PageRoleMapping to delete.</param>
        /// <returns>No content if the deletion is successful.</returns>
        [HttpDelete("{id:guid}/{OrgID:Guid}/{Subscription}")]
        public async Task<IActionResult> DeletePageRoleMapping(Guid id, Guid OrgID, bool Subscription)
        {
            IActionResult response;
            try
            {
                _logger.LogInformation(_localizer["DeletingPageRoleMappingWithID"], id);
                await _pageRoleMappingCommandHandler.DeletePageRoleMappingAsync(id, OrgID, Subscription);
                _logger.LogInformation(_localizer["PageRoleMappingDeletedSuccessfully"], id);
                response = NoContent();
            }
            catch (KeyNotFoundException)
            {
                _logger.LogWarning(_localizer["PageRoleMappingNotFoundForDeletion"], id);
                response = NotFound(_localizer["PageRoleMappingNotFoundMessage"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorDeletingPageRoleMapping"]);
                response = StatusCode(500, _localizer["InternalServerErrorMessage"]);
            }
            return response;
        }

        /// <summary>
        /// Gets the PageRoleMappings by page path.
        /// </summary>
        /// <param name="pagePath">The page path.</param>
        /// <returns>The PageRoleMappings associated with the specified page path.</returns>
        [HttpGet("search/{OrgID:guid}/{Subscription}")]
        public async Task<IActionResult> GetPageRoleMappingByPagePath([FromQuery] string pagePath, Guid OrganizationId, bool Subscription)
        {
            IActionResult response;
            if (string.IsNullOrWhiteSpace(pagePath))
            {
                _logger.LogWarning(_localizer["PagePathRequired"]);
                response = BadRequest(_localizer["PagePathRequiredMessage"]);
            }
            else
            {
                try
                {
                    _logger.LogInformation(_localizer["SearchingPageRoleMappingsByPagePath"], pagePath);
                    var pageRoleMappings = await _pageRoleMappingQueryHandler.GetPageRoleMappingsByPagePathAsync(pagePath, OrganizationId, Subscription);

                    if (pageRoleMappings == null || pageRoleMappings.Count == 0)
                    {
                        _logger.LogWarning(_localizer["NoPageRoleMappingsFoundByPagePath"], pagePath);
                        response = NotFound(_localizer["PageRoleMappingsNotFoundMessage"]);
                    }
                    else
                    {
                        _logger.LogInformation(_localizer["PageRoleMappingsFetchedSuccessfully"], pagePath);
                        response = Ok(pageRoleMappings);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["ErrorSearchingPageRoleMappings"]);
                    response = StatusCode(500, _localizer["InternalServerErrorMessage"]);
                }
            }
            return response;
        }

        /// <summary>
        /// Gets all PageRoleMappings.
        /// </summary>
        /// <returns>All PageRoleMappings.</returns>
        [HttpGet]
        public async Task<IActionResult> GetAllPageRoleMappings()
        {
            IActionResult response;
            _logger.LogInformation(_localizer["FetchingAllPageRoleMappings"]);
            try
            {
                var pageRoleMappings = await _pageRoleMappingQueryHandler.GetAllPageRoleMappingsAsync();

                if (pageRoleMappings == null || pageRoleMappings.Count == 0)
                {
                    _logger.LogWarning(_localizer["NoPageRoleMappingsFound"]);
                    response = NotFound(_localizer["PageRoleMappingsNotFoundMessage"]);
                }
                else
                {
                    _logger.LogInformation(_localizer["AllPageRoleMappingsFetchedSuccessfully"]);
                    response = Ok(pageRoleMappings);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingPageRoleMappings"]);
                response = StatusCode(500, _localizer["InternalServerErrorMessage"]);
            }
            return response;
        }

        /// <summary>
        /// Gets the roles by page path.
        /// </summary>
        /// <param name="pagePath">The page path.</param>
        /// <returns>The roles associated with the specified page path.</returns>
        [HttpGet("roles-by-pagepath")]
        public async Task<IActionResult> GetRolesByPagePath([FromQuery] string pagePath, [FromQuery] Guid OrganizationId, [FromQuery] bool Subscription)
        {
            IActionResult response;
            if (string.IsNullOrWhiteSpace(pagePath))
            {
                _logger.LogWarning(_localizer["PagePathRequired"]);
                response = BadRequest(_localizer["PagePathRequiredMessage"]);
            }
            else
            {
                try
                {
                    _logger.LogInformation(_localizer["FetchingRolesByPagePath"], pagePath);
                    var roles = await _pageRoleMappingQueryHandler.GetRolesByPagePathAsync(pagePath, OrganizationId, Subscription);

                    if (roles == null || roles.Count == 0)
                    {
                        _logger.LogWarning(_localizer["RolesNotFoundForPagePath"], pagePath);
                        response = NotFound(_localizer["RolesNotFoundMessage"]);
                    }
                    else
                    {
                        _logger.LogInformation(_localizer["RolesFetchedSuccessfully"], pagePath);
                        response = Ok(roles);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["ErrorFetchingRoles"], pagePath);
                    response = StatusCode(500, _localizer["InternalServerErrorMessage"]);
                }
            }
            return response;
        }
    }
}
