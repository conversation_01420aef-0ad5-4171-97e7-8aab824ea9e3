using NUnit.Framework;
using Moq;
using MemberServiceBusinessLayer.QueryHandler;
using MemberServiceDataAccessLayer.Implementation;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;

namespace MemberServiceApiBusinessLayerTestCases.QueryHandlerTests
{
    [TestFixture]
    public class PlanTypeQueryHandlerTests
    {
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<IPlanTypeRepository> _mockPlanTypeRepository;
        private PlanTypeQueryHandler _planTypeQueryHandler;

        [SetUp]
        public void Setup()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockPlanTypeRepository = new Mock<IPlanTypeRepository>();

            _mockUnitOfWork.Setup(u => u.PlanTypeRepository).Returns(_mockPlanTypeRepository.Object);

            _planTypeQueryHandler = new PlanTypeQueryHandler(_mockUnitOfWork.Object);
        }

        [Test]
        public async Task GetPlanTypeByIdAsync_ShouldReturnPlanType_WhenPlanTypeExists()
        {
            // Arrange
            var planTypeId = Guid.NewGuid();
            var createdDate = DateTime.Now.AddDays(-30);
            var expectedPlanType = new PlanType 
            { 
                Id = planTypeId, 
                PlanName = "Premium Plan",
                CreatedDate = createdDate
            };

            _mockPlanTypeRepository
                .Setup(r => r.GetByIdAsync(planTypeId))
                .ReturnsAsync(expectedPlanType);

            // Act
            var result = await _planTypeQueryHandler.GetPlanTypeByIdAsync(planTypeId);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(planTypeId));
            Assert.That(result.PlanName, Is.EqualTo("Premium Plan"));
            Assert.That(result.CreatedDate, Is.EqualTo(createdDate));
            
            _mockPlanTypeRepository.Verify(r => r.GetByIdAsync(planTypeId), Times.Once);
        }

        [Test]
        public async Task GetPlanTypeByIdAsync_ShouldReturnNull_WhenPlanTypeDoesNotExist()
        {
            // Arrange
            var planTypeId = Guid.NewGuid();

            _mockPlanTypeRepository
                .Setup(r => r.GetByIdAsync(planTypeId))
                .ReturnsAsync((PlanType)null);

            // Act
            var result = await _planTypeQueryHandler.GetPlanTypeByIdAsync(planTypeId);

            // Assert
            Assert.That(result, Is.Null);
            _mockPlanTypeRepository.Verify(r => r.GetByIdAsync(planTypeId), Times.Once);
        }

        [Test]
        public async Task GetAllPlanTypesAsync_ShouldReturnAllPlanTypes()
        {
            // Arrange
            var planTypes = new List<PlanType>
            {
                new PlanType 
                { 
                    Id = Guid.NewGuid(), 
                    PlanName = "Basic Plan",
                    CreatedDate = DateTime.Now.AddDays(-60)
                },
                new PlanType 
                { 
                    Id = Guid.NewGuid(), 
                    PlanName = "Premium Plan",
                    CreatedDate = DateTime.Now.AddDays(-30)
                },
                new PlanType 
                { 
                    Id = Guid.NewGuid(), 
                    PlanName = "Enterprise Plan",
                    CreatedDate = DateTime.Now.AddDays(-15)
                }
            };

            _mockPlanTypeRepository
                .Setup(r => r.GetAllAsync())
                .ReturnsAsync(planTypes);

            // Act
            var result = await _planTypeQueryHandler.GetAllPlanTypesAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(3));
            Assert.That(result.Select(p => p.PlanName).ToList(), 
                Contains.Item("Basic Plan").And.Contains("Premium Plan").And.Contains("Enterprise Plan"));
            
            _mockPlanTypeRepository.Verify(r => r.GetAllAsync(), Times.Once);
        }

        [Test]
        public void Constructor_ShouldThrowArgumentNullException_WhenUnitOfWorkIsNull()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new PlanTypeQueryHandler(null));
        }
    }
}
