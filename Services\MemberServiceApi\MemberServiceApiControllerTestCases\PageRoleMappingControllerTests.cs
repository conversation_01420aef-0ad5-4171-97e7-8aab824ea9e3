using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using MemberServiceApi.Controllers;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using MemberServiceBusinessLayer;
using System.Linq;

namespace MemberServiceApiControllerTestCases
{
    [TestFixture]
    public class PageRoleMappingControllerTests
    {
        private Mock<IPageRoleMappingQueryHandler<PageRoleMapping>> _mockQueryHandler;
        private Mock<IPageRoleMappingCommandHandler<PageRoleMapping>> _mockCommandHandler;
        private Mock<ILogger<PageRoleMappingController>> _mockLogger;
        private Mock<IStringLocalizer<PageRoleMappingController>> _mockLocalizer;
        private PageRoleMappingController _controller;

        [SetUp]
        public void Setup()
        {
            _mockQueryHandler = new Mock<IPageRoleMappingQueryHandler<PageRoleMapping>>();
            _mockCommandHandler = new Mock<IPageRoleMappingCommandHandler<PageRoleMapping>>();
            _mockLogger = new Mock<ILogger<PageRoleMappingController>>();
            _mockLocalizer = new Mock<IStringLocalizer<PageRoleMappingController>>();

            // Setup localizer to return the key when requested
            _mockLocalizer.Setup(l => l[It.IsAny<string>()]).Returns((string key) => new LocalizedString(key, key));

            _controller = new PageRoleMappingController(
                _mockQueryHandler.Object,
                _mockCommandHandler.Object,
                _mockLogger.Object,
                _mockLocalizer.Object
            );
        }

        [Test]
        public async Task GetAllPageRoleMappings_WhenMappingsExist_ReturnsOkWithMappings()
        {
            // Arrange
            var mockMappings = new List<PageRoleMapping>
            {
                new PageRoleMapping { Id = Guid.NewGuid(), PagePath = "/dashboard", RoleId = Guid.NewGuid(), OrganizationId = Guid.NewGuid() },
                new PageRoleMapping { Id = Guid.NewGuid(), PagePath = "/users", RoleId = Guid.NewGuid(), OrganizationId = Guid.NewGuid() }
            };

            _mockQueryHandler
                .Setup(q => q.GetAllPageRoleMappingsAsync())
                .ReturnsAsync(mockMappings);

            // Act
            var result = await _controller.GetAllPageRoleMappings();

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult?.StatusCode, Is.EqualTo(200));
            Assert.That(okResult?.Value, Is.EqualTo(mockMappings));
        }

        [Test]
        public async Task GetAllPageRoleMappings_WhenNoMappingsExist_ReturnsNotFound()
        {
            // Arrange
            _mockQueryHandler
                .Setup(q => q.GetAllPageRoleMappingsAsync())
                .ReturnsAsync(new List<PageRoleMapping>());

            // Act
            var result = await _controller.GetAllPageRoleMappings();

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult?.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task GetAllPageRoleMappings_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            _mockQueryHandler
                .Setup(q => q.GetAllPageRoleMappingsAsync())
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetAllPageRoleMappings();

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task GetPageRoleMappingById_WhenMappingExists_ReturnsOkWithMapping()
        {
            // Arrange
            var mappingId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var mockMapping = new PageRoleMapping
            {
                Id = mappingId,
                PagePath = "/dashboard",
                RoleId = Guid.NewGuid(),
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockQueryHandler
                .Setup(q => q.GetPageRoleMappingByIdAsync(mappingId, orgId, subscription))
                .ReturnsAsync(mockMapping);

            // Act
            var result = await _controller.GetPageRoleMappingById(mappingId, orgId, subscription);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult?.StatusCode, Is.EqualTo(200));
            Assert.That(okResult?.Value, Is.EqualTo(mockMapping));
        }

        [Test]
        public async Task GetPageRoleMappingById_WhenMappingDoesNotExist_ReturnsNotFound()
        {
            // Arrange
            var mappingId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockQueryHandler
                .Setup(q => q.GetPageRoleMappingByIdAsync(mappingId, orgId, subscription))
                .ReturnsAsync((PageRoleMapping)null);

            // Act
            var result = await _controller.GetPageRoleMappingById(mappingId, orgId, subscription);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult?.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task GetPageRoleMappingById_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var mappingId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockQueryHandler
                .Setup(q => q.GetPageRoleMappingByIdAsync(mappingId, orgId, subscription))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetPageRoleMappingById(mappingId, orgId, subscription);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task GetPagesByRoleId_WhenMappingsExist_ReturnsOkWithMappings()
        {
            // Arrange
            var roleId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var mockMappings = new List<PageRoleMapping>
            {
                new PageRoleMapping { Id = Guid.NewGuid(), PagePath = "/dashboard", RoleId = roleId, OrganizationId = orgId, Subscription = subscription },
                new PageRoleMapping { Id = Guid.NewGuid(), PagePath = "/users", RoleId = roleId, OrganizationId = orgId, Subscription = subscription }
            };

            _mockQueryHandler
                .Setup(q => q.GetPagesByRoleIdAsync(roleId, orgId, subscription))
                .ReturnsAsync(mockMappings);

            // Act
            var result = await _controller.GetPagesByRoleId(roleId, orgId, subscription);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult?.StatusCode, Is.EqualTo(200));
            Assert.That(okResult?.Value, Is.EqualTo(mockMappings));
        }

        [Test]
        public async Task GetPagesByRoleId_WhenNoMappingsExist_ReturnsNotFound()
        {
            // Arrange
            var roleId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockQueryHandler
                .Setup(q => q.GetPagesByRoleIdAsync(roleId, orgId, subscription))
                .ReturnsAsync(new List<PageRoleMapping>());

            // Act
            var result = await _controller.GetPagesByRoleId(roleId, orgId, subscription);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult?.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task GetPagesByRoleId_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var roleId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockQueryHandler
                .Setup(q => q.GetPagesByRoleIdAsync(roleId, orgId, subscription))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetPagesByRoleId(roleId, orgId, subscription);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }



        [Test]
        public async Task AddPageRoleMapping_WhenValidMapping_ReturnsCreatedAtAction()
        {
            // Arrange
            var mappingId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var mapping = new PageRoleMapping
            {
                Id = mappingId,
                PagePath = "/dashboard",
                RoleId = Guid.NewGuid(),
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockCommandHandler
                .Setup(c => c.AddPageRoleMappingAsync(mapping, orgId, subscription))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.AddPageRoleMapping(mapping);

            // Assert
            Assert.That(result, Is.InstanceOf<CreatedAtActionResult>());
            var createdResult = result as CreatedAtActionResult;
            Assert.That(createdResult?.StatusCode, Is.EqualTo(201));
            Assert.That(createdResult?.ActionName, Is.EqualTo(nameof(PageRoleMappingController.GetPageRoleMappingById)));
            Assert.That(createdResult?.RouteValues["id"], Is.EqualTo(mappingId));
            Assert.That(createdResult?.Value, Is.EqualTo(mapping));
        }

        [Test]
        public async Task AddPageRoleMapping_WhenNullMapping_ReturnsBadRequest()
        {
            // Act
            var result = await _controller.AddPageRoleMapping(null);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult?.StatusCode, Is.EqualTo(400));
        }

        [Test]
        public async Task AddPageRoleMapping_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var mappingId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var mapping = new PageRoleMapping
            {
                Id = mappingId,
                PagePath = "/dashboard",
                RoleId = Guid.NewGuid(),
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockCommandHandler
                .Setup(c => c.AddPageRoleMappingAsync(mapping, orgId, subscription))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.AddPageRoleMapping(mapping);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task UpdatePageRoleMapping_WhenValidMapping_ReturnsNoContent()
        {
            // Arrange
            var mappingId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var mapping = new PageRoleMapping
            {
                Id = mappingId,
                PagePath = "/dashboard",
                RoleId = Guid.NewGuid(),
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockCommandHandler
                .Setup(c => c.UpdatePageRoleMappingAsync(mapping, orgId, subscription))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.UpdatePageRoleMapping(mappingId, mapping);

            // Assert
            Assert.That(result, Is.InstanceOf<NoContentResult>());
            var noContentResult = result as NoContentResult;
            Assert.That(noContentResult?.StatusCode, Is.EqualTo(204));
        }

        [Test]
        public async Task UpdatePageRoleMapping_WhenNullMapping_ReturnsBadRequest()
        {
            // Arrange
            var mappingId = Guid.NewGuid();

            // Act
            var result = await _controller.UpdatePageRoleMapping(mappingId, null);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult?.StatusCode, Is.EqualTo(400));
        }

        [Test]
        public async Task UpdatePageRoleMapping_WhenIdMismatch_ReturnsBadRequest()
        {
            // Arrange
            var mappingId = Guid.NewGuid();
            var differentId = Guid.NewGuid();
            var mapping = new PageRoleMapping
            {
                Id = differentId,
                PagePath = "/dashboard",
                RoleId = Guid.NewGuid(),
                OrganizationId = Guid.NewGuid()
            };

            // Act
            var result = await _controller.UpdatePageRoleMapping(mappingId, mapping);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult?.StatusCode, Is.EqualTo(400));
        }

        [Test]
        public async Task UpdatePageRoleMapping_WhenMappingDoesNotExist_ReturnsNotFound()
        {
            // Arrange
            var mappingId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var mapping = new PageRoleMapping
            {
                Id = mappingId,
                PagePath = "/dashboard",
                RoleId = Guid.NewGuid(),
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockCommandHandler
                .Setup(c => c.UpdatePageRoleMappingAsync(mapping, orgId, subscription))
                .ThrowsAsync(new KeyNotFoundException());

            // Act
            var result = await _controller.UpdatePageRoleMapping(mappingId, mapping);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult?.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task UpdatePageRoleMapping_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var mappingId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var mapping = new PageRoleMapping
            {
                Id = mappingId,
                PagePath = "/dashboard",
                RoleId = Guid.NewGuid(),
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockCommandHandler
                .Setup(c => c.UpdatePageRoleMappingAsync(mapping, orgId, subscription))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.UpdatePageRoleMapping(mappingId, mapping);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task DeletePageRoleMapping_WhenMappingExists_ReturnsNoContent()
        {
            // Arrange
            var mappingId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockCommandHandler
                .Setup(c => c.DeletePageRoleMappingAsync(mappingId, orgId, subscription))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.DeletePageRoleMapping(mappingId, orgId, subscription);

            // Assert
            Assert.That(result, Is.InstanceOf<NoContentResult>());
            var noContentResult = result as NoContentResult;
            Assert.That(noContentResult?.StatusCode, Is.EqualTo(204));
        }

        [Test]
        public async Task DeletePageRoleMapping_WhenMappingDoesNotExist_ReturnsNotFound()
        {
            // Arrange
            var mappingId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockCommandHandler
                .Setup(c => c.DeletePageRoleMappingAsync(mappingId, orgId, subscription))
                .ThrowsAsync(new KeyNotFoundException());

            // Act
            var result = await _controller.DeletePageRoleMapping(mappingId, orgId, subscription);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult?.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task DeletePageRoleMapping_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var mappingId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockCommandHandler
                .Setup(c => c.DeletePageRoleMappingAsync(mappingId, orgId, subscription))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.DeletePageRoleMapping(mappingId, orgId, subscription);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }
    }
}
