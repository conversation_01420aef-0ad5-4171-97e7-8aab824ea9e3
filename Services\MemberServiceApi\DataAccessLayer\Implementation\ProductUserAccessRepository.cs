﻿using Contracts;
using DataAccessLayer.Context;
using Interfaces.ShardManagement;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MemberServiceDataAccessLayer.Implementation
{
    public class ProductUserAccessRepository : ShardGenericRepository<ProductUserAccess>, IProductUserAccessRepository
    {
        private readonly AccountDatabaseContext _context;
        private readonly ShardMapManagerService<AccountDatabaseContext, AccountDatabaseContext> _shardMapManagerService;
        private readonly IStringLocalizer<AccountDatabaseContext> _localizer;
        private readonly ILogger<AccountDatabaseContext> _logger;
        private readonly string _shardMapName;

        public ProductUserAccessRepository(AccountDatabaseContext context, ShardMapManagerService<AccountDatabaseContext, AccountDatabaseContext> shardMapManagerService, IStringLocalizer<AccountDatabaseContext> localizer,
            ILogger<AccountDatabaseContext> logger) : base(context, shardMapManagerService, localizer, logger)
        {
            _context = context;
            _shardMapManagerService = shardMapManagerService;
            _localizer = localizer;
            _logger = logger;
            _shardMapName = Environment.GetEnvironmentVariable("shardMapName");
        }

        public async Task<IEnumerable<Member>> GetMembersForProduct(Guid productId, Guid orgId, bool subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(subscription, orgId, _shardMapName);
            if (context == null)
                return Enumerable.Empty<Member>();

            return await context.ProductUserAccess
                .Where(pua => pua.ProductId == productId)
                .Select(pua => new Member
                {
                    Id = pua.Member.Id,
                    UserName = pua.Member.UserName,
                    Email = pua.Member.Email,
                    Password = pua.Member.Password,
                    IsActive = pua.HasAccess
                })
                .ToListAsync();
        }

        public async Task<ProductUserAccess> GetAccessRecordAsync(Guid productId, Guid memberId, Guid orgId, bool subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(subscription, orgId, _shardMapName);
            if (context == null)
                return null;

            return await context.ProductUserAccess
                .FirstOrDefaultAsync(a => a.ProductId == productId && a.MemberId == memberId);
        }

    }
}
