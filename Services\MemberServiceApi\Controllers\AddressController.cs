﻿using Contracts;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MemberServiceBusinessLayer;

namespace MemberServiceApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class AddressController : ControllerBase
    {
        private readonly IAddressesQueryHandler<Address> _addressQueryHandler;
        private readonly IAddressesCommandHandler<Address> _addressCommandHandler;
        private readonly ILogger<AddressController> _logger;
        private readonly IStringLocalizer<AddressController> _localizer;

        public AddressController(
            IAddressesQueryHandler<Address> addressQueryHandler,
            IAddressesCommandHandler<Address> addressCommandHandler,
            ILogger<AddressController> logger,
            IStringLocalizer<AddressController> localizer)
        {
            _addressQueryHandler = addressQueryHandler ?? throw new ArgumentNullException(nameof(addressQueryHandler));
            _addressCommandHandler = addressCommandHandler ?? throw new ArgumentNullException(nameof(addressCommandHandler));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _localizer = localizer ?? throw new ArgumentNullException(nameof(localizer));
        }

        [HttpGet("{id:Guid}/{OrgID:Guid}/{Subscription}")]
        public async Task<IActionResult> GetAddressById(Guid id, Guid OrgID, bool Subscription)
        {
            IActionResult response;
            _logger.LogInformation(_localizer["FetchingAddressWithID"], id);
            var address = await _addressQueryHandler.GetAddressByIdAsync(id, OrgID, Subscription);
            if (address == null)
            {
                _logger.LogWarning(_localizer["AddressNotFound"], id);
                response = NotFound(_localizer["AddressNotFoundMessage"]);
            }
            else
            {
                _logger.LogInformation(_localizer["AddressFetchedSuccessfully"], id);
                response = Ok(address);
            }
            return response;
        }

        [HttpPost]
        public async Task<IActionResult> AddAddress([FromBody] Address address)
        {
            IActionResult response;
            if (address == null)
            {
                _logger.LogWarning(_localizer["InvalidAddressData"]);
                response = BadRequest(_localizer["AddressInvalidMessage"]);
            }
            else
            {
                try
                {
                    _logger.LogInformation(_localizer["AddingNewAddress"]);
                    await _addressCommandHandler.AddAddressAsync(new List<Address> { address },address.Subscription, address.OrganizationID);
                    _logger.LogInformation(_localizer["AddressAddedSuccessfully"], address.AddressId);
                    //response = CreatedAtAction(nameof(GetAddressById), new { id = address.AddressId, orgId = address.OrganizationId, subscription = address.Subscription }, address);
                    response = CreatedAtAction(nameof(GetAddressById),
                        new { id = address.AddressId, orgId = address.OrganizationID, subscription = address.Subscription },
                        new
                        {
                            address.AddressId,
                            address.OrganizationID,
                            address.Subscription
                            // add any other safe fields you want to expose here
                        });

                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["ErrorAddingAddress"]);
                    response = StatusCode(500, _localizer["InternalServerErrorMessage"]);
                }
            }
            return response;
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateAddress(Guid id, [FromBody] Address address)
        {
            IActionResult response;
            if (address == null || id != address.AddressId)
            {
                _logger.LogWarning(_localizer["InvalidDataForUpdate"], id);
                response = BadRequest(_localizer["AddressInvalidMessage"]);
            }
            else
            {
                try
                {
                    _logger.LogInformation(_localizer["UpdatingAddressWithID"], id);

                    await _addressCommandHandler.UpdateAddressAsync(address,address.Subscription,address.OrganizationID);
                    _logger.LogInformation(_localizer["AddressUpdatedSuccessfully"], id);
                    response = NoContent();
                }
                catch (KeyNotFoundException)
                {
                    _logger.LogWarning(_localizer["AddressNotFoundForUpdate"], id);
                    response = NotFound(_localizer["AddressNotFoundMessage"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["ErrorUpdatingAddress"]);
                    response = StatusCode(500, _localizer["InternalServerErrorMessage"]);
                }
            }
            return response;
        }

        [HttpDelete("{id:guid}/{OrgID:Guid}/{Subscription}")]
        public async Task<IActionResult> DeleteAddress(Guid id, Guid OrgID, bool Subscription)
        {
            IActionResult response;
            try
            {
                _logger.LogInformation(_localizer["DeletingAddressWithID"], id);
                await _addressCommandHandler.DeleteAddressAsync(id, Subscription, OrgID);
                _logger.LogInformation(_localizer["AddressDeletedSuccessfully"], id);
                response = NoContent();
            }
            catch (KeyNotFoundException)
            {
                _logger.LogWarning(_localizer["AddressNotFoundForDeletion"], id);
                response = NotFound(_localizer["AddressNotFoundMessage"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorDeletingAddress"]);
                response = StatusCode(500, _localizer["InternalServerErrorMessage"]);
            }
            return response;
        }

        [HttpGet]
        [Route("{orgId:guid}/{Subscription}")]
        public async Task<IActionResult> GetAllAddresses(Guid orgId, bool Subscription)
        {
            IActionResult response;
            _logger.LogInformation(_localizer["FetchingAllAddresses"]);
            try
            {
                var addresses = await _addressQueryHandler.GetAllAddressesAsync(orgId, Subscription);

                if (addresses == null || addresses.Count == 0)
                {
                    _logger.LogWarning(_localizer["NoAddressesFound"]);
                    response = NotFound(_localizer["AddressesNotFoundMessage"]);
                }
                else
                {
                    _logger.LogInformation(_localizer["AllAddressesFetchedSuccessfully"]);
                    response = Ok(addresses);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingAddresses"]);
                response = StatusCode(500, _localizer["InternalServerErrorMessage"]);
            }
            return response;
        }
    }
}
