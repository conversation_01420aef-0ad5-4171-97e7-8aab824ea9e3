using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using MemberServiceApi.Controllers;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using MemberServiceBusinessLayer;
using System.Linq;

namespace MemberServiceApiControllerTestCases
{
    [TestFixture]
    public class PredefinedVisitTypeControllerTests
    {
        private Mock<IPredefinedVisitTypeQueryHandler<PredefinedVisitType>> _mockQueryHandler;
        private Mock<ILogger<PredefinedVisitTypeController>> _mockLogger;
        private Mock<IStringLocalizer<PredefinedVisitTypeController>> _mockLocalizer;
        private PredefinedVisitTypeController _controller;

        [SetUp]
        public void Setup()
        {
            _mockQueryHandler = new Mock<IPredefinedVisitTypeQueryHandler<PredefinedVisitType>>();
            _mockLogger = new Mock<ILogger<PredefinedVisitTypeController>>();
            _mockLocalizer = new Mock<IStringLocalizer<PredefinedVisitTypeController>>();

            // Setup localizer to return the key when requested
            _mockLocalizer.Setup(l => l[It.IsAny<string>()]).Returns((string key) => new LocalizedString(key, key));

            _controller = new PredefinedVisitTypeController(
                _mockQueryHandler.Object,
                _mockLogger.Object,
                _mockLocalizer.Object
            );
        }

        [Test]
        public async Task GetAll_WhenVisitTypesExist_ReturnsOkWithVisitTypes()
        {
            // Arrange
            var mockVisitTypes = new List<PredefinedVisitType>
            {
                new PredefinedVisitType { ID = Guid.NewGuid(), VisitName = "Visit Type 1", CPTCode = "12345" },
                new PredefinedVisitType { ID = Guid.NewGuid(), VisitName = "Visit Type 2", CPTCode = "67890" }
            };

            _mockQueryHandler
                .Setup(q => q.GetPredefinedVisitType())
                .ReturnsAsync(mockVisitTypes);

            // Act
            var result = await _controller.GetAll();

            // Assert
            Assert.That(result.Result, Is.InstanceOf<OkObjectResult>());
            var okResult = result.Result as OkObjectResult;
            Assert.That(okResult.StatusCode, Is.EqualTo(200));
            Assert.That(okResult.Value, Is.EqualTo(mockVisitTypes));
        }

        [Test]
        public async Task GetAll_WhenNoVisitTypesExist_ReturnsNotFound()
        {
            // Arrange
            _mockQueryHandler
                .Setup(q => q.GetPredefinedVisitType())
                .ReturnsAsync(new List<PredefinedVisitType>());

            // Act
            var result = await _controller.GetAll();

            // Assert
            Assert.That(result.Result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result.Result as NotFoundObjectResult;
            Assert.That(notFoundResult.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task GetAll_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            _mockQueryHandler
                .Setup(q => q.GetPredefinedVisitType())
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetAll();

            // Assert
            Assert.That(result.Result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result.Result as ObjectResult;
            Assert.That(statusCodeResult.StatusCode, Is.EqualTo(500));
        }

        // GetById method is not implemented in PredefinedVisitTypeController
        // This test has been removed

        // GetById method is not implemented in PredefinedVisitTypeController
        // This test has been removed

        // GetById method is not implemented in PredefinedVisitTypeController
        // This test has been removed
    }
}
