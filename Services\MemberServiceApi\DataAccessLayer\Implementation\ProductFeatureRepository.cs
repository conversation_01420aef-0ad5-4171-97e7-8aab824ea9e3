﻿using Contracts;
using DataAccessLayer.Context;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;

namespace MemberServiceDataAccessLayer.Implementation
{
    public class ProductFeatureRepository : GenericRepository<ProductFeature>, IProductFeatureRepository
    {
        private readonly AccountDatabaseContext _context;

        public ProductFeatureRepository(AccountDatabaseContext context) : base(context)
        {
            _context = context;
        }

        public async Task AddAsync(ProductFeature feature)
        {
            await _context.ProductFeatures.AddAsync(feature);
        }

        public async Task UpdateAsync(ProductFeature feature)
        {
            _context.ProductFeatures.Update(feature);
            await Task.CompletedTask;
        }

        public async Task DeleteByIdAsync(Guid id)
        {
            var feature = await _context.ProductFeatures.FindAsync(id);
            if (feature != null)
            {
                _context.ProductFeatures.Remove(feature);
            }
        }

        public async Task<ProductFeature> GetByIdAsync(Guid id)
        {
            var result = await _context.ProductFeatures.FindAsync(id);
            return result;
        }

        public async Task<List<ProductFeature>> GetAllAsync()
        {
            var result = await _context.ProductFeatures.ToListAsync();
            return result;
        }

    }
}