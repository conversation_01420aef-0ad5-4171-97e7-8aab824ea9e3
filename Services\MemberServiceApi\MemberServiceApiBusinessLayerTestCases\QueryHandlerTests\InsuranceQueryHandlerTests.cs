using NUnit.Framework;
using Moq;
using MemberServiceBusinessLayer.QueryHandler;
using MemberServiceDataAccessLayer.Implementation;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;

namespace MemberServiceApiBusinessLayerTestCases.QueryHandlerTests
{
    [TestFixture]
    public class InsuranceQueryHandlerTests
    {
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<IInsuranceRepository> _mockInsuranceRepository;
        private InsuranceQueryHandler _insuranceQueryHandler;

        [SetUp]
        public void Setup()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockInsuranceRepository = new Mock<IInsuranceRepository>();

            _mockUnitOfWork.Setup(u => u.InsuranceRepository).Returns(_mockInsuranceRepository.Object);

            _insuranceQueryHandler = new InsuranceQueryHandler(_mockUnitOfWork.Object);
        }

        [Test]
        public async Task GetInsuranceByIdAsync_ShouldReturnInsurance_WhenInsuranceExists()
        {
            // Arrange
            var insuranceId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var expectedInsurance = new Insurance 
            { 
                InsuranceId = insuranceId, 
                PrimaryInsuranceProvider = "Insurance Co", 
                PolicyNumber = "POL123456", 
                PlanName = "Basic Plan", 
                GroupNumber = "GRP123",
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockInsuranceRepository
                .Setup(r => r.GetByIdAsync(insuranceId, orgId, subscription))
                .ReturnsAsync(expectedInsurance);

            // Act
            var result = await _insuranceQueryHandler.GetInsuranceByIdAsync(insuranceId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.InsuranceId, Is.EqualTo(insuranceId));
            Assert.That(result.PrimaryInsuranceProvider, Is.EqualTo("Insurance Co"));
            Assert.That(result.PolicyNumber, Is.EqualTo("POL123456"));
            Assert.That(result.PlanName, Is.EqualTo("Basic Plan"));
            Assert.That(result.GroupNumber, Is.EqualTo("GRP123"));
            Assert.That(result.OrganizationId, Is.EqualTo(orgId));
            Assert.That(result.Subscription, Is.EqualTo(subscription));
            
            _mockInsuranceRepository.Verify(r => r.GetByIdAsync(insuranceId, orgId, subscription), Times.Once);
        }

        [Test]
        public async Task GetInsuranceByIdAsync_ShouldReturnNull_WhenInsuranceDoesNotExist()
        {
            // Arrange
            var insuranceId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockInsuranceRepository
                .Setup(r => r.GetByIdAsync(insuranceId, orgId, subscription))
                .ReturnsAsync((Insurance)null);

            // Act
            var result = await _insuranceQueryHandler.GetInsuranceByIdAsync(insuranceId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Null);
            _mockInsuranceRepository.Verify(r => r.GetByIdAsync(insuranceId, orgId, subscription), Times.Once);
        }

        [Test]
        public async Task GetAllInsurancesAsync_ShouldReturnAllInsurances()
        {
            // Arrange
            var insurances = new List<Insurance>
            {
                new Insurance 
                { 
                    InsuranceId = Guid.NewGuid(), 
                    PrimaryInsuranceProvider = "Insurance Co A", 
                    PolicyNumber = "POL123456", 
                    PlanName = "Basic Plan", 
                    GroupNumber = "GRP123" 
                },
                new Insurance 
                { 
                    InsuranceId = Guid.NewGuid(), 
                    PrimaryInsuranceProvider = "Insurance Co B", 
                    PolicyNumber = "POL789012", 
                    PlanName = "Premium Plan", 
                    GroupNumber = "GRP456" 
                }
            };

            _mockInsuranceRepository
                .Setup(r => r.GetAllAsync())
                .ReturnsAsync(insurances);

            // Act
            var result = await _insuranceQueryHandler.GetAllInsurancesAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result.First().PrimaryInsuranceProvider, Is.EqualTo("Insurance Co A"));
            Assert.That(result.Last().PrimaryInsuranceProvider, Is.EqualTo("Insurance Co B"));
            
            _mockInsuranceRepository.Verify(r => r.GetAllAsync(), Times.Once);
        }

        [Test]
        public void Constructor_ShouldThrowArgumentNullException_WhenUnitOfWorkIsNull()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new InsuranceQueryHandler(null));
        }
    }
}
