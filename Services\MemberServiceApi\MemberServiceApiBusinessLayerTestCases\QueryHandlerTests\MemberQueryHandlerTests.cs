using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Configuration;
using MemberServiceBusinessLayer.QueryHandler;
using MemberServiceDataAccessLayer.Implementation;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;

namespace MemberServiceApiBusinessLayerTestCases.QueryHandlerTests
{
    [TestFixture]
    public class MemberQueryHandlerTests
    {
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<IMemberRepository> _mockMemberRepository;
        private MemberQueryHandler _memberQueryHandler;

        [SetUp]
        public void Setup()
        {
            _mockConfiguration = new Mock<IConfiguration>();
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockMemberRepository = new Mock<IMemberRepository>();

            _mockUnitOfWork.Setup(u => u.MemberRepository).Returns(_mockMemberRepository.Object);

            _memberQueryHandler = new MemberQueryHandler(_mockConfiguration.Object, _mockUnitOfWork.Object);
        }

        [Test]
        public async Task GetMember_ShouldReturnAllMembers()
        {
            // Arrange
            var expectedMembers = new List<Member>
            {
                new Member { Id = Guid.NewGuid(), FirstName = "John", LastName = "Doe" },
                new Member { Id = Guid.NewGuid(), FirstName = "Jane", LastName = "Smith" }
            };

            _mockMemberRepository.Setup(r => r.GetAllAsync()).ReturnsAsync(expectedMembers);

            // Act
            var result = await _memberQueryHandler.GetMember();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.EquivalentTo(expectedMembers));
            _mockMemberRepository.Verify(r => r.GetAllAsync(), Times.Once);
        }

        [Test]
        public async Task GetMemberById_ShouldReturnMember_WhenMemberExists()
        {
            // Arrange
            var memberId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var expectedMember = new Member
            {
                Id = memberId,
                FirstName = "John",
                LastName = "Doe",
                OrganizationID = orgId,
                Subscription = subscription
            };

            _mockMemberRepository.Setup(r => r.GetByIdAsync(memberId, orgId, subscription)).ReturnsAsync(expectedMember);

            // Act
            var result = await _memberQueryHandler.GetMemberById(memberId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(memberId));
            Assert.That(result.FirstName, Is.EqualTo("John"));
            Assert.That(result.LastName, Is.EqualTo("Doe"));
            _mockMemberRepository.Verify(r => r.GetByIdAsync(memberId, orgId, subscription), Times.Once);
        }

        [Test]
        public async Task GetMemberById_ShouldReturnNull_WhenMemberDoesNotExist()
        {
            // Arrange
            var memberId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockMemberRepository.Setup(r => r.GetByIdAsync(memberId, orgId, subscription)).ReturnsAsync((Member)null);

            // Act
            var result = await _memberQueryHandler.GetMemberById(memberId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Null);
            _mockMemberRepository.Verify(r => r.GetByIdAsync(memberId, orgId, subscription), Times.Once);
        }

        [Test]
        public async Task SearchMembersAsync_ShouldReturnMatchingMembers()
        {
            // Arrange
            string searchTerm = "John";
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var expectedMembers = new List<Member>
            {
                new Member { Id = Guid.NewGuid(), FirstName = "John", LastName = "Doe" },
                new Member { Id = Guid.NewGuid(), FirstName = "Johnny", LastName = "Smith" }
            };

            _mockMemberRepository.Setup(r => r.SearchMembersAsync(searchTerm, orgId, subscription)).ReturnsAsync(expectedMembers);

            // Act
            var result = await _memberQueryHandler.SearchMembersAsync(searchTerm, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(2));
            Assert.That(result, Is.EquivalentTo(expectedMembers));
            _mockMemberRepository.Verify(r => r.SearchMembersAsync(searchTerm, orgId, subscription), Times.Once);
        }

        [Test]
        public async Task GetPatientdatabyid_ShouldReturnPatient_WhenPatientExists()
        {
            // Arrange
            var patientId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var expectedPatient = new Patient
            {
                Id = patientId,
                Name = "John Doe",
                OrganizationID = orgId,
                Subscription = subscription
            };

            _mockMemberRepository.Setup(r => r.GetByIdAsync(patientId, orgId, subscription)).ReturnsAsync(new Member
            {
                Id = patientId,
                UserName = "John Doe",
                OrganizationID = orgId,
                Subscription = subscription,
                AddressId = Guid.NewGuid(),
                InsuranceId = Guid.NewGuid()
            });

            _mockUnitOfWork.Setup(u => u.AddressesRepository.GetByIdAsync(It.IsAny<Guid>(), orgId, subscription))
                .ReturnsAsync(new Address { AddressLine1 = "123 Main St", City = "Anytown", State = "CA", PostalCode = "12345" });

            _mockUnitOfWork.Setup(u => u.InsuranceRepository.GetByIdAsync(It.IsAny<Guid>(), orgId, subscription))
                .ReturnsAsync(new Insurance { PrimaryInsuranceProvider = "Insurance Co", PolicyNumber = "12345", PlanName = "Basic Plan", GroupNumber = "G123" });

            // Act
            var result = await _memberQueryHandler.GetPatientdatabyid(patientId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(patientId));
            Assert.That(result.Name, Is.EqualTo("John Doe"));
            _mockMemberRepository.Verify(r => r.GetByIdAsync(patientId, orgId, subscription), Times.Once);
        }

        [Test]
        public async Task GetPatientsByIdsAsync_ShouldReturnPatients_WhenPatientsExist()
        {
            // Arrange
            var patientIds = new List<Guid> { Guid.NewGuid(), Guid.NewGuid() };
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var expectedPatients = new List<Office_visit_members>
            {
                new Office_visit_members { PatientId = patientIds[0], UserName = "John" },
                new Office_visit_members { PatientId = patientIds[1], UserName = "Jane" }
            };

            _mockMemberRepository.Setup(r => r.GetPatientsByIdsAsync(patientIds, orgId, subscription)).ReturnsAsync(expectedPatients);

            // Act
            var result = await _memberQueryHandler.GetPatientsByIdsAsync(patientIds, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(2));
            Assert.That(result, Is.EquivalentTo(expectedPatients));
            _mockMemberRepository.Verify(r => r.GetPatientsByIdsAsync(patientIds, orgId, subscription), Times.Once);
        }
    }
}
