using NUnit.Framework;
using Moq;
using MemberServiceBusinessLayer.QueryHandler;
using MemberServiceDataAccessLayer.Implementation;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;

namespace MemberServiceApiBusinessLayerTestCases.QueryHandlerTests
{
    [TestFixture]
    public class FacilityQueryHandlerTests
    {
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<IFacilityRepository> _mockFacilityRepository;
        private FacilityQueryHandler _facilityQueryHandler;

        [SetUp]
        public void Setup()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockFacilityRepository = new Mock<IFacilityRepository>();

            _mockUnitOfWork.Setup(u => u.FacilityRepository).Returns(_mockFacilityRepository.Object);

            _facilityQueryHandler = new FacilityQueryHandler(_mockUnitOfWork.Object);
        }

        [Test]
        public async Task GetFacilityByIdAsync_ShouldReturnFacility_WhenFacilityExists()
        {
            // Arrange
            var facilityId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var expectedFacility = new Facility
            {
                FacilityId = facilityId,
                FacilityName = "Facility 1",
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockFacilityRepository.Setup(r => r.GetByIdAsync(facilityId, orgId, subscription)).ReturnsAsync(expectedFacility);

            // Act
            var result = await _facilityQueryHandler.GetFacilityByIdAsync(subscription, facilityId, orgId);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.FacilityId, Is.EqualTo(facilityId));
            Assert.That(result.FacilityName, Is.EqualTo("Facility 1"));
            _mockFacilityRepository.Verify(r => r.GetByIdAsync(facilityId, orgId, subscription), Times.Once);
        }

        [Test]
        public async Task GetFacilityByIdAsync_ShouldReturnNull_WhenFacilityDoesNotExist()
        {
            // Arrange
            var facilityId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockFacilityRepository.Setup(r => r.GetByIdAsync(facilityId, orgId, subscription)).ReturnsAsync((Facility)null);

            // Act
            var result = await _facilityQueryHandler.GetFacilityByIdAsync(subscription, facilityId, orgId);

            // Assert
            Assert.That(result, Is.Null);
            _mockFacilityRepository.Verify(r => r.GetByIdAsync(facilityId, orgId, subscription), Times.Once);
        }

        [Test]
        public async Task GetFacilitiesByNameAsync_ShouldReturnFacilities_WhenFacilitiesExist()
        {
            // Arrange
            string facilityName = "Test Facility";
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var expectedFacilities = new List<Facility>
            {
                new Facility { FacilityId = Guid.NewGuid(), FacilityName = "Test Facility 1", OrganizationId = orgId },
                new Facility { FacilityId = Guid.NewGuid(), FacilityName = "Test Facility 2", OrganizationId = orgId }
            };

            _mockFacilityRepository.Setup(r => r.GetFacilitiesByNameAsync(facilityName, orgId, subscription)).ReturnsAsync(expectedFacilities);

            // Act
            var result = await _facilityQueryHandler.GetFacilitiesByNameAsync(subscription, facilityName, orgId);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(2));
            Assert.That(result, Is.EquivalentTo(expectedFacilities));
            _mockFacilityRepository.Verify(r => r.GetFacilitiesByNameAsync(facilityName, orgId, subscription), Times.Once);
        }

        [Test]
        public async Task GetAllFacilitiesAsync_ShouldReturnAllFacilities()
        {
            // Arrange
            var expectedFacilities = new List<Facility>
            {
                new Facility { FacilityId = Guid.NewGuid(), FacilityName = "Facility 1" },
                new Facility { FacilityId = Guid.NewGuid(), FacilityName = "Facility 2" }
            };

            _mockFacilityRepository.Setup(r => r.GetAllAsync()).ReturnsAsync(expectedFacilities);

            // Act
            var result = await _facilityQueryHandler.GetAllFacilitiesAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result, Is.EquivalentTo(expectedFacilities));
            _mockFacilityRepository.Verify(r => r.GetAllAsync(), Times.Once);
        }

        [Test]
        public async Task GetFacilitiesByOrgAsync_ShouldReturnFacilities_WhenFacilitiesExist()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var expectedFacilities = new List<Facility>
            {
                new Facility { FacilityId = Guid.NewGuid(), FacilityName = "Facility 1", OrganizationId = orgId },
                new Facility { FacilityId = Guid.NewGuid(), FacilityName = "Facility 2", OrganizationId = orgId }
            };

            _mockFacilityRepository.Setup(r => r.GetByOrgIdAsync(orgId, subscription)).ReturnsAsync(expectedFacilities);

            // Act
            var result = await _facilityQueryHandler.GetFacilitiesByOrgAsync(subscription, orgId);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(2));
            Assert.That(result, Is.EquivalentTo(expectedFacilities));
            _mockFacilityRepository.Verify(r => r.GetByOrgIdAsync(orgId, subscription), Times.Once);
        }

        [Test]
        public void Constructor_ShouldThrowArgumentNullException_WhenUnitOfWorkIsNull()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new FacilityQueryHandler(null));
        }
    }
}
