using NUnit.Framework;
using Moq;
using MemberServiceBusinessLayer.CommandHandler;
using MemberServiceDataAccessLayer.Implementation;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MemberServiceApiBusinessLayerTestCases.CommandHandlerTests
{
    [TestFixture]
    public class RolesCommandHandlerTests
    {
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<IRolesRepository> _mockRolesRepository;
        private RolesCommandHandler _rolesCommandHandler;

        [SetUp]
        public void Setup()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockRolesRepository = new Mock<IRolesRepository>();

            _mockUnitOfWork.Setup(u => u.RolesRepository).Returns(_mockRolesRepository.Object);

            _rolesCommandHandler = new RolesCommandHandler(_mockUnitOfWork.Object);
        }

        [Test]
        public async Task AddRoleAsync_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var roles = new List<Role>
            {
                new Role { RoleId = Guid.NewGuid(), RoleName = "Admin", OrganizationId = orgId, Subscription = subscription },
                new Role { RoleId = Guid.NewGuid(), RoleName = "User", OrganizationId = orgId, Subscription = subscription }
            };

            _mockRolesRepository
                .Setup(r => r.AddAsync(roles, orgId, subscription))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _rolesCommandHandler.AddRoleAsync(roles, orgId, subscription);

            // Assert
            _mockRolesRepository.Verify(r => r.AddAsync(roles, orgId, subscription), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateRoleAsync_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var roleId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var role = new Role 
            { 
                RoleId = roleId, 
                RoleName = "Admin",
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockRolesRepository
                .Setup(r => r.UpdateAsync(role, orgId, subscription))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _rolesCommandHandler.UpdateRoleAsync(role, orgId, subscription);

            // Assert
            _mockRolesRepository.Verify(r => r.UpdateAsync(role, orgId, subscription), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteRoleAsync_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var roleId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockRolesRepository
                .Setup(r => r.DeleteByIdAsync(roleId, orgId, subscription))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _rolesCommandHandler.DeleteRoleAsync(roleId, orgId, subscription);

            // Assert
            _mockRolesRepository.Verify(r => r.DeleteByIdAsync(roleId, orgId, subscription), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task AddRoleAsync_WhenRepositoryThrowsException_ShouldPropagateException()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var roles = new List<Role>
            {
                new Role { RoleId = Guid.NewGuid(), RoleName = "Admin", OrganizationId = orgId, Subscription = subscription }
            };

            var expectedException = new InvalidOperationException("Test exception");

            _mockRolesRepository
                .Setup(r => r.AddAsync(roles, orgId, subscription))
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = Assert.ThrowsAsync<InvalidOperationException>(async () => 
                await _rolesCommandHandler.AddRoleAsync(roles, orgId, subscription));
            
            Assert.That(exception.Message, Is.EqualTo("Test exception"));
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Never);
        }
    }
}
