using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using MemberServiceApi.Controllers;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using MemberServiceBusinessLayer;

namespace MemberServiceApiControllerTestCases
{
    [TestFixture]
    public class UserLicenseControllerTests
    {
        private Mock<IUserLicenseQueryHandler<UserLicense>> _mockQueryHandler;
        private Mock<IUserLicenseCommandHandler<UserLicense>> _mockCommandHandler;
        private Mock<ILogger<UserLicenseController>> _mockLogger;
        private Mock<IStringLocalizer<UserLicenseController>> _mockLocalizer;
        private UserLicenseController _controller;

        [SetUp]
        public void Setup()
        {
            _mockQueryHandler = new Mock<IUserLicenseQueryHandler<UserLicense>>();
            _mockCommandHandler = new Mock<IUserLicenseCommandHandler<UserLicense>>();
            _mockLogger = new Mock<ILogger<UserLicenseController>>();
            _mockLocalizer = new Mock<IStringLocalizer<UserLicenseController>>();

            // Setup localizer to return the key when requested
            _mockLocalizer.Setup(l => l[It.IsAny<string>()]).Returns((string key) => new LocalizedString(key, key));

            _controller = new UserLicenseController(
                _mockQueryHandler.Object,
                _mockCommandHandler.Object,
                _mockLogger.Object,
                _mockLocalizer.Object
            );
        }

        [Test]
        public async Task GetAllUserLicenses_WhenUserLicensesExist_ReturnsOkWithUserLicenses()
        {
            // Arrange
            var mockUserLicenses = new List<UserLicense>
            {
                new UserLicense { Id = Guid.NewGuid(), OrganizationId = Guid.NewGuid(), PlanId = Guid.NewGuid(), Seats = 5, ActiveUsers = 2, Status = true, ExpiryDate = DateTime.Now.AddDays(30) },
                new UserLicense { Id = Guid.NewGuid(), OrganizationId = Guid.NewGuid(), PlanId = Guid.NewGuid(), Seats = 10, ActiveUsers = 5, Status = true, ExpiryDate = DateTime.Now.AddDays(60) }
            };

            _mockQueryHandler
                .Setup(q => q.GetAllUserLicensesAsync())
                .ReturnsAsync(mockUserLicenses);

            // Act
            var result = await _controller.GetAllUserLicenses();

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult.StatusCode, Is.EqualTo(200));
            Assert.That(okResult.Value, Is.EqualTo(mockUserLicenses));
        }

        [Test]
        public async Task GetAllUserLicenses_WhenNoUserLicensesExist_ReturnsNotFound()
        {
            // Arrange
            _mockQueryHandler
                .Setup(q => q.GetAllUserLicensesAsync())
                .ReturnsAsync(new List<UserLicense>());

            // Act
            var result = await _controller.GetAllUserLicenses();

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task GetAllUserLicenses_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            _mockQueryHandler
                .Setup(q => q.GetAllUserLicensesAsync())
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetAllUserLicenses();

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task GetUserLicenseById_WhenUserLicenseExists_ReturnsOkWithUserLicense()
        {
            // Arrange
            var userLicenseId = Guid.NewGuid();
            var mockUserLicense = new UserLicense
            {
                Id = userLicenseId,
                OrganizationId = Guid.NewGuid(),
                PlanId = Guid.NewGuid(),
                Seats = 5,
                ActiveUsers = 2,
                Status = true,
                ExpiryDate = DateTime.Now.AddDays(30)
            };

            _mockQueryHandler
                .Setup(q => q.GetUserLicenseByIdAsync(userLicenseId))
                .ReturnsAsync(mockUserLicense);

            // Act
            var result = await _controller.GetUserLicenseById(userLicenseId);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult.StatusCode, Is.EqualTo(200));
            Assert.That(okResult.Value, Is.EqualTo(mockUserLicense));
        }

        [Test]
        public async Task GetUserLicenseById_WhenUserLicenseDoesNotExist_ReturnsNotFound()
        {
            // Arrange
            var userLicenseId = Guid.NewGuid();

            _mockQueryHandler
                .Setup(q => q.GetUserLicenseByIdAsync(userLicenseId))
                .ReturnsAsync((UserLicense)null);

            // Act
            var result = await _controller.GetUserLicenseById(userLicenseId);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task GetUserLicenseById_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var userLicenseId = Guid.NewGuid();

            _mockQueryHandler
                .Setup(q => q.GetUserLicenseByIdAsync(userLicenseId))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetUserLicenseById(userLicenseId);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task AddUserLicense_WhenValidUserLicense_ReturnsCreatedAtAction()
        {
            // Arrange
            var userLicenseId = Guid.NewGuid();
            var userLicense = new UserLicense
            {
                Id = userLicenseId,
                OrganizationId = Guid.NewGuid(),
                PlanId = Guid.NewGuid(),
                Seats = 5,
                ActiveUsers = 2,
                Status = true,
                ExpiryDate = DateTime.Now.AddDays(30)
            };

            _mockCommandHandler
                .Setup(c => c.AddUserLicenseAsync(userLicense))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.AddUserLicense(userLicense);

            // Assert
            Assert.That(result, Is.InstanceOf<CreatedAtActionResult>());
            var createdResult = result as CreatedAtActionResult;
            Assert.That(createdResult.StatusCode, Is.EqualTo(201));
            Assert.That(createdResult.ActionName, Is.EqualTo(nameof(UserLicenseController.GetUserLicenseById)));
            Assert.That(createdResult.RouteValues["id"], Is.EqualTo(userLicenseId));
            Assert.That(createdResult.Value, Is.EqualTo(userLicense));
        }

        [Test]
        public async Task AddUserLicense_WhenNullUserLicense_ReturnsBadRequest()
        {
            // Act
            var result = await _controller.AddUserLicense(null);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult.StatusCode, Is.EqualTo(400));
        }

        [Test]
        public async Task AddUserLicense_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var userLicense = new UserLicense
            {
                Id = Guid.NewGuid(),
                OrganizationId = Guid.NewGuid(),
                PlanId = Guid.NewGuid(),
                Seats = 5,
                ActiveUsers = 2,
                Status = true,
                ExpiryDate = DateTime.Now.AddDays(30)
            };

            _mockCommandHandler
                .Setup(c => c.AddUserLicenseAsync(userLicense))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.AddUserLicense(userLicense);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task SetLicenseStatus_WhenValidData_ReturnsOkWithMessage()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            bool status = true;

            _mockCommandHandler
                .Setup(c => c.SetLicenseStatusAsync(organizationId, status))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.SetLicenseStatus(organizationId, status);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult.StatusCode, Is.EqualTo(200));
        }

        [Test]
        public async Task SetLicenseStatus_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            bool status = true;

            _mockCommandHandler
                .Setup(c => c.SetLicenseStatusAsync(organizationId, status))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.SetLicenseStatus(organizationId, status);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult.StatusCode, Is.EqualTo(500));
        }
    }
}
