﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PracticeContracts;
using PracticeDataAccessLayer.Implementation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ShardModels;
using PracticeDataAccessLayer.Context;
using Interfaces.ShardManagement;
using PracticeDataAccessLayer.PracticeDataAccessLayerResources;

namespace PracticeBusinessLayer.CommandHandler
{
    public class PracticeCommandHandler :IPracticeCommandHandler<Tasks>
    {
        private readonly IConfiguration _configuration;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<PracticeCommandHandler> _logger;
        private readonly IMigration<PracticeDatabaseContext, Tasks, PracticeDataAccessLayerStrings> _migration;


        public PracticeCommandHandler(
            IConfiguration configuration,
            IUnitOfWork unitOfWork,
            ILogger<PracticeCommandHandler> logger,
            IMigration<PracticeDatabaseContext, Tasks, PracticeDataAccessLayerStrings> migration)
        {
            _configuration = configuration;
            _unitOfWork = unitOfWork;
            _logger = logger;
            _migration = migration;
        }

        public async Task AddTasks(List<Tasks> tasks, Guid OrgId, bool Subscription)
        {
            await _unitOfWork.PracticeRepository.AddAsync(tasks, OrgId, Subscription);
            await _unitOfWork.SaveAsync();

        }

        public async Task UpdateTasks(Tasks task, Guid OrgId, bool Subscription)
        {
            await _unitOfWork.PracticeRepository.UpdateAsync(task, OrgId, Subscription);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteTasksById(Guid id, Guid OrgId, bool Subscription)
        {
            await _unitOfWork.PracticeRepository.DeleteByIdAsync(id, OrgId, Subscription);
            await _unitOfWork.SaveAsync();
        }

        public async Task DeleteTasksByEntity(Tasks task, Guid OrgId, bool Subscription)
        {
            await _unitOfWork.PracticeRepository.DeleteByEntityAsync(task, OrgId, Subscription);
            await _unitOfWork.SaveAsync();
        }

        public async Task UpdateTasksByPatientId(Tasks task, Guid OrgId, bool Subscription)
        {
            await _unitOfWork.PracticeRepository.UpdateTasksByPatientIdAsync(task, OrgId, Subscription);
            await _unitOfWork.SaveAsync();
        }

    }
}
