﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Contracts;
using DataAccessLayer.Context;
using DotNetEnv;
using Interfaces.ShardManagement;
using MemberServiceDataAccessLayer.Implementation;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;

namespace MemberServiceDataAccessLayer.Implementation
{
    public class ShardGenericRepository<T> : IShardGenericRepository<T>
        where T : class
    {
        private readonly ShardMapManagerService<AccountDatabaseContext, AccountDatabaseContext> _shardMapManagerService;
        private readonly string _shardMapName;
        private readonly IStringLocalizer<AccountDatabaseContext> _localizer;
        private readonly ILogger<AccountDatabaseContext> _logger;
        private readonly AccountDatabaseContext _context;
        private readonly DbSet<T> _dbSet;
        public ShardGenericRepository(AccountDatabaseContext context, ShardMapManagerService<AccountDatabaseContext,AccountDatabaseContext> shardMapManagerService, IStringLocalizer<AccountDatabaseContext> localizer,ILogger<AccountDatabaseContext> logger)
        {
            Env.Load();
            _shardMapManagerService = shardMapManagerService ?? throw new ArgumentNullException(nameof(shardMapManagerService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _context = context;
            _dbSet = context.Set<T>();
            _localizer = localizer;
            _shardMapName = Environment.GetEnvironmentVariable("shardMapName");
        }


        public async Task<bool> DeleteByNameAsync(string name, Guid orgId, bool subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(subscription, orgId, _shardMapName);
            if (context == null)
                return false;

            var entity = await context.VisitTypes
                .FirstOrDefaultAsync(vt => vt.OrganizationId == orgId && vt.VisitName == name);

            if (entity == null)
                return false;

            entity.IsActive = false;

            var affectedRows = await context.SaveChangesAsync();
            return affectedRows > 0;
        }

        public async Task<T> GetFirstByOrganizationIdAsync(Guid id, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, id, _shardMapName);
            if (context == null)
                return default;

            return await context.Set<T>()
                .FirstOrDefaultAsync(ul => EF.Property<Guid>(ul, "OrganizationID") == id);
        }

        // Add
        public async Task<IEnumerable<T>> GetAllAsync()
        {
            var allEntities = new List<T>();
            var allKeys = _shardMapManagerService.GetAllShardKeys(_shardMapName);
            foreach (var key in allKeys)
            {
                using (var context = _shardMapManagerService.GetDbContext(key, _shardMapName))
                {
                    var entities = await context.Set<T>().ToListAsync();
                    allEntities.AddRange(entities);
                }
            }
            return allEntities;
        }
        public async Task AddAsync(IEnumerable<T> entities, Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return;
            await context.Set<T>().AddRangeAsync(entities);
            await context.SaveChangesAsync();
        }

        // Get All (Ordered)
        public async Task<IEnumerable<T>> GetAllAsync(Guid OrgId, bool Subscription)   
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return Enumerable.Empty<T>();

            return await context.Set<T>()
                                .Where(e => EF.Property<Guid>(e, "OrganizationID") == OrgId)
                                .ToListAsync();
        }
        public async Task<IEnumerable<T>> GetByOrgIdAsync(Guid orgId, bool subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(subscription, orgId, _shardMapName);
            if (context == null) return new List<T>();

            return await context.Set<T>()
                .Where(e => EF.Property<Guid>(e, "OrganizationID") == orgId)
                .ToListAsync();
        }
       
        // Get by ID
        public async Task<T> GetByIdAsync(Guid id, Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return null;
            return await context.Set<T>().FindAsync(id);
        }

        // Update single entity
        public async Task UpdateAsync(T entity, Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return;
            context.Set<T>().Update(entity);
            await context.SaveChangesAsync();
        }

        // Update range
        public async Task UpdateRangeAsync(IEnumerable<T> entities, Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId, _shardMapName);
            if (context == null) return;
            context.Set<T>().UpdateRange(entities);
            await context.SaveChangesAsync();
        }
        // Delete by entity
        public async Task DeleteByEntityAsync(T entity, Guid OrgId, bool Subscription)
        {
            using var context = _shardMapManagerService.GetNextKey(Subscription, OrgId,_shardMapName);
            if (context == null) return;
            context.Set<T>().Remove(entity);
            await context.SaveChangesAsync();
        }
        // Delete by ID
        public async Task DeleteByIdAsync(Guid id, Guid OrgId, bool Subscription)
        {
            var entity = await GetByIdAsync(id, OrgId, Subscription);
            if (entity != null)
            {
                await DeleteByEntityAsync(entity, OrgId, Subscription);
            }
        }
    }
}