using NUnit.Framework;
using Moq;
using MemberServiceBusinessLayer.CommandHandler;
using MemberServiceDataAccessLayer.Implementation;
using Contracts;
using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace MemberServiceApiBusinessLayerTestCases.CommandHandlerTests
{
    [TestFixture]
    public class UserAccessCommandHandlerTests
    {
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<IProductUserAccessRepository> _mockProductUserAccessRepository;
        private Mock<ILogger<UserAccessCommandHandler>> _mockLogger;
        private UserAccessCommandHandler _userAccessCommandHandler;

        [SetUp]
        public void Setup()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockProductUserAccessRepository = new Mock<IProductUserAccessRepository>();
            _mockLogger = new Mock<ILogger<UserAccessCommandHandler>>();

            _mockUnitOfWork.Setup(u => u.ProductUserAccessRepository).Returns(_mockProductUserAccessRepository.Object);

            _userAccessCommandHandler = new UserAccessCommandHandler(
                _mockUnitOfWork.Object,
                _mockLogger.Object
            );
        }

        [Test]
        public async Task UpdateUserAccessAsync_ShouldUpdateAccessAndSaveChanges()
        {
            // Arrange
            var productId = Guid.NewGuid();
            var memberId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            
            var memberAccessUpdate = new MemberAccessUpdate
            {
                MemberId = memberId,
                HasAccess = true
            };

            var existingAccessRecord = new ProductUserAccess
            {
                ProductId = productId,
                MemberId = memberId,
                HasAccess = false
            };

            _mockProductUserAccessRepository
                .Setup(r => r.GetAccessRecordAsync(productId, memberId, orgId, subscription))
                .ReturnsAsync(existingAccessRecord);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            var result = await _userAccessCommandHandler.UpdateUserAccessAsync(productId, memberAccessUpdate, orgId, subscription);

            // Assert
            Assert.That(result, Is.True);
            Assert.That(existingAccessRecord.HasAccess, Is.True);
            _mockProductUserAccessRepository.Verify(r => r.GetAccessRecordAsync(productId, memberId, orgId, subscription), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public void UpdateUserAccessAsync_ShouldThrowException_WhenAccessRecordNotFound()
        {
            // Arrange
            var productId = Guid.NewGuid();
            var memberId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            
            var memberAccessUpdate = new MemberAccessUpdate
            {
                MemberId = memberId,
                HasAccess = true
            };

            _mockProductUserAccessRepository
                .Setup(r => r.GetAccessRecordAsync(productId, memberId, orgId, subscription))
                .ReturnsAsync((ProductUserAccess)null);

            // Act & Assert
            var exception = Assert.ThrowsAsync<NullReferenceException>(async () => 
                await _userAccessCommandHandler.UpdateUserAccessAsync(productId, memberAccessUpdate, orgId, subscription));
            
            _mockProductUserAccessRepository.Verify(r => r.GetAccessRecordAsync(productId, memberId, orgId, subscription), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Never);
        }
    }
}
