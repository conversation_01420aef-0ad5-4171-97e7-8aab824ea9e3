using NUnit.Framework;
using Moq;
using MemberServiceBusinessLayer.QueryHandler;
using MemberServiceDataAccessLayer.Implementation;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;

namespace MemberServiceApiBusinessLayerTestCases.QueryHandlerTests
{
    [TestFixture]
    public class PageRoleMappingQueryHandlerTests
    {
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<IPageRoleMappingRepository> _mockPageRoleMappingRepository;
        private PageRoleMappingQueryHandler _pageRoleMappingQueryHandler;

        [SetUp]
        public void Setup()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockPageRoleMappingRepository = new Mock<IPageRoleMappingRepository>();

            _mockUnitOfWork.Setup(u => u.PageRoleMappingRepository).Returns(_mockPageRoleMappingRepository.Object);

            _pageRoleMappingQueryHandler = new PageRoleMappingQueryHandler(_mockUnitOfWork.Object);
        }

        [Test]
        public async Task GetPageRoleMappingByIdAsync_ShouldReturnPageRoleMapping_WhenPageRoleMappingExists()
        {
            // Arrange
            var pageRoleMappingId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var expectedPageRoleMapping = new PageRoleMapping 
            { 
                Id = pageRoleMappingId, 
                PagePath = "/dashboard",
                RoleId = Guid.NewGuid(),
                RoleName = "Admin",
                OrganizationId = orgId,
                HasAccess = true,
                Subscription = subscription
            };

            _mockPageRoleMappingRepository
                .Setup(r => r.GetByIdAsync(pageRoleMappingId, orgId, subscription))
                .ReturnsAsync(expectedPageRoleMapping);

            // Act
            var result = await _pageRoleMappingQueryHandler.GetPageRoleMappingByIdAsync(pageRoleMappingId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(pageRoleMappingId));
            Assert.That(result.PagePath, Is.EqualTo("/dashboard"));
            Assert.That(result.RoleName, Is.EqualTo("Admin"));
            _mockPageRoleMappingRepository.Verify(r => r.GetByIdAsync(pageRoleMappingId, orgId, subscription), Times.Once);
        }

        [Test]
        public async Task GetPagesByRoleIdAsync_ShouldReturnPages_WhenPagesExist()
        {
            // Arrange
            var roleId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var pageRoleMappings = new List<PageRoleMapping>
            {
                new PageRoleMapping 
                { 
                    Id = Guid.NewGuid(), 
                    PagePath = "/dashboard",
                    RoleId = roleId,
                    RoleName = "Admin",
                    OrganizationId = orgId,
                    HasAccess = true,
                    Subscription = subscription
                },
                new PageRoleMapping 
                { 
                    Id = Guid.NewGuid(), 
                    PagePath = "/users",
                    RoleId = roleId,
                    RoleName = "Admin",
                    OrganizationId = orgId,
                    HasAccess = true,
                    Subscription = subscription
                },
                new PageRoleMapping 
                { 
                    Id = Guid.NewGuid(), 
                    PagePath = "/settings",
                    RoleId = Guid.NewGuid(), // Different role
                    RoleName = "User",
                    OrganizationId = orgId,
                    HasAccess = true,
                    Subscription = subscription
                }
            };

            _mockPageRoleMappingRepository
                .Setup(r => r.GetAllAsync(orgId, subscription))
                .ReturnsAsync(pageRoleMappings);

            // Act
            var result = await _pageRoleMappingQueryHandler.GetPagesByRoleIdAsync(roleId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result.Select(p => p.PagePath).ToList(), Contains.Item("/dashboard").And.Contains("/users"));
            _mockPageRoleMappingRepository.Verify(r => r.GetAllAsync(orgId, subscription), Times.Once);
        }

        [Test]
        public async Task GetRolesByPagePathAsync_ShouldReturnRoles_WhenRolesExist()
        {
            // Arrange
            var pagePath = "/dashboard";
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var expectedRoles = new List<string> { "Admin", "Manager" };

            _mockPageRoleMappingRepository
                .Setup(r => r.GetRolesByPagePathAsync(pagePath, orgId, subscription))
                .ReturnsAsync(expectedRoles);

            // Act
            var result = await _pageRoleMappingQueryHandler.GetRolesByPagePathAsync(pagePath, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result, Contains.Item("Admin").And.Contains("Manager"));
            _mockPageRoleMappingRepository.Verify(r => r.GetRolesByPagePathAsync(pagePath, orgId, subscription), Times.Once);
        }

        [Test]
        public void GetRolesByPagePathAsync_ShouldThrowArgumentException_WhenPagePathIsNull()
        {
            // Arrange
            string pagePath = null;
            var orgId = Guid.NewGuid();
            bool subscription = false;

            // Act & Assert
            Assert.ThrowsAsync<ArgumentException>(async () => 
                await _pageRoleMappingQueryHandler.GetRolesByPagePathAsync(pagePath, orgId, subscription));
            
            _mockPageRoleMappingRepository.Verify(
                r => r.GetRolesByPagePathAsync(It.IsAny<string>(), It.IsAny<Guid>(), It.IsAny<bool>()), 
                Times.Never);
        }

        [Test]
        public void Constructor_ShouldThrowArgumentNullException_WhenUnitOfWorkIsNull()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new PageRoleMappingQueryHandler(null));
        }
    }
}
