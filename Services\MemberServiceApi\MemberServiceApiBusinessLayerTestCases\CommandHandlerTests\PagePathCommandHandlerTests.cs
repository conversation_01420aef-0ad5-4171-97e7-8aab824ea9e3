using NUnit.Framework;
using Moq;
using MemberServiceBusinessLayer.CommandHandler;
using MemberServiceDataAccessLayer.Implementation;
using Contracts;
using System;
using System.Threading.Tasks;

namespace MemberServiceApiBusinessLayerTestCases.CommandHandlerTests
{
    [TestFixture]
    public class PagePathCommandHandlerTests
    {
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<IPagePathRepository> _mockPagePathRepository;
        private PagePathCommandHandler _pagePathCommandHandler;

        [SetUp]
        public void Setup()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockPagePathRepository = new Mock<IPagePathRepository>();

            _mockUnitOfWork.Setup(u => u.PagePathRepository).Returns(_mockPagePathRepository.Object);

            _pagePathCommandHandler = new PagePathCommandHandler(_mockUnitOfWork.Object);
        }

        [Test]
        public async Task AddPagePathAsync_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var pagePath = new PagePath 
            { 
                PageId = Guid.NewGuid(), 
                PagePathValue = "/dashboard",
                CreatedBy = Guid.NewGuid(),
                CreatedDate = DateTime.Now,
                IsActive = true
            };

            _mockPagePathRepository
                .Setup(r => r.AddAsync(pagePath))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _pagePathCommandHandler.AddPagePathAsync(pagePath);

            // Assert
            _mockPagePathRepository.Verify(r => r.AddAsync(pagePath), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdatePagePathAsync_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var pagePath = new PagePath 
            { 
                PageId = Guid.NewGuid(), 
                PagePathValue = "/dashboard",
                CreatedBy = Guid.NewGuid(),
                CreatedDate = DateTime.Now.AddDays(-1),
                UpdatedBy = Guid.NewGuid(),
                UpdatedDate = DateTime.Now,
                IsActive = true
            };

            _mockPagePathRepository
                .Setup(r => r.UpdateAsync(pagePath))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _pagePathCommandHandler.UpdatePagePathAsync(pagePath);

            // Assert
            _mockPagePathRepository.Verify(r => r.UpdateAsync(pagePath), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeletePagePathAsync_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var pagePathId = Guid.NewGuid();

            _mockPagePathRepository
                .Setup(r => r.DeleteByIdAsync(pagePathId))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _pagePathCommandHandler.DeletePagePathAsync(pagePathId);

            // Assert
            _mockPagePathRepository.Verify(r => r.DeleteByIdAsync(pagePathId), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public void AddPagePathAsync_ShouldThrowArgumentNullException_WhenPagePathIsNull()
        {
            // Act & Assert
            Assert.ThrowsAsync<ArgumentNullException>(async () => 
                await _pagePathCommandHandler.AddPagePathAsync(null));
            
            _mockPagePathRepository.Verify(r => r.AddAsync(It.IsAny<PagePath>()), Times.Never);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Never);
        }

        [Test]
        public void UpdatePagePathAsync_ShouldThrowArgumentNullException_WhenPagePathIsNull()
        {
            // Act & Assert
            Assert.ThrowsAsync<ArgumentNullException>(async () => 
                await _pagePathCommandHandler.UpdatePagePathAsync(null));
            
            _mockPagePathRepository.Verify(r => r.UpdateAsync(It.IsAny<PagePath>()), Times.Never);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Never);
        }
    }
}
