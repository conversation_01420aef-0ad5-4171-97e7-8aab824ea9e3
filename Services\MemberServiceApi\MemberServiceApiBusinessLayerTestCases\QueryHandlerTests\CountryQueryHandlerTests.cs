using NUnit.Framework;
using Moq;
using MemberServiceBusinessLayer.QueryHandler;
using MemberServiceDataAccessLayer.Implementation;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;

namespace MemberServiceApiBusinessLayerTestCases.QueryHandlerTests
{
    [TestFixture]
    public class CountryQueryHandlerTests
    {
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<ICountryRepository> _mockCountryRepository;
        private CountryQueryHandler _countryQueryHandler;

        [SetUp]
        public void Setup()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockCountryRepository = new Mock<ICountryRepository>();

            _mockUnitOfWork.Setup(u => u.CountryRepository).Returns(_mockCountryRepository.Object);

            _countryQueryHandler = new CountryQueryHandler(_mockUnitOfWork.Object);
        }

        [Test]
        public async Task GetCountryByIdAsync_ShouldReturnCountry_WhenCountryExists()
        {
            // Arrange
            var countryId = Guid.NewGuid();
            var expectedCountry = new Country 
            { 
                CountryId = countryId, 
                CountryName = "United States" 
            };

            _mockCountryRepository
                .Setup(r => r.GetByIdAsync(countryId))
                .ReturnsAsync(expectedCountry);

            // Act
            var result = await _countryQueryHandler.GetCountryByIdAsync(countryId);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.CountryId, Is.EqualTo(countryId));
            Assert.That(result.CountryName, Is.EqualTo("United States"));
            
            _mockCountryRepository.Verify(r => r.GetByIdAsync(countryId), Times.Once);
        }

        [Test]
        public async Task GetCountryByIdAsync_ShouldReturnNull_WhenCountryDoesNotExist()
        {
            // Arrange
            var countryId = Guid.NewGuid();

            _mockCountryRepository
                .Setup(r => r.GetByIdAsync(countryId))
                .ReturnsAsync((Country)null);

            // Act
            var result = await _countryQueryHandler.GetCountryByIdAsync(countryId);

            // Assert
            Assert.That(result, Is.Null);
            _mockCountryRepository.Verify(r => r.GetByIdAsync(countryId), Times.Once);
        }

        [Test]
        public async Task GetAllCountriesAsync_ShouldReturnAllCountries()
        {
            // Arrange
            var countries = new List<Country>
            {
                new Country { CountryId = Guid.NewGuid(), CountryName = "United States" },
                new Country { CountryId = Guid.NewGuid(), CountryName = "Canada" },
                new Country { CountryId = Guid.NewGuid(), CountryName = "Mexico" }
            };

            _mockCountryRepository
                .Setup(r => r.GetAllAsync())
                .ReturnsAsync(countries);

            // Act
            var result = await _countryQueryHandler.GetAllCountriesAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(3));
            Assert.That(result.Select(c => c.CountryName).ToList(), 
                Contains.Item("United States").And.Contains("Canada").And.Contains("Mexico"));
            
            _mockCountryRepository.Verify(r => r.GetAllAsync(), Times.Once);
        }

        [Test]
        public async Task GetCountriesByNameAsync_ShouldReturnMatchingCountries()
        {
            // Arrange
            var countries = new List<Country>
            {
                new Country { CountryId = Guid.NewGuid(), CountryName = "United States" },
                new Country { CountryId = Guid.NewGuid(), CountryName = "United Kingdom" },
                new Country { CountryId = Guid.NewGuid(), CountryName = "Canada" }
            };

            _mockCountryRepository
                .Setup(r => r.GetAllAsync())
                .ReturnsAsync(countries);

            // Act
            var result = await _countryQueryHandler.GetCountriesByNameAsync("United");

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result.Select(c => c.CountryName).ToList(), 
                Contains.Item("United States").And.Contains("United Kingdom"));
            
            _mockCountryRepository.Verify(r => r.GetAllAsync(), Times.Once);
        }

        [Test]
        public void Constructor_ShouldThrowArgumentNullException_WhenUnitOfWorkIsNull()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new CountryQueryHandler(null));
        }
    }
}
