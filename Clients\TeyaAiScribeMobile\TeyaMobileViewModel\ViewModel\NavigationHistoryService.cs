﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Routing;
using System.Collections.Generic;

namespace TeyaMobileViewModel.ViewModel
{
    public class NavigationHistoryService : INavigationHistoryService
    {
        private readonly Stack<string> _navigationStack = new();
        private readonly object _lock = new();

        public bool CanGoBack
        {
            get
            {
                lock (_lock)
                {
                    return _navigationStack.Count > 0;
                }
            }
        }

        public event Action? NavigationChanged;

        public void PushPage(string url)
        {
            if (string.IsNullOrWhiteSpace(url)) return;

            lock (_lock)
            {
                _navigationStack.Push(url);
            }

            NavigationChanged?.Invoke();
        }

        public string? PopPage()
        {
            lock (_lock)
            {
                if (_navigationStack.Count > 0)
                {
                    var previousPage = _navigationStack.Pop();
                    NavigationChanged?.Invoke();
                    return previousPage;
                }
            }

            return null;
        }

        public void Clear()
        {
            lock (_lock)
            {
                _navigationStack.Clear();
            }

            NavigationChanged?.Invoke();
        }
    }

}
