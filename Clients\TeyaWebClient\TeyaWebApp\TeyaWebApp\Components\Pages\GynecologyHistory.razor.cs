﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.RichTextEditor;
using System.Linq;
using TeyaUIModels.Model;
using System.Collections.Generic;
using System;
using System.Threading.Tasks;
using TeyaUIViewModels.ViewModel;
using TeyaUIModels.ViewModel;
using TeyaWebApp.Services;
using Microsoft.Azure.Amqp.Framing;

namespace TeyaWebApp.Components.Pages
{
    public partial class GynecologyHistory
    {
        [Inject]
        private ILogger<GynHistoryDTO> Logger { get; set; }
        [Inject] private PatientService PatientService { get; set; }
        [Inject] private ActiveUser User { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        private Guid activeUserOrganizationId { get; set; }
        private bool Subscription = false;

        private SfRichTextEditor richTextEditor;
        private MudDialog showBrowsePopup;
        private SfGrid<GynHistoryDTO> GynHistoryGrid;
        private string richTextContent = string.Empty;
        private string symptoms = string.Empty;
        private string notes = string.Empty;
        [Inject] private IDialogService DialogService { get; set; }
        private List<GynHistoryDTO> gynHistories = new();
        private List<GynHistoryDTO> addedGynHistories = new();
        private List<GynHistoryDTO> updatedGynHistories = new();
        private List<GynHistoryDTO> deletedGynHistories = new();
        private bool add = false;
        private Guid PatientId { get; set; }
        private Guid? OrgID { get; set; }

        private bool isInternalUpdate { get; set; } = false;
        [Parameter] public Guid PatientID { get; set; }
        [Parameter] public Guid OrgId { get; set; }
        [Parameter] public string? Data { get; set; }
        [Parameter] public string? TotalText { get; set; }
        [Parameter] public EventCallback<string> OnValueChanged { get; set; }
        protected override async Task OnInitializedAsync()
        {
            try
            {
                PatientId = PatientID;
                richTextContent = TotalText;
                OrgID = OrgId;
                 var activeUserLicense = await UserLicenseService.GetUserLicenseByOrganizationIdAsync(OrgId);
                var planType = await PlanTypeService.GetPlanTypeByIdAsync(activeUserLicense.PlanId);
                Subscription = planType.PlanName == Localizer["Enterprise"];
               
                await LoadGynHistoriesAsync();
                
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex,Localizer["InitializationError"]);
            }
        }

        private List<ToolbarItemModel> GetToolbarItems() => new()
        {
            new() { Command = ToolbarCommand.Bold },
            new() { Command = ToolbarCommand.Italic },
            new() { Command = ToolbarCommand.Underline },
            new() { Command = ToolbarCommand.FontName },
            new() { Command = ToolbarCommand.FontSize },
            new() { Command = ToolbarCommand.OrderedList },
            new() { Command = ToolbarCommand.UnorderedList },
            new() { Command = ToolbarCommand.Undo },
            new() { Command = ToolbarCommand.Redo },
            new() { Name = "add", TooltipText = "Insert Symbol" }
        };


        private async Task OnActionBeginHandler(ActionEventArgs<GynHistoryDTO> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                // Show confirmation dialog
                bool? result = await DialogService.ShowMessageBox(
                    Localizer["Confirm Delete"],
                    Localizer["Do you want to delete this entry?"],
                    yesText: Localizer["Yes"],
                    noText: Localizer["No"]);

                // Cancel deletion if user says no
                if (result != true)
                {
                    args.Cancel = true;
                }
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                // Check for duplicates in existing and added entries
                bool isDuplicate = gynHistories.Concat(addedGynHistories)
                    .Any(h =>
                        h.Symptoms?.Trim().Equals(args.Data.Symptoms?.Trim(), StringComparison.OrdinalIgnoreCase) == true &&
                        h.Notes?.Trim().Equals(args.Data.Notes?.Trim(), StringComparison.OrdinalIgnoreCase) == true &&
                       
                        h.gynId != args.Data.gynId // ignore self
                    );

                if (isDuplicate)
                {
                    Snackbar.Add(Localizer["Duplicate entry not allowed."], Severity.Error);
                    if (GynHistoryGrid != null)
                        await GynHistoryGrid.CloseEditAsync();
                    //CloseBrowsePopup();
                    args.Cancel = true;
                }

                var today= DateTime.Now.Date;
                if (args.Data.DateOfHistory > today)
                {
                    Snackbar.Add(Localizer["Validate.CreateDate"], Severity.Error);
                    args.Cancel = true;
                }

            }


        }
        public async Task ActionCompletedHandler(ActionEventArgs<GynHistoryDTO> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                deletedGynHistories.Add(args.Data);
                args.Data.IsDeleted = true;
                
                
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Add)
            {

                args.Data.gynId = Guid.NewGuid();
                args.Data.PatientId = PatientId;
                args.Data.OrganizationId = OrgId;
                args.Data.PcpId = Guid.Parse(User.id);
                args.Data.DateOfHistory = DateTime.Now;
                add = true;
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                if (args.Data.DateOfHistory > DateTime.Now)
                {
                    Snackbar.Add(Localizer["Future dates are not allowed."], Severity.Error);
                    if (GynHistoryGrid != null)
                        await GynHistoryGrid.CloseEditAsync();
                    CloseBrowsePopup();
                }

              




                if (add)
                {
                    addedGynHistories.Add(args.Data);
                    add = false;
                }
                else if (!updatedGynHistories.Contains(args.Data))
                {
                    updatedGynHistories.Add(args.Data);
                }
            }
        }


        private async Task SaveChanges()
        {
            try
            {
                // Handle new entries
                if (addedGynHistories.Any())
                {
                    foreach (var newEntry in addedGynHistories)
                    {
                        await GynHistoryService.AddAsync(newEntry, OrgID, Subscription);

                    }
                    addedGynHistories.Clear();
                }

                // Handle updates
                if (updatedGynHistories.Any())
                {
                    await GynHistoryService.UpdateGynHistoryListAsync(updatedGynHistories, OrgID, Subscription);
                    updatedGynHistories.Clear();
                }

                // Handle deletes
                if (deletedGynHistories.Any())
                {
                    await GynHistoryService.UpdateGynHistoryListAsync(deletedGynHistories, OrgID, Subscription);
                    deletedGynHistories.Clear();
                }

                await LoadGynHistoriesAsync();
                richTextContent = GenerateRichTextContent(Data);
                await HandleDynamicComponentUpdate();
                await richTextEditor.RefreshUIAsync();
                CloseBrowsePopup();
                Snackbar.Add(Localizer["RecordSaved"], Severity.Success);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["SaveError"]);
                Snackbar.Add(Localizer["SaveError"], Severity.Error);
            }
        }

        private async Task CancelChanges()
        {
            addedGynHistories.Clear();
            updatedGynHistories.Clear();
            deletedGynHistories.Clear();
            await LoadGynHistoriesAsync();
            CloseBrowsePopup();
            Snackbar.Add(Localizer["ChangesCancelled"], Severity.Info);
        }

        private async Task LoadGynHistoriesAsync()
        {
            try
            {
                gynHistories = await GynHistoryService.LoadGynHistoriesAsync(PatientId, OrgID, Subscription);
                gynHistories = gynHistories.OrderByDescending(h => h.DateOfHistory).ToList();



                if (GynHistoryGrid != null)
                    await GynHistoryGrid.Refresh();

                if (richTextEditor != null)
                    await richTextEditor.RefreshUIAsync();

                await InvokeAsync(StateHasChanged);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorLoading"]);
            }
        }

        private string GenerateRichTextContentt() => string.Join(" ",
            gynHistories.OrderByDescending(o => o.DateOfHistory)
                .Select(o => $"<ul><li style='margin-left: 20px;'><b>{o.DateOfHistory:yyyy-MM-dd}</b> : {o.Symptoms} - {o.Notes}</li></ul>"));

       

        private async Task HandleBackdropClick()
        {
            Snackbar.Add(Localizer["BackdropDisabledMessage"], Severity.Info);
        }

        private void CloseBrowsePopup()
        {
            symptoms = string.Empty;
            notes = string.Empty;
            showBrowsePopup?.CloseAsync();
        }

        private async Task OpenBrowsePopup() => await showBrowsePopup?.ShowAsync();




        private string GenerateRichTextContent(string manualData)
        {
            manualData ??= string.Empty;

            // Manual section
            string manualSection = $@"<h4 style='margin-top: 20px; margin-bottom: 10px;'>Manual Content</h4>
    {manualData}";

            // Dynamic Gyn History
            string dynamicContent = (gynHistories != null && gynHistories.Any())
                ? string.Join(" ",
                    gynHistories
                        .OrderByDescending(o => o.DateOfHistory)
                        .Select(o =>
                            $"<ul><li style='margin-left: 20px;'><b>{o.DateOfHistory:yyyy-MM-dd}</b>: {o.Symptoms} <strong>-</strong> {o.Notes}</li></ul>"))
                : "<ul><li style='margin-left: 20px;'>No gynecological history available.</li></ul>";

            return $@"<div>
        {manualSection}
        <h4 style='margin-bottom: 10px;'>Dynamic Content</h4>
        {dynamicContent}
    </div>";
        }



        private async Task HandelRichTextChange(string value)
        {
            if (isInternalUpdate)
            {
                // Reset flag here instead of in finally block
                isInternalUpdate = false;
                return;
            }
            richTextContent = value;
            //if (richTextEditor != null)
            //{
            //    await richTextEditor.RefreshUIAsync();
            //}
            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(value);
            }
            await InvokeAsync(StateHasChanged);
        }

        private async Task HandleDynamicComponentUpdate()
        {
            isInternalUpdate = true;
            richTextContent = GenerateRichTextContent(Data);
            await InvokeAsync(StateHasChanged);

            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(richTextContent);
            }

        }
    }
}
