﻿using Contracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MemberServiceDataAccessLayer.Implementation
{
    public interface IMemberRepository : IShardGenericRepository<Member>
    {
        Task<List<string>> GetExistingEmailsAsync(List<string?> newEmails, Guid orgId,bool Subsciption);
        Task<IEnumerable<Office_visit_members>> GetPatientsByIdsAsync(List<Guid> patientIds,Guid orgId, bool Subscription);
        Task<IEnumerable<ProviderPatient>> GetProviderPatientByOrganizationId(Guid organizationId, bool Subscription);
        Task<IEnumerable<Member>> SearchMembersByEmailAsync(string searchTerm, Guid orgId, bool Subscription);
        Task<IEnumerable<ProviderPatient>> GetPatientsByOrganizationId(Guid organizationId, bool Subscription);
        Task<IEnumerable<Member>> SearchMembersAsync(string searchTerm, Guid orgID, bool subscription);
    }
}
