using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using MemberServiceApi.Controllers;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using MemberServiceBusinessLayer;

namespace MemberServiceApiControllerTestCases
{
    [TestFixture]
    public class RolesControllerTests
    {
        private Mock<IRolesQueryHandler<Role>> _mockQueryHandler;
        private Mock<IRolesCommandHandler<Role>> _mockCommandHandler;
        private Mock<ILogger<RolesController>> _mockLogger;
        private Mock<IStringLocalizer<RolesController>> _mockLocalizer;
        private RolesController _controller;

        [SetUp]
        public void Setup()
        {
            _mockQueryHandler = new Mock<IRolesQueryHandler<Role>>();
            _mockCommandHandler = new Mock<IRolesCommandHandler<Role>>();
            _mockLogger = new Mock<ILogger<RolesController>>();
            _mockLocalizer = new Mock<IStringLocalizer<RolesController>>();

            // Setup localizer to return the key when requested
            _mockLocalizer.Setup(l => l[It.IsAny<string>()]).Returns((string key) => new LocalizedString(key, key));

            _controller = new RolesController(
                _mockQueryHandler.Object,
                _mockCommandHandler.Object,
                _mockLogger.Object,
                _mockLocalizer.Object
            );
        }

        [Test]
        public async Task GetAllRoles_WhenRolesExist_ReturnsOkWithRoles()
        {
            // Arrange
            var mockRoles = new List<Role>
            {
                new Role { RoleId = Guid.NewGuid(), RoleName = "Admin" },
                new Role { RoleId = Guid.NewGuid(), RoleName = "User" }
            };

            _mockQueryHandler
                .Setup(q => q.GetAllRolesAsync())
                .ReturnsAsync(mockRoles);

            // Act
            var result = await _controller.GetAllRoles();

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult.StatusCode, Is.EqualTo(200));
            Assert.That(okResult.Value, Is.EqualTo(mockRoles));
        }

        [Test]
        public async Task GetAllRoles_WhenNoRolesExist_ReturnsNotFound()
        {
            // Arrange
            _mockQueryHandler
                .Setup(q => q.GetAllRolesAsync())
                .ReturnsAsync(new List<Role>());

            // Act
            var result = await _controller.GetAllRoles();

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task GetAllRoles_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            _mockQueryHandler
                .Setup(q => q.GetAllRolesAsync())
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetAllRoles();

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task GetRoleById_WhenRoleExists_ReturnsOkWithRole()
        {
            // Arrange
            var roleId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var mockRole = new Role
            {
                RoleId = roleId,
                RoleName = "Admin",
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockQueryHandler
                .Setup(q => q.GetRoleByIdAsync(roleId, orgId, subscription))
                .ReturnsAsync(mockRole);

            // Act
            var result = await _controller.GetRoleById(roleId, orgId, subscription);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult.StatusCode, Is.EqualTo(200));
            Assert.That(okResult.Value, Is.EqualTo(mockRole));
        }

        [Test]
        public async Task GetRoleById_WhenRoleDoesNotExist_ReturnsNotFound()
        {
            // Arrange
            var roleId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockQueryHandler
                .Setup(q => q.GetRoleByIdAsync(roleId, orgId, subscription))
                .ReturnsAsync((Role)null);

            // Act
            var result = await _controller.GetRoleById(roleId, orgId, subscription);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task GetRoleById_WhenExceptionOccurs_ThrowsException()
        {
            // Arrange
            var roleId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockQueryHandler
                .Setup(q => q.GetRoleByIdAsync(roleId, orgId, subscription))
                .ThrowsAsync(new Exception("Test exception"));

            // Act & Assert
            try
            {
                await _controller.GetRoleById(roleId, orgId, subscription);
                Assert.Fail("Expected exception was not thrown");
            }
            catch (Exception ex)
            {
                Assert.That(ex.Message, Is.EqualTo("Test exception"));
            }
        }

        [Test]
        public async Task AddRole_WhenValidRole_ReturnsCreatedAtAction()
        {
            // Arrange
            var roleId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var role = new Role
            {
                RoleId = roleId,
                RoleName = "New Role",
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockCommandHandler
                .Setup(c => c.AddRoleAsync(It.IsAny<List<Role>>(), orgId, subscription))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.AddRole(role);

            // Assert
            Assert.That(result, Is.InstanceOf<CreatedAtActionResult>());
            var createdResult = result as CreatedAtActionResult;
            Assert.That(createdResult.StatusCode, Is.EqualTo(201));
            Assert.That(createdResult.ActionName, Is.EqualTo(nameof(RolesController.GetRoleById)));
            Assert.That(createdResult.RouteValues["id"], Is.EqualTo(roleId));
            Assert.That(createdResult.Value, Is.EqualTo(role));
        }

        [Test]
        public async Task AddRole_WhenNullRole_ReturnsBadRequest()
        {
            // Act
            var result = await _controller.AddRole(null);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult.StatusCode, Is.EqualTo(400));
        }

        [Test]
        public async Task AddRole_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var role = new Role
            {
                RoleId = Guid.NewGuid(),
                RoleName = "New Role"
            };

            _mockCommandHandler
                .Setup(c => c.AddRoleAsync(It.IsAny<List<Role>>(), It.IsAny<Guid>(), It.IsAny<bool>()))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.AddRole(role);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task DeleteRole_WhenRoleExists_ReturnsNoContent()
        {
            // Arrange
            var roleId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockCommandHandler
                .Setup(c => c.DeleteRoleAsync(roleId, orgId, subscription))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.DeleteRole(roleId, orgId, subscription);

            // Assert
            Assert.That(result, Is.InstanceOf<NoContentResult>());
            var noContentResult = result as NoContentResult;
            Assert.That(noContentResult.StatusCode, Is.EqualTo(204));
        }

        [Test]
        public async Task DeleteRole_WhenRoleDoesNotExist_ReturnsNotFound()
        {
            // Arrange
            var roleId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockCommandHandler
                .Setup(c => c.DeleteRoleAsync(roleId, orgId, subscription))
                .ThrowsAsync(new KeyNotFoundException());

            // Act
            var result = await _controller.DeleteRole(roleId, orgId, subscription);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task DeleteRole_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var roleId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockCommandHandler
                .Setup(c => c.DeleteRoleAsync(roleId, orgId, subscription))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.DeleteRole(roleId, orgId, subscription);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult.StatusCode, Is.EqualTo(500));
        }
    }
}
