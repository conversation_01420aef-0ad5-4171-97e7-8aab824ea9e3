# Automated Versioning System

This document explains how the automated versioning system works in the TeyaHealth repository.

## Overview

The versioning system has been redesigned to work with branch protection rules and only version projects when actual changes are made. It creates pull requests instead of pushing directly to the main branch.

## How It Works

### Automatic Versioning (build.yml)

The automatic versioning runs as part of the main build workflow and:

1. **Only triggers on main branch pushes** (not on pull requests)
2. **Detects changes** in project-related files (.csproj, .cs, .razor, .js, .css, .html)
3. **Identifies affected projects** by finding .csproj files in directories with changes
4. **Creates a properly named branch** following the convention: `DEV-{timestamp}-version-bump`
5. **Creates a pull request** with detailed information about the changes

#### Trigger Conditions:
- ✅ Push to main branch
- ✅ Build and tests pass
- ✅ Project-related files were changed
- ❌ Pull requests (skipped)
- ❌ No relevant file changes (skipped)

### Manual Versioning (manual-versioning.yml)

A separate workflow for manual versioning control with options:

#### Version Types:
- **auto**: `YYYY.MM.DD.HHMM` (default)
- **major**: `YYYY.0.0`
- **minor**: `YYYY.MM.0`
- **patch**: `YYYY.MM.DD`
- **custom**: User-specified version

#### Options:
- **Target Projects**: Specify which projects to version (comma-separated)
- **Custom Version**: Override automatic version generation

## Branch Naming Convention

All versioning branches follow your established naming convention:
```
DEV-{timestamp}-version-bump
DEV-{timestamp}-manual-version-bump
```

Where `{timestamp}` is in format `YYYYMMDDHHMM` (e.g., `202412151430`)

## Version Format

Versions are added to .csproj files in the format:
```xml
<PropertyGroup>
  <Version>v2024.12.15.1430</Version>
</PropertyGroup>
```

## Usage

### Automatic Versioning
1. Make changes to your code
2. Create a pull request to main
3. After PR is merged, versioning automatically runs
4. Review and merge the versioning PR

### Manual Versioning
1. Go to Actions → Manual Versioning
2. Click "Run workflow"
3. Choose your options:
   - Version type (auto/major/minor/patch)
   - Target projects (optional)
   - Custom version (optional)
4. Review and merge the created PR

## Examples

### Automatic Versioning Scenarios

**Scenario 1: Service Update**
```
Changed files: Services/MemberServiceApi/Controllers/UserController.cs
Result: Only MemberServiceApi.csproj gets versioned
```

**Scenario 2: Frontend Update**
```
Changed files: Clients/TeyaWebApp/Components/Dashboard.razor
Result: Only TeyaWebApp.csproj gets versioned
```

**Scenario 3: Documentation Update**
```
Changed files: README.md, docs/api.md
Result: No versioning (no project files changed)
```

### Manual Versioning Examples

**Example 1: Version All Projects**
```
Version Type: auto
Target Projects: (empty)
Custom Version: (empty)
Result: All main projects get version v2024.12.15.1430
```

**Example 2: Version Specific Projects**
```
Version Type: auto
Target Projects: Services/MemberServiceApi/MemberServiceApi.csproj,Services/AppointmentsApi/Appointments.csproj
Custom Version: (empty)
Result: Only specified projects get versioned
```

**Example 3: Custom Version**
```
Version Type: auto
Target Projects: (empty)
Custom Version: 2.1.0
Result: All projects get version 2.1.0
```

## Benefits

1. **Respects Branch Protection**: Creates PRs instead of direct pushes
2. **Smart Detection**: Only versions when actual changes are made
3. **Follows Conventions**: Uses proper branch naming format
4. **Granular Control**: Versions only affected projects
5. **Manual Override**: Allows manual versioning when needed
6. **Audit Trail**: Clear PR history of all version changes
7. **Team Notifications**: MS Teams integration for visibility

## Troubleshooting

### Versioning Not Running
- Check if changes were made to project-related files
- Verify build and tests passed
- Ensure push was to main branch (not PR)

### Branch Name Rejected
- The system automatically follows your naming convention
- If issues persist, check branch protection rules

### No Projects Detected
- Verify .csproj files exist in expected locations
- Check file paths in the workflow logs
- Use manual versioning to specify exact projects

## Configuration

### Environment Variables Required:
- `GITHUB_TOKEN`: For creating PRs (automatically provided)
- `TEAMS_WEBHOOK_URL`: For MS Teams notifications (secret)

### Customization:
- Modify file extensions in change detection
- Adjust project discovery patterns
- Change version format in workflow files
