using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Configuration;
using MemberServiceBusinessLayer.QueryHandler;
using MemberServiceDataAccessLayer.Implementation;
using MemberServiceDataAccessLayer;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;

namespace MemberServiceApiBusinessLayerTestCases.QueryHandlerTests
{
    [TestFixture]
    public class VisitStatusQueryHandlerTests
    {
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<IVisitStatusRepository> _mockVisitStatusRepository;
        private VisitStatusQueryHandler _visitStatusQueryHandler;

        [SetUp]
        public void Setup()
        {
            _mockConfiguration = new Mock<IConfiguration>();
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockVisitStatusRepository = new Mock<IVisitStatusRepository>();

            _mockUnitOfWork.Setup(u => u.VisitStatusRepository).Returns(_mockVisitStatusRepository.Object);

            _visitStatusQueryHandler = new VisitStatusQueryHandler(_mockConfiguration.Object, _mockUnitOfWork.Object);
        }

        [Test]
        public async Task GetAllVisitStatusAsync_ShouldReturnAllVisitStatuses()
        {
            // Arrange
            var expectedVisitStatuses = new List<VisitStatus>
            {
                new VisitStatus
                {
                    ID = Guid.NewGuid(),
                    Visitstatus = "Scheduled"
                },
                new VisitStatus
                {
                    ID = Guid.NewGuid(),
                    Visitstatus = "Completed"
                },
                new VisitStatus
                {
                    ID = Guid.NewGuid(),
                    Visitstatus = "Cancelled"
                }
            };

            _mockVisitStatusRepository
                .Setup(r => r.GetAllAsync())
                .ReturnsAsync(expectedVisitStatuses);

            // Act
            var result = await _visitStatusQueryHandler.GetAllVisitStatusAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(3));
            Assert.That(result, Is.EquivalentTo(expectedVisitStatuses));
            _mockVisitStatusRepository.Verify(r => r.GetAllAsync(), Times.Once);
        }
    }
}
