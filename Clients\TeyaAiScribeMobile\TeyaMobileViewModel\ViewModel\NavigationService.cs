﻿using Microsoft.AspNetCore.Components;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TeyaMobileViewModel.ViewModel
{
    public class NavigationService : INavigationService
    {
        private readonly INavigationHistoryService _historyService;
        private NavigationManager? _navigationManager;
        private bool _isInitialized = false;

        public NavigationService(INavigationHistoryService historyService)
        {
            _historyService = historyService;
            _historyService.NavigationChanged += () => NavigationStateChanged?.Invoke();
        }

        public void Initialize(NavigationManager navigationManager)
        {
            if (!_isInitialized)
            {
                _navigationManager = navigationManager;
                _isInitialized = true;
            }
        }

        public bool CanGoBack => _isInitialized && _historyService.CanGoBack;
        public event Action? NavigationStateChanged;

        public async Task NavigateToAsync(string url, bool addToHistory = true)
        {
            if (_navigationManager == null || !_isInitialized) return;

            if (addToHistory)
            {
                var currentUri = _navigationManager.Uri;
                var currentRelativeUri = _navigationManager.ToBaseRelativePath(currentUri);

                if (!string.Equals(currentRelativeUri, url.TrimStart('/'), StringComparison.OrdinalIgnoreCase))
                {
                    _historyService.PushPage(currentRelativeUri);
                }
            }

            _navigationManager.NavigateTo(url);
            await Task.CompletedTask;
        }

        public async Task GoBackAsync()
        {
            if (_navigationManager == null || !_isInitialized) return;

            var previousPage = _historyService.PopPage();
            if (!string.IsNullOrEmpty(previousPage))
            {
                _navigationManager.NavigateTo(previousPage, false);
            }

            await Task.CompletedTask;
        }
    }
}
