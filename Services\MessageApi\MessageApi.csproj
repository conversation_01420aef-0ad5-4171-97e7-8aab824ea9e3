<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Contracts\**" />
    <Compile Remove="MessageDataAccessLayer\**" />
    <Content Remove="Contracts\**" />
    <Content Remove="MessageDataAccessLayer\**" />
    <EmbeddedResource Remove="Contracts\**" />
    <EmbeddedResource Remove="MessageDataAccessLayer\**" />
    <None Remove="Contracts\**" />
    <None Remove="MessageDataAccessLayer\**" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Controllers\" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="DotNetEnv" Version="3.1.1" />
    <PackageReference Include="Microsoft.EntityFramework.SqlServer" Version="6.5.1" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.4" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.4">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.4">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Identity.Web" Version="3.8.4" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="8.1.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="Contracts\MessageContracts.csproj" />
    <ProjectReference Include="MessageBusinessLayer\MessageBusinessLayer.csproj" />
    <ProjectReference Include="MessageDataAccessLayer\MessageDataAccessLayer.csproj" />
  </ItemGroup>

</Project>
