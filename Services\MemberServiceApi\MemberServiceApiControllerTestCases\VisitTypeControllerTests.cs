using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using MemberServiceApi.Controllers;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using MemberServiceBusinessLayer;
using System.Linq;

namespace MemberServiceApiControllerTestCases
{
    [TestFixture]
    public class VisitTypeControllerTests
    {
        private Mock<IVisitTypeQueryHandler<VisitType>> _mockQueryHandler;
        private Mock<IVisitTypeCommandHandler<VisitType>> _mockCommandHandler;
        private Mock<ILogger<VisitTypeController>> _mockLogger;
        private Mock<IStringLocalizer<VisitTypeController>> _mockLocalizer;
        private VisitTypeController _controller;

        [SetUp]
        public void Setup()
        {
            _mockQueryHandler = new Mock<IVisitTypeQueryHandler<VisitType>>();
            _mockCommandHandler = new Mock<IVisitTypeCommandHandler<VisitType>>();
            _mockLogger = new Mock<ILogger<VisitTypeController>>();
            _mockLocalizer = new Mock<IStringLocalizer<VisitTypeController>>();

            // Setup localizer to return the key when requested
            _mockLocalizer.Setup(l => l[It.IsAny<string>()]).Returns((string key) => new LocalizedString(key, key));

            _controller = new VisitTypeController(
                _mockQueryHandler.Object,
                _mockCommandHandler.Object,
                _mockLogger.Object,
                _mockLocalizer.Object
            );
        }

        [Test]
        public async Task Get_WhenVisitTypesExist_ReturnsOkWithVisitTypes()
        {
            // Arrange
            var mockVisitTypes = new List<VisitType>
            {
                new VisitType
                {
                    ID = Guid.NewGuid(),
                    VisitName = "Initial Consultation",
                    CPTCode = "99201",
                    OrganizationId = Guid.NewGuid(),
                    Subscription = false,
                    IsActive = true
                },
                new VisitType
                {
                    ID = Guid.NewGuid(),
                    VisitName = "Follow-up Visit",
                    CPTCode = "99211",
                    OrganizationId = Guid.NewGuid(),
                    Subscription = true,
                    IsActive = true
                }
            };

            _mockQueryHandler
                .Setup(q => q.GetAllVisitTypesAsync())
                .ReturnsAsync(mockVisitTypes);

            // Act
            var result = await _controller.Get();

            // Assert
            Assert.That(result.Result, Is.InstanceOf<OkObjectResult>());
            var okResult = result.Result as OkObjectResult;
            Assert.That(okResult?.StatusCode, Is.EqualTo(200));
            Assert.That(okResult?.Value, Is.EqualTo(mockVisitTypes));
        }

        [Test]
        public async Task Get_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            _mockQueryHandler
                .Setup(q => q.GetAllVisitTypesAsync())
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.Get();

            // Assert
            Assert.That(result.Result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result.Result as ObjectResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task GetByOrganizationId_WhenVisitTypesExist_ReturnsOkWithVisitTypes()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var mockVisitTypes = new List<VisitType>
            {
                new VisitType
                {
                    ID = Guid.NewGuid(),
                    VisitName = "Initial Consultation",
                    CPTCode = "99201",
                    OrganizationId = orgId,
                    Subscription = subscription,
                    IsActive = true
                },
                new VisitType
                {
                    ID = Guid.NewGuid(),
                    VisitName = "Follow-up Visit",
                    CPTCode = "99211",
                    OrganizationId = orgId,
                    Subscription = subscription,
                    IsActive = true
                }
            };

            _mockQueryHandler
                .Setup(q => q.GetByOrganizationIdAsync(orgId, subscription))
                .ReturnsAsync(mockVisitTypes);

            // Act
            var result = await _controller.GetByOrganizationId(orgId, subscription);

            // Assert
            Assert.That(result.Result, Is.InstanceOf<OkObjectResult>());
            var okResult = result.Result as OkObjectResult;
            Assert.That(okResult?.StatusCode, Is.EqualTo(200));
            Assert.That(okResult?.Value, Is.EqualTo(mockVisitTypes));
        }

        [Test]
        public async Task GetByOrganizationId_WhenNoVisitTypesExist_ReturnsNotFound()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockQueryHandler
                .Setup(q => q.GetByOrganizationIdAsync(orgId, subscription))
                .ReturnsAsync((IEnumerable<VisitType>)null);

            // Act
            var result = await _controller.GetByOrganizationId(orgId, subscription);

            // Assert
            Assert.That(result.Result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result.Result as NotFoundObjectResult;
            Assert.That(notFoundResult?.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task GetByOrganizationId_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockQueryHandler
                .Setup(q => q.GetByOrganizationIdAsync(orgId, subscription))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetByOrganizationId(orgId, subscription);

            // Assert
            Assert.That(result.Result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result.Result as ObjectResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task AddVisitType_WhenValidVisitType_ReturnsCreatedAtAction()
        {
            // Arrange
            var visitTypeId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var visitType = new VisitType
            {
                ID = visitTypeId,
                VisitName = "New Visit Type",
                CPTCode = "99999",
                OrganizationId = orgId,
                Subscription = subscription,
                IsActive = true
            };

            _mockCommandHandler
                .Setup(c => c.AddVisitTypeAsync(visitType, orgId, subscription))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.AddVisitType(visitType);

            // Assert
            Assert.That(result, Is.InstanceOf<CreatedAtActionResult>());
            var createdResult = result as CreatedAtActionResult;
            Assert.That(createdResult?.StatusCode, Is.EqualTo(201));
            Assert.That(createdResult?.ActionName, Is.EqualTo(nameof(VisitTypeController.GetByOrganizationId)));
            Assert.That(createdResult?.RouteValues["orgId"], Is.EqualTo(orgId));
            Assert.That(createdResult?.RouteValues["Subscription"], Is.EqualTo(subscription));
            Assert.That(createdResult?.Value, Is.EqualTo(visitType));
        }

        [Test]
        public async Task AddVisitType_WhenNullVisitType_ReturnsBadRequest()
        {
            // Act
            var result = await _controller.AddVisitType(null);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult?.StatusCode, Is.EqualTo(400));
        }

        [Test]
        public async Task AddVisitType_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var visitTypeId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var visitType = new VisitType
            {
                ID = visitTypeId,
                VisitName = "New Visit Type",
                CPTCode = "99999",
                OrganizationId = orgId,
                Subscription = subscription,
                IsActive = true
            };

            _mockCommandHandler
                .Setup(c => c.AddVisitTypeAsync(visitType, orgId, subscription))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.AddVisitType(visitType);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task UpdateCptCode_WhenVisitTypeExists_ReturnsOk()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var visitName = "Initial Consultation";
            var newCptCode = "99202";
            bool subscription = false;

            _mockCommandHandler
                .Setup(c => c.UpdateCptCodeAsync(orgId, visitName, newCptCode, subscription))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.UpdateCptCode(orgId, visitName, newCptCode, subscription);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult?.StatusCode, Is.EqualTo(200));
        }

        [Test]
        public async Task UpdateCptCode_WhenVisitTypeDoesNotExist_ReturnsNotFound()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var visitName = "Nonexistent Visit";
            var newCptCode = "99202";
            bool subscription = false;

            _mockCommandHandler
                .Setup(c => c.UpdateCptCodeAsync(orgId, visitName, newCptCode, subscription))
                .ReturnsAsync(false);

            // Act
            var result = await _controller.UpdateCptCode(orgId, visitName, newCptCode, subscription);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult?.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task UpdateCptCode_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var visitName = "Initial Consultation";
            var newCptCode = "99202";
            bool subscription = false;

            _mockCommandHandler
                .Setup(c => c.UpdateCptCodeAsync(orgId, visitName, newCptCode, subscription))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.UpdateCptCode(orgId, visitName, newCptCode, subscription);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task DeleteVisitType_WhenVisitTypeExists_ReturnsOk()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var visitName = "Initial Consultation";
            bool subscription = false;

            _mockCommandHandler
                .Setup(c => c.DeleteVisitTypeAsync(orgId, visitName, subscription))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.DeleteVisitType(orgId, visitName, subscription);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult?.StatusCode, Is.EqualTo(200));
        }

        [Test]
        public async Task DeleteVisitType_WhenVisitTypeDoesNotExist_ReturnsNotFound()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var visitName = "Nonexistent Visit";
            bool subscription = false;

            _mockCommandHandler
                .Setup(c => c.DeleteVisitTypeAsync(orgId, visitName, subscription))
                .ReturnsAsync(false);

            // Act
            var result = await _controller.DeleteVisitType(orgId, visitName, subscription);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult?.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task DeleteVisitType_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var visitName = "Initial Consultation";
            bool subscription = false;

            _mockCommandHandler
                .Setup(c => c.DeleteVisitTypeAsync(orgId, visitName, subscription))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.DeleteVisitType(orgId, visitName, subscription);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }
    }
}
