﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="PracticeApi_Tests\**" />
    <Compile Remove="PracticeBusinessLayer\**" />
    <Compile Remove="PracticeContracts\**" />
    <Compile Remove="PracticeDataAccessLayer\**" />
    <Compile Remove="TestCases\**" />
    <Compile Remove="TestProject1\**" />
    <Content Remove="PracticeApi_Tests\**" />
    <Content Remove="PracticeBusinessLayer\**" />
    <Content Remove="PracticeContracts\**" />
    <Content Remove="PracticeDataAccessLayer\**" />
    <Content Remove="TestCases\**" />
    <Content Remove="TestProject1\**" />
    <EmbeddedResource Remove="PracticeApi_Tests\**" />
    <EmbeddedResource Remove="PracticeBusinessLayer\**" />
    <EmbeddedResource Remove="PracticeContracts\**" />
    <EmbeddedResource Remove="PracticeDataAccessLayer\**" />
    <EmbeddedResource Remove="TestCases\**" />
    <EmbeddedResource Remove="TestProject1\**" />
    <None Remove="PracticeApi_Tests\**" />
    <None Remove="PracticeBusinessLayer\**" />
    <None Remove="PracticeContracts\**" />
    <None Remove="PracticeDataAccessLayer\**" />
    <None Remove="TestCases\**" />
    <None Remove="TestProject1\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Azure.Messaging.ServiceBus" Version="7.18.2" />
    <PackageReference Include="DotNetEnv" Version="3.1.1" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.11" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="5.2.2" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Identity.Web" Version="3.5.0" />
    <PackageReference Include="NUnit" Version="4.3.2" />
    <PackageReference Include="ShardInterfaces" Version="1.0.0" />
    <PackageReference Include="ShardModels" Version="1.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="7.2.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="PracticeBusinessLayer\PracticeBusinessLayer.csproj" />
    <ProjectReference Include="PracticeContracts\PracticeContracts.csproj" />
    <ProjectReference Include="PracticeDataAccessLayer\PracticeDataAccessLayer.csproj" />
  </ItemGroup>


  <ItemGroup>
    <Compile Update="PracticeApiResources\PracticeApiStrings.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>PracticeApiStrings.resx</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="PracticeApiResources\PracticeApiStrings.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>PracticeApiStrings.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>

</Project>
