﻿
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.HttpsPolicy;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Models;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authentication;
using Microsoft.Identity.Web;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using PracticeContracts;
using PracticeDataAccessLayer.Context;
using PracticeDataAccessLayer.Implementation;
using PracticeBusinessLayer.CommandHandler;
using PracticeBusinessLayer.QueryHandler;
using PracticeBusinessLayer;
using Azure.Messaging.ServiceBus;
using Interfaces.ShardManagement;
using PracticeDataAccessLayer.PracticeDataAccessLayerResources;
using Microsoft.Extensions.Localization;


namespace PracticeApi
{
    public class Startup
    {
        public Startup(IConfiguration configuration)
        {
            DotNetEnv.Env.Load();
            Configuration = configuration;
        }

        public IConfiguration Configuration { get; }

        public void ConfigureServices(IServiceCollection services)
        {
            services.AddScoped(typeof(IMigration<,,>), typeof(Migration<,,>));
            services.AddScoped<IShardMapManagerService<PracticeDatabaseContext>>(provider =>
            {
                var connectionString = Environment.GetEnvironmentVariable("ShardMapManagerConnectionString");
                var logger = provider.GetRequiredService<ILogger<PracticeDatabaseContext>>();
                var localizer = provider.GetRequiredService<IStringLocalizer<PracticeDataAccessLayerStrings>>();
                return new ShardMapManagerService<PracticeDatabaseContext, PracticeDataAccessLayerStrings>(connectionString, logger, localizer);
            });
            services.AddScoped<ShardMapManagerService<PracticeDatabaseContext, PracticeDataAccessLayerStrings>>(provider =>
                (ShardMapManagerService<PracticeDatabaseContext, PracticeDataAccessLayerStrings>)provider.GetRequiredService<IShardMapManagerService<PracticeDatabaseContext>>());

            services.AddControllers();

            // Swagger configuration
            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new OpenApiInfo { Title = "Swagger Azure AD for Practice API", Version = "v1" });
                c.AddSecurityDefinition("oauth2", new OpenApiSecurityScheme
                {
                    Description = "Oauth2.0 which uses AuthorizationCode flow",
                    Name = "oauth2.0",
                    Type = SecuritySchemeType.OAuth2,
                    Flows = new OpenApiOAuthFlows
                    {
                        AuthorizationCode = new OpenApiOAuthFlow
                        {
                            AuthorizationUrl = new Uri(Environment.GetEnvironmentVariable("SwaggerAzureAd__AuthorizationUrl")),
                            TokenUrl = new Uri(Environment.GetEnvironmentVariable("SwaggerAzureAd__TokenUrl")),
                            Scopes = new Dictionary<string, string>
                            {
                                { Environment.GetEnvironmentVariable("SwaggerAzureAd__Scope"), "Access API as User" }
                            }
                        }
                    }
                });

                c.AddSecurityRequirement(new OpenApiSecurityRequirement
                {
                    {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference
                            {
                                Type = ReferenceType.SecurityScheme,
                                Id = "oauth2"
                            }
                        },
                        new[] { Environment.GetEnvironmentVariable("SwaggerAzureAd__Scope") }
                    }
                });
            });

            // Localization configuration
            services.AddLocalization();

            // Authentication configuration
            services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
                .AddJwtBearer(options =>
                {
                    options.Authority = Environment.GetEnvironmentVariable("AzureAd__Authority");
                    options.TokenValidationParameters = new Microsoft.IdentityModel.Tokens.TokenValidationParameters
                    {
                        ValidateIssuer = true,
                        ValidateAudience = true,
                        ValidateLifetime = true,
                        ValidIssuer = Environment.GetEnvironmentVariable("AzureAd__Authority"),
                        ValidAudience = Environment.GetEnvironmentVariable("AzureAd__ClientId")
                    };
                });

            // Dependency Injection configuration
            services.AddScoped<IPracticeQueryHandler<Tasks>, PracticeQueryHandler>();
            services.AddScoped<IPracticeCommandHandler<Tasks>, PracticeCommandHandler>();
            services.AddScoped<IProviderPatientQueryHandler<ProviderPatient>, ProviderPatientQueryHandler>();
            services.AddScoped<IProviderPatientCommandHandler<ProviderPatient>, ProviderPatientCommandHandler>();


            // Database context configuration
            services.AddDbContext<PracticeDatabaseContext>(options =>
                options.UseSqlServer(Environment.GetEnvironmentVariable("DatabaseConnectionString"))
                       .EnableSensitiveDataLogging()
                       .LogTo(Console.WriteLine, LogLevel.Information));

            // Unit of Work
            services.AddTransient<IUnitOfWork, UnitOfWork>(); 

        }

        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            if (env.IsDevelopment() || env.IsProduction())
            {
                app.UseSwagger();
                app.UseSwaggerUI(c =>
                {
                    c.OAuthClientId(Environment.GetEnvironmentVariable("SwaggerAzureAd__ClientId"));
                    c.OAuthUsePkce();
                    c.OAuthScopeSeparator(" ");
                    string swaggerJsonBasePath = string.IsNullOrWhiteSpace(c.RoutePrefix) ? "." : "..";
                    c.SwaggerEndpoint($"{swaggerJsonBasePath}/swagger/v1/swagger.json", "PracticeAPI v1");
                });
            }

            app.UseHttpsRedirection();

            app.UseRouting();
            app.UseAuthentication();
            app.UseAuthorization();

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
            });
        }
    }
}