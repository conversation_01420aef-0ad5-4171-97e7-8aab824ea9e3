﻿using PracticeContracts;
using ShardModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PracticeBusinessLayer
{
    public interface IPracticeCommandHandler<T>
    {
        Task AddTasks(List<Tasks> tasks, Guid OrgId, bool Subscription);
        Task UpdateTasks(Tasks task, Guid OrgId, bool Subscription);
        Task DeleteTasksById(Guid id, Guid OrgId, bool Subscription);
    }
}
