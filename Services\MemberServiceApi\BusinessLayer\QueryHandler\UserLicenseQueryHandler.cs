﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MemberServiceBusinessLayer;
using Contracts;
using MemberServiceDataAccessLayer.Implementation;
using Microsoft.Extensions.Localization;

namespace MemberServiceBusinessLayer.QueryHandler
{
    public class UserLicenseQueryHandler : IUserLicenseQueryHandler<UserLicense>
    {
        private readonly IUnitOfWork _unitOfWork;

        public UserLicenseQueryHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        }

        public async Task<UserLicense> GetUserLicenseByIdAsync(Guid id)
        {
            var UserLicense = await _unitOfWork.UserLicenseRepository.GetByIdAsync(id);
            return UserLicense;
        }
        public async Task<UserLicense> GetUserLicenseByOrganizationIdAsync(Guid id)
        {
            return await _unitOfWork.UserLicenseRepository.GetByOrganizationIdAsync(id);
        }


        public async Task<List<UserLicense>> GetAllUserLicensesAsync()
        {
            var licenses = await _unitOfWork.UserLicenseRepository.GetAllAsync();
            return licenses.ToList();
        }
    }
}