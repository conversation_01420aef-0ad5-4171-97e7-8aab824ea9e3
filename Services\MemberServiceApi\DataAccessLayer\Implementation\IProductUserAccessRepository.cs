﻿using Contracts;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MemberServiceDataAccessLayer.Implementation
{
    public interface IProductUserAccessRepository : IShardGenericRepository<ProductUserAccess>
    {
        Task<IEnumerable<Member>> GetMembersForProduct(Guid productId, Guid orgId, bool Subscription);
        Task<ProductUserAccess> GetAccessRecordAsync(Guid productId, Guid memberId, Guid orgId, bool Subscription);
    }
}
