using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Configuration;
using MemberServiceBusinessLayer.QueryHandler;
using MemberServiceDataAccessLayer.Implementation;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;

namespace MemberServiceApiBusinessLayerTestCases.QueryHandlerTests
{
    [TestFixture]
    public class LicenseQueryHandlerTests
    {
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<ILicenseRepository> _mockLicenseRepository;
        private LicenseQueryHandler _licenseQueryHandler;

        [SetUp]
        public void Setup()
        {
            _mockConfiguration = new Mock<IConfiguration>();
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockLicenseRepository = new Mock<ILicenseRepository>();

            _mockUnitOfWork.Setup(u => u.ProductLicenseRepository).Returns(_mockLicenseRepository.Object);

            _licenseQueryHandler = new LicenseQueryHandler(_mockConfiguration.Object, _mockUnitOfWork.Object);
        }

        [Test]
        public async Task GetLicense_ShouldReturnAllLicenses()
        {
            // Arrange
            var expectedLicenses = new List<ProductLicense>
            {
                new ProductLicense 
                { 
                    Id = Guid.NewGuid(), 
                    ProductName = "Product 1", 
                    Description = "License 1",
                    IsLicenseActivated = true
                },
                new ProductLicense 
                { 
                    Id = Guid.NewGuid(), 
                    ProductName = "Product 2", 
                    Description = "License 2",
                    IsLicenseActivated = false
                }
            };

            _mockLicenseRepository.Setup(r => r.GetAllAsync()).ReturnsAsync(expectedLicenses);

            // Act
            var result = await _licenseQueryHandler.GetLicense();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.EquivalentTo(expectedLicenses));
            _mockLicenseRepository.Verify(r => r.GetAllAsync(), Times.Once);
        }

        [Test]
        public async Task GetLicenseById_ShouldReturnLicense_WhenLicenseExists()
        {
            // Arrange
            var licenseId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var expectedLicense = new ProductLicense 
            { 
                Id = licenseId, 
                ProductName = "Product 1", 
                Description = "License 1",
                IsLicenseActivated = true,
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockLicenseRepository
                .Setup(r => r.GetByIdAsync(licenseId, orgId, subscription))
                .ReturnsAsync(expectedLicense);

            // Act
            var result = await _licenseQueryHandler.GetLicenseById(licenseId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(licenseId));
            Assert.That(result.ProductName, Is.EqualTo("Product 1"));
            Assert.That(result.Description, Is.EqualTo("License 1"));
            _mockLicenseRepository.Verify(r => r.GetByIdAsync(licenseId, orgId, subscription), Times.Once);
        }

        [Test]
        public async Task GetLicenseById_ShouldReturnNull_WhenLicenseDoesNotExist()
        {
            // Arrange
            var licenseId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockLicenseRepository
                .Setup(r => r.GetByIdAsync(licenseId, orgId, subscription))
                .ReturnsAsync((ProductLicense)null);

            // Act
            var result = await _licenseQueryHandler.GetLicenseById(licenseId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Null);
            _mockLicenseRepository.Verify(r => r.GetByIdAsync(licenseId, orgId, subscription), Times.Once);
        }
    }
}
