using NUnit.Framework;
using Moq;
using MemberServiceBusinessLayer.QueryHandler;
using MemberServiceDataAccessLayer.Implementation;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;

namespace MemberServiceApiBusinessLayerTestCases.QueryHandlerTests
{
    [TestFixture]
    public class EmployerQueryHandlerTests
    {
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<IEmployerRepository> _mockEmployerRepository;
        private EmployerQueryHandler _employerQueryHandler;

        [SetUp]
        public void Setup()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockEmployerRepository = new Mock<IEmployerRepository>();

            _mockUnitOfWork.Setup(u => u.EmployerRepository).Returns(_mockEmployerRepository.Object);

            _employerQueryHandler = new EmployerQueryHandler(_mockUnitOfWork.Object);
        }

        [Test]
        public async Task GetEmployerByIdAsync_ShouldReturnEmployer_WhenEmployerExists()
        {
            // Arrange
            var employerId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var expectedEmployer = new Employer
            {
                EmployerId = employerId,
                EmployerName = "Employer 1",
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockEmployerRepository.Setup(r => r.GetByIdAsync(employerId, orgId, subscription)).ReturnsAsync(expectedEmployer);

            // Act
            var result = await _employerQueryHandler.GetEmployerByIdAsync(employerId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.EmployerId, Is.EqualTo(employerId));
            Assert.That(result.EmployerName, Is.EqualTo("Employer 1"));
            _mockEmployerRepository.Verify(r => r.GetByIdAsync(employerId, orgId, subscription), Times.Once);
        }

        [Test]
        public async Task GetEmployerByIdAsync_ShouldReturnNull_WhenEmployerDoesNotExist()
        {
            // Arrange
            var employerId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockEmployerRepository.Setup(r => r.GetByIdAsync(employerId, orgId, subscription)).ReturnsAsync((Employer)null);

            // Act
            var result = await _employerQueryHandler.GetEmployerByIdAsync(employerId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Null);
            _mockEmployerRepository.Verify(r => r.GetByIdAsync(employerId, orgId, subscription), Times.Once);
        }

        [Test]
        public async Task GetAllEmployerAsync_ShouldReturnAllEmployers()
        {
            // Arrange
            var expectedEmployers = new List<Employer>
            {
                new Employer { EmployerId = Guid.NewGuid(), EmployerName = "Employer 1" },
                new Employer { EmployerId = Guid.NewGuid(), EmployerName = "Employer 2" }
            };

            _mockEmployerRepository.Setup(r => r.GetAllAsync()).ReturnsAsync(expectedEmployers);

            // Act
            var result = await _employerQueryHandler.GetAllEmployerAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result, Is.EquivalentTo(expectedEmployers));
            _mockEmployerRepository.Verify(r => r.GetAllAsync(), Times.Once);
        }

        [Test]
        public void Constructor_ShouldThrowArgumentNullException_WhenUnitOfWorkIsNull()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new EmployerQueryHandler(null));
        }
    }
}
