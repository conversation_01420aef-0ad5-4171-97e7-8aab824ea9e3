﻿using Contracts;
using DataAccessLayer.Context;
using Interfaces.ShardManagement;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MemberServiceDataAccessLayer.Implementation
{
    public class LicenseRepository : ShardGenericRepository<ProductLicense>, ILicenseRepository
    {
        private readonly AccountDatabaseContext _context;

        public LicenseRepository(AccountDatabaseContext context, ShardMapManagerService<AccountDatabaseContext, AccountDatabaseContext> _shardMapManagerService, IStringLocalizer<AccountDatabaseContext> localizer,
            ILogger<AccountDatabaseContext> logger) : base(context, _shardMapManagerService, localizer, logger)
        {
            _context = context;
        }
    }
}