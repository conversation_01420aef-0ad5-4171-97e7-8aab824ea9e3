using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using MemberServiceApi.Controllers;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using MemberServiceBusinessLayer;
using System.Linq;

namespace MemberServiceApiControllerTestCases
{
    [TestFixture]
    public class RoleslistControllerTests
    {
        private Mock<IRoleslistQueryHandler<Rolesdata>> _mockQueryHandler;
        private Mock<ILogger<RoleslistController>> _mockLogger;
        private Mock<IStringLocalizer<RoleslistController>> _mockLocalizer;
        private RoleslistController _controller;

        [SetUp]
        public void Setup()
        {
            _mockQueryHandler = new Mock<IRoleslistQueryHandler<Rolesdata>>();
            _mockLogger = new Mock<ILogger<RoleslistController>>();
            _mockLocalizer = new Mock<IStringLocalizer<RoleslistController>>();

            // Setup localizer to return the key when requested
            _mockLocalizer.Setup(l => l[It.IsAny<string>()]).Returns((string key) => new LocalizedString(key, key));

            _controller = new RoleslistController(
                _mockQueryHandler.Object,
                _mockLogger.Object,
                _mockLocalizer.Object
            );
        }

        [Test]
        public async Task GetAll_WhenRolesExist_ReturnsOkWithRoles()
        {
            // Arrange
            var mockRoles = new List<Rolesdata>
            {
                new Rolesdata { ID = Guid.NewGuid(), Rolename = "Admin" },
                new Rolesdata { ID = Guid.NewGuid(), Rolename = "User" }
            };

            _mockQueryHandler
                .Setup(q => q.GetRoleslist())
                .ReturnsAsync(mockRoles);

            // Act
            var result = await _controller.GetAll();

            // Assert
            Assert.That(result.Result, Is.InstanceOf<OkObjectResult>());
            var okResult = result.Result as OkObjectResult;
            Assert.That(okResult.StatusCode, Is.EqualTo(200));
            Assert.That(okResult.Value, Is.EqualTo(mockRoles));
        }

        [Test]
        public async Task GetAll_WhenNoRolesExist_ReturnsNotFound()
        {
            // Arrange
            _mockQueryHandler
                .Setup(q => q.GetRoleslist())
                .ReturnsAsync(new List<Rolesdata>());

            // Act
            var result = await _controller.GetAll();

            // Assert
            Assert.That(result.Result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result.Result as NotFoundObjectResult;
            Assert.That(notFoundResult.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task GetAll_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            _mockQueryHandler
                .Setup(q => q.GetRoleslist())
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetAll();

            // Assert
            Assert.That(result.Result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result.Result as ObjectResult;
            Assert.That(statusCodeResult.StatusCode, Is.EqualTo(500));
        }
    }
}
