using NUnit.Framework;
using Moq;
using MemberServiceBusinessLayer.CommandHandler;
using MemberServiceDataAccessLayer.Implementation;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MemberServiceApiBusinessLayerTestCases.CommandHandlerTests
{
    [TestFixture]
    public class OrganizationsCommandHandlerTests
    {
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<IOrganizationRepository> _mockOrganizationRepository;
        private OrganizationsCommandHandler _organizationsCommandHandler;

        [SetUp]
        public void Setup()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockOrganizationRepository = new Mock<IOrganizationRepository>();

            _mockUnitOfWork.Setup(u => u.OrganizationRepository).Returns(_mockOrganizationRepository.Object);

            _organizationsCommandHandler = new OrganizationsCommandHandler(_mockUnitOfWork.Object);
        }

        [Test]
        public async Task AddOrganizationAsync_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            var organizations = new List<Organization>
            {
                new Organization 
                { 
                    OrganizationId = orgId, 
                    OrganizationName = "Test Organization",
                    Address = "123 Main St",
                    Country = "USA",
                    ContactNumber = "123-456-7890",
                    Email = "<EMAIL>",
                    IsActive = true,
                    CreatedDate = DateTime.Now
                }
            };

            _mockOrganizationRepository
                .Setup(r => r.AddAsync(organizations))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _organizationsCommandHandler.AddOrganizationAsync(organizations, orgId);

            // Assert
            _mockOrganizationRepository.Verify(r => r.AddAsync(organizations), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateOrganizationAsync_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var organization = new Organization 
            { 
                OrganizationId = Guid.NewGuid(), 
                OrganizationName = "Test Organization",
                Address = "123 Main St",
                Country = "USA",
                ContactNumber = "123-456-7890",
                Email = "<EMAIL>",
                IsActive = true,
                CreatedDate = DateTime.Now,
                UpdatedDate = DateTime.Now,
                UpdatedBy = Guid.NewGuid()
            };

            _mockOrganizationRepository
                .Setup(r => r.UpdateAsync(organization))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _organizationsCommandHandler.UpdateOrganizationAsync(organization);

            // Assert
            _mockOrganizationRepository.Verify(r => r.UpdateAsync(organization), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteOrganizationAsync_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var organizationId = Guid.NewGuid();

            _mockOrganizationRepository
                .Setup(r => r.DeleteByIdAsync(organizationId))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _organizationsCommandHandler.DeleteOrganizationAsync(organizationId);

            // Assert
            _mockOrganizationRepository.Verify(r => r.DeleteByIdAsync(organizationId), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }
    }
}
