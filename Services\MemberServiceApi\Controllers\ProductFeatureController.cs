﻿using Contracts;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MemberServiceBusinessLayer;
using System.Diagnostics.Metrics;

namespace MemberServiceApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ProductFeatureController : ControllerBase
    {
        private readonly IProductFeatureQueryHandler<ProductFeature> _ProductFeatureQueryHandler;
        private readonly IProductFeatureCommandHandler<ProductFeature> _ProductFeatureCommandHandler;
        private readonly ILogger<ProductFeatureController> _logger;
        private readonly IStringLocalizer<ProductFeatureController> _localizer;
        public ProductFeatureController(
            IProductFeatureQueryHandler<ProductFeature> ProductFeatureQueryHandler,
            IProductFeatureCommandHandler<ProductFeature> ProductFeatureCommandHandler,
            ILogger<ProductFeatureController> logger,
            IStringLocalizer<ProductFeatureController> localizer)
        {
            _ProductFeatureQueryHandler = ProductFeatureQueryHandler ?? throw new ArgumentNullException(nameof(ProductFeatureQueryHandler));
            _ProductFeatureCommandHandler = ProductFeatureCommandHandler ?? throw new ArgumentNullException(nameof(ProductFeatureCommandHandler));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _localizer = localizer ?? throw new ArgumentNullException(nameof(localizer));
        }

        /// <summary>
        /// Gets a ProductFeature by its ID.
        /// </summary>
        /// <param name="id">The ID of the ProductFeature.</param>
        /// <returns>An IActionResult containing the ProductFeature or a not found result.</returns>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetProductFeatureById(Guid id)
        {
            IActionResult response;
            try
            {
                _logger.LogInformation(_localizer["FetchingProductFeatureWithID"], id);
                var ProductFeature = await _ProductFeatureQueryHandler.GetProductFeatureByIdAsync(id);
                if (ProductFeature == null)
                {
                    _logger.LogWarning(_localizer["ProductFeatureNotFound"], id);
                    response = NotFound(_localizer["ProductFeatureNotFoundMessage"]);
                }
                else
                {
                    _logger.LogInformation(_localizer["ProductFeatureFetchedSuccessfully"], id);
                    response = Ok(ProductFeature);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingProductFeature"], id);
                response = StatusCode(500, _localizer["InternalServerErrorMessage"]);
            }
            return response;
        }

        /// <summary>
        /// Adds a new ProductFeature.
        /// </summary>
        /// <param name="ProductFeature">The ProductFeature to add.</param>
        /// <returns>An IActionResult containing the result of the operation.</returns>
        [HttpPost]
        public async Task<IActionResult> AddProductFeature([FromBody] ProductFeature ProductFeature)
        {
            IActionResult response;
            if (ProductFeature == null)
            {
                _logger.LogWarning(_localizer["InvalidProductFeatureData"]);
                response = BadRequest(_localizer["ProductFeatureInvalidMessage"]);
            }
            else
            {
                try
                {
                    _logger.LogInformation(_localizer["AddingNewProductFeature"]);
                    await _ProductFeatureCommandHandler.AddProductFeatureAsync(ProductFeature);
                    _logger.LogInformation(_localizer["ProductFeatureAddedSuccessfully"], ProductFeature.Id);
                    response = CreatedAtAction(nameof(GetProductFeatureById), new { id = ProductFeature.Id }, ProductFeature);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["ErrorAddingProductFeature"]);
                    response = StatusCode(500, _localizer["InternalServerErrorMessage"]);
                }
            }
            return response;
        }

        /// <summary>
        /// Updates an existing ProductFeature.
        /// </summary>
        /// <param name="id">The ID of the ProductFeature to update.</param>
        /// <param name="ProductFeature">The updated ProductFeature data.</param>
        /// <returns>An IActionResult containing the result of the operation.</returns>
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateProductFeature(Guid id, [FromBody] ProductFeature ProductFeature)
        {
            IActionResult response;
            if (ProductFeature == null || id != ProductFeature.Id)
            {
                _logger.LogWarning(_localizer["InvalidDataForUpdate"], id);
                response = BadRequest(_localizer["ProductFeatureInvalidMessage"]);
            }
            else
            {
                try
                {
                    _logger.LogInformation(_localizer["UpdatingProductFeatureWithID"], id);
                    await _ProductFeatureCommandHandler.UpdateProductFeatureAsync(ProductFeature);
                    _logger.LogInformation(_localizer["ProductFeatureUpdatedSuccessfully"], id);
                    response = NoContent();
                }
                catch (KeyNotFoundException)
                {
                    _logger.LogWarning(_localizer["ProductFeatureNotFoundForUpdate"], id);
                    response = NotFound(_localizer["ProductFeatureNotFoundMessage"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["ErrorUpdatingProductFeature"]);
                    response = StatusCode(500, _localizer["InternalServerErrorMessage"]);
                }
            }
            return response;
        }

        /// <summary>
        /// Deletes a ProductFeature by its ID.
        /// </summary>
        /// <param name="id">The ID of the ProductFeature to delete.</param>
        /// <returns>An IActionResult containing the result of the operation.</returns>
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteProductFeature(Guid id)
        {
            IActionResult response;
            try
            {
                _logger.LogInformation(_localizer["DeletingProductFeatureWithID"], id);
                await _ProductFeatureCommandHandler.DeleteProductFeatureAsync(id);
                _logger.LogInformation(_localizer["ProductFeatureDeletedSuccessfully"], id);
                response = NoContent();
            }
            catch (KeyNotFoundException)
            {
                _logger.LogWarning(_localizer["ProductFeatureNotFoundForDeletion"], id);
                response = NotFound(_localizer["ProductFeatureNotFoundMessage"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorDeletingProductFeature"]);
                response = StatusCode(500, _localizer["InternalServerErrorMessage"]);
            }
            return response;
        }

        /// <summary>
        /// Gets all ProductFeatures.
        /// </summary>
        /// <returns>An IActionResult containing the list of all ProductFeatures or a not found result.</returns>
        [HttpGet]
        public async Task<IActionResult> GetAllProductFeatures()
        {
            IActionResult response;
            _logger.LogInformation(_localizer["FetchingAllProductFeatures"]);
            try
            {
                var ProductFeatures = await _ProductFeatureQueryHandler.GetAllProductFeaturesAsync();
                if (ProductFeatures == null || ProductFeatures.Count == 0)
                {
                    _logger.LogWarning(_localizer["NoProductFeaturesFound"]);
                    response = NotFound(_localizer["ProductFeaturesNotFoundMessage"]);
                }
                else
                {
                    _logger.LogInformation(_localizer["AllProductFeaturesFetchedSuccessfully"]);
                    response = Ok(ProductFeatures);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingProductFeatures"]);
                response = StatusCode(500, _localizer["InternalServerErrorMessage"]);
            }
            return response;
        }
    }
}