﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="GetLogError" xml:space="preserve">
    <value>An error occurred while fetching members.</value>
  </data>
  <data name="PostLogError" xml:space="preserve">
    <value>An error occurred while registering members.</value>
  </data>
  <data name="NoMembers" xml:space="preserve">
    <value>No members to register.</value>
  </data>
  <data name="SuccessfulRegistration" xml:space="preserve">
    <value>Members registered successfully.</value>
  </data>
  <data name="DatabaseError" xml:space="preserve">
    <value>A database error occurred.</value>
  </data>
  <data name="DeleteSuccessful" xml:space="preserve">
    <value>Delete Successful</value>
  </data>
  <data name="MemberNotFound" xml:space="preserve">
    <value>Member Not Found</value>
  </data>
  <data name="UpdateLogError" xml:space="preserve">
    <value>Log not updated</value>
  </data>
  <data name="UpdateSuccessful" xml:space="preserve">
    <value>Update Successful</value>
  </data>
  <data name="InvalidId" xml:space="preserve">
    <value>The given Id is not valid</value>
  </data>
  <data name="InvalidMember" xml:space="preserve">
    <value>Not a valid member</value>
  </data>
  <data name="DeleteLogError" xml:space="preserve">
    <value>Item is not deleted</value>
  </data>
  <data name="AccessError" xml:space="preserve">
    <value>ProductId and MemberId are required.</value>
  </data>
  <data name="AccessUpdateSuccessful" xml:space="preserve">
    <value>Access updated successfully</value>
  </data>
  <data name="AccessNotFound" xml:space="preserve">
    <value>Access record not found</value>
  </data>
  <data name="Users" xml:space="preserve">
    <value>Users</value>
  </data>
  <data name="Accounts" xml:space="preserve">
    <value>Accounts</value>
  </data>
  <data name="AccountService" xml:space="preserve">
    <value>AccountService</value>
  </data>
  <data name="NewIdFunction" xml:space="preserve">
    <value>NEWID()</value>
  </data>
  <data name="registration" xml:space="preserve">
    <value>registration</value>
  </data>
  <data name="Products" xml:space="preserve">
    <value>Products</value>
  </data>
  <data name="ProductUserAccess" xml:space="preserve">
    <value>ProductUserAccess</value>
  </data>
  <data name="Licenses" xml:space="preserve">
    <value>Licenses</value>
  </data>
  <data name="ProductService" xml:space="preserve">
    <value>ProductService</value>
  </data>
  <data name="FetchingPlanTypeWithID" xml:space="preserve">
    <value>FetchingPlanTypeWithID</value>
  </data>
  <data name="PlanTypeNotFound" xml:space="preserve">
    <value>PlanTypeNotFound</value>
  </data>
  <data name="PlanTypeNotFoundMessage" xml:space="preserve">
    <value>PlanTypeNotFoundMessage</value>
  </data>
  <data name="PlanTypeFetchedSuccessfully" xml:space="preserve">
    <value>PlanTypeFetchedSuccessfully</value>
  </data>
  <data name="ErrorFetchingPlanType" xml:space="preserve">
    <value>ErrorFetchingPlanType</value>
  </data>
  <data name="InternalServerErrorMessage" xml:space="preserve">
    <value>InternalServerErrorMessage</value>
  </data>
  <data name="InvalidPlanTypeData" xml:space="preserve">
    <value>InvalidPlanTypeData</value>
  </data>
  <data name="PlanTypeInvalidMessage" xml:space="preserve">
    <value>PlanTypeInvalidMessage</value>
  </data>
  <data name="AddingNewPlanType" xml:space="preserve">
    <value>AddingNewPlanType</value>
  </data>
  <data name="PlanTypeAddedSuccessfully" xml:space="preserve">
    <value>PlanTypeAddedSuccessfully</value>
  </data>
  <data name="ErrorAddingPlanType" xml:space="preserve">
    <value>ErrorAddingPlanType</value>
  </data>
  <data name="InvalidDataForUpdate" xml:space="preserve">
    <value>InvalidDataForUpdate</value>
  </data>
  <data name="UpdatingPlanTypeWithID" xml:space="preserve">
    <value>UpdatingPlanTypeWithID</value>
  </data>
  <data name="PlanTypeUpdatedSuccessfully" xml:space="preserve">
    <value>PlanTypeUpdatedSuccessfully</value>
  </data>
  <data name="PlanTypeNotFoundForUpdate" xml:space="preserve">
    <value>PlanTypeNotFoundForUpdate</value>
  </data>
  <data name="ErrorUpdatingPlanType" xml:space="preserve">
    <value>ErrorUpdatingPlanType</value>
  </data>
  <data name="DeletingPlanTypeWithID" xml:space="preserve">
    <value>DeletingPlanTypeWithID</value>
  </data>
  <data name="PlanTypeDeletedSuccessfully" xml:space="preserve">
    <value>PlanTypeDeletedSuccessfully</value>
  </data>
  <data name="PlanTypeNotFoundForDeletion" xml:space="preserve">
    <value>PlanTypeNotFoundForDeletion</value>
  </data>
  <data name="ErrorDeletingPlanType" xml:space="preserve">
    <value>ErrorDeletingPlanType</value>
  </data>
  <data name="FetchingAllPlanTypes" xml:space="preserve">
    <value>FetchingAllPlanTypes</value>
  </data>
  <data name="NoPlanTypesFound" xml:space="preserve">
    <value>NoPlanTypesFound</value>
  </data>
  <data name="PlanTypesNotFoundMessage" xml:space="preserve">
    <value>PlanTypesNotFoundMessage</value>
  </data>
  <data name="AllPlanTypesFetchedSuccessfully" xml:space="preserve">
    <value>AllPlanTypesFetchedSuccessfully</value>
  </data>
  <data name="ErrorFetchingPlanTypes" xml:space="preserve">
    <value>ErrorFetchingPlanTypes</value>
  </data>
  <data name="FetchingUserLicenseWithID" xml:space="preserve">
    <value>FetchingUserLicenseWithID</value>
  </data>
  <data name="UserLicenseNotFound" xml:space="preserve">
    <value>UserLicenseNotFound</value>
  </data>
  <data name="UserLicenseNotFoundMessage" xml:space="preserve">
    <value>UserLicenseNotFoundMessage</value>
  </data>
  <data name="UserLicenseFetchedSuccessfully" xml:space="preserve">
    <value>UserLicenseFetchedSuccessfully</value>
  </data>
  <data name="ErrorFetchingUserLicense" xml:space="preserve">
    <value>ErrorFetchingUserLicense</value>
  </data>
  <data name="InvalidUserLicenseData" xml:space="preserve">
    <value>InvalidUserLicenseData</value>
  </data>
  <data name="UserLicenseInvalidMessage" xml:space="preserve">
    <value>UserLicenseInvalidMessage</value>
  </data>
  <data name="AddingNewUserLicense" xml:space="preserve">
    <value>AddingNewUserLicense</value>
  </data>
  <data name="UserLicenseAddedSuccessfully" xml:space="preserve">
    <value>UserLicenseAddedSuccessfully</value>
  </data>
  <data name="ErrorAddingUserLicense" xml:space="preserve">
    <value>ErrorAddingUserLicense</value>
  </data>
  <data name="UpdatingUserLicenseWithID" xml:space="preserve">
    <value>UpdatingUserLicenseWithID</value>
  </data>
  <data name="UserLicenseUpdatedSuccessfully" xml:space="preserve">
    <value>UserLicenseUpdatedSuccessfully</value>
  </data>
  <data name="UserLicenseNotFoundForUpdate" xml:space="preserve">
    <value>UserLicenseNotFoundForUpdate</value>
  </data>
  <data name="ErrorUpdatingUserLicense" xml:space="preserve">
    <value>ErrorUpdatingUserLicense</value>
  </data>
  <data name="DeletingUserLicenseWithID" xml:space="preserve">
    <value>DeletingUserLicenseWithID</value>
  </data>
  <data name="UserLicenseDeletedSuccessfully" xml:space="preserve">
    <value>UserLicenseDeletedSuccessfully</value>
  </data>
  <data name="UserLicenseNotFoundForDeletion" xml:space="preserve">
    <value>UserLicenseNotFoundForDeletion</value>
  </data>
  <data name="ErrorDeletingUserLicense" xml:space="preserve">
    <value>ErrorDeletingUserLicense</value>
  </data>
  <data name="FetchingAllUserLicenses" xml:space="preserve">
    <value>FetchingAllUserLicenses</value>
  </data>
  <data name="AllUserLicensesFetchedSuccessfully" xml:space="preserve">
    <value>AllUserLicensesFetchedSuccessfully</value>
  </data>
  <data name="FetchingPreDefinedPageRoleMappingWithID" xml:space="preserve">
    <value>FetchingPreDefinedPageRoleMappingWithID</value>
  </data>
  <data name="PreDefinedPageRoleMappingNotFound" xml:space="preserve">
    <value>PreDefinedPageRoleMappingNotFound</value>
  </data>
  <data name="PreDefinedPageRoleMappingNotFoundMessage" xml:space="preserve">
    <value>PreDefinedPageRoleMappingNotFoundMessage</value>
  </data>
  <data name="PreDefinedPageRoleMappingFetchedSuccessfully" xml:space="preserve">
    <value>PreDefinedPageRoleMappingFetchedSuccessfully</value>
  </data>
  <data name="ErrorFetchingPreDefinedPageRoleMapping" xml:space="preserve">
    <value>ErrorFetchingPreDefinedPageRoleMapping</value>
  </data>
  <data name="FetchingPagesWithRoleID" xml:space="preserve">
    <value>FetchingPagesWithRoleID</value>
  </data>
  <data name="PagesNotFound" xml:space="preserve">
    <value>PagesNotFound</value>
  </data>
  <data name="PagesFetchedSuccessfully" xml:space="preserve">
    <value>PagesFetchedSuccessfully</value>
  </data>
  <data name="ErrorFetchingPages" xml:space="preserve">
    <value>ErrorFetchingPages</value>
  </data>
  <data name="InvalidPreDefinedPageRoleMappingData" xml:space="preserve">
    <value>InvalidPreDefinedPageRoleMappingData</value>
  </data>
  <data name="PreDefinedPageRoleMappingInvalidMessage" xml:space="preserve">
    <value>PreDefinedPageRoleMappingInvalidMessage</value>
  </data>
  <data name="AddingNewPreDefinedPageRoleMapping" xml:space="preserve">
    <value>AddingNewPreDefinedPageRoleMapping</value>
  </data>
  <data name="PreDefinedPageRoleMappingAddedSuccessfully" xml:space="preserve">
    <value>PreDefinedPageRoleMappingAddedSuccessfully</value>
  </data>
  <data name="ErrorAddingPreDefinedPageRoleMapping" xml:space="preserve">
    <value>ErrorAddingPreDefinedPageRoleMapping</value>
  </data>
  <data name="UpdatingPreDefinedPageRoleMappingWithID" xml:space="preserve">
    <value>UpdatingPreDefinedPageRoleMappingWithID</value>
  </data>
  <data name="PreDefinedPageRoleMappingUpdatedSuccessfully" xml:space="preserve">
    <value>PreDefinedPageRoleMappingUpdatedSuccessfully</value>
  </data>
  <data name="PreDefinedPageRoleMappingNotFoundForUpdate" xml:space="preserve">
    <value>PreDefinedPageRoleMappingNotFoundForUpdate</value>
  </data>
  <data name="ErrorUpdatingPreDefinedPageRoleMapping" xml:space="preserve">
    <value>ErrorUpdatingPreDefinedPageRoleMapping</value>
  </data>
  <data name="DeletingPreDefinedPageRoleMappingWithID" xml:space="preserve">
    <value>DeletingPreDefinedPageRoleMappingWithID</value>
  </data>
  <data name="PreDefinedPageRoleMappingDeletedSuccessfully" xml:space="preserve">
    <value>PreDefinedPageRoleMappingDeletedSuccessfully</value>
  </data>
  <data name="PreDefinedPageRoleMappingNotFoundForDeletion" xml:space="preserve">
    <value>PreDefinedPageRoleMappingNotFoundForDeletion</value>
  </data>
  <data name="ErrorDeletingPreDefinedPageRoleMapping" xml:space="preserve">
    <value>ErrorDeletingPreDefinedPageRoleMapping</value>
  </data>
  <data name="PagePathRequired" xml:space="preserve">
    <value>PagePathRequired</value>
  </data>
  <data name="PagePathRequiredMessage" xml:space="preserve">
    <value>PagePathRequiredMessage</value>
  </data>
  <data name="SearchingPreDefinedPageRoleMappingsByPagePath" xml:space="preserve">
    <value>SearchingPreDefinedPageRoleMappingsByPagePath</value>
  </data>
  <data name="NoPreDefinedPageRoleMappingsFoundByPagePath" xml:space="preserve">
    <value>NoPreDefinedPageRoleMappingsFoundByPagePath</value>
  </data>
  <data name="PreDefinedPageRoleMappingsNotFoundMessage" xml:space="preserve">
    <value>PreDefinedPageRoleMappingsNotFoundMessage</value>
  </data>
  <data name="PreDefinedPageRoleMappingsFetchedSuccessfully" xml:space="preserve">
    <value>PreDefinedPageRoleMappingsFetchedSuccessfully</value>
  </data>
  <data name="ErrorSearchingPreDefinedPageRoleMappings" xml:space="preserve">
    <value>ErrorSearchingPreDefinedPageRoleMappings</value>
  </data>
  <data name="FetchingAllPreDefinedPageRoleMappings" xml:space="preserve">
    <value>FetchingAllPreDefinedPageRoleMappings</value>
  </data>
  <data name="NoPreDefinedPageRoleMappingsFound" xml:space="preserve">
    <value>NoPreDefinedPageRoleMappingsFound</value>
  </data>
  <data name="FetchingProductFeatureWithID" xml:space="preserve">
    <value>FetchingProductFeatureWithID</value>
  </data>
  <data name="ProductFeatureNotFound" xml:space="preserve">
    <value>ProductFeatureNotFound</value>
  </data>
  <data name="ProductFeatureNotFoundMessage" xml:space="preserve">
    <value>ProductFeatureNotFoundMessage</value>
  </data>
  <data name="ProductFeatureFetchedSuccessfully" xml:space="preserve">
    <value>ProductFeatureFetchedSuccessfully</value>
  </data>
  <data name="ErrorFetchingProductFeature" xml:space="preserve">
    <value>ErrorFetchingProductFeature</value>
  </data>
  <data name="InvalidProductFeatureData" xml:space="preserve">
    <value>InvalidProductFeatureData</value>
  </data>
  <data name="ProductFeatureInvalidMessage" xml:space="preserve">
    <value>ProductFeatureInvalidMessage</value>
  </data>
  <data name="AddingNewProductFeature" xml:space="preserve">
    <value>AddingNewProductFeature</value>
  </data>
  <data name="ProductFeatureAddedSuccessfully" xml:space="preserve">
    <value>ProductFeatureAddedSuccessfully</value>
  </data>
  <data name="ErrorAddingProductFeature" xml:space="preserve">
    <value>ErrorAddingProductFeature</value>
  </data>
  <data name="UpdatingProductFeatureWithID" xml:space="preserve">
    <value>UpdatingProductFeatureWithID</value>
  </data>
  <data name="ProductFeatureUpdatedSuccessfully" xml:space="preserve">
    <value>ProductFeatureUpdatedSuccessfully</value>
  </data>
  <data name="ProductFeatureNotFoundForUpdate" xml:space="preserve">
    <value>ProductFeatureNotFoundForUpdate</value>
  </data>
  <data name="ErrorUpdatingProductFeature" xml:space="preserve">
    <value>ErrorUpdatingProductFeature</value>
  </data>
  <data name="DeletingProductFeatureWithID" xml:space="preserve">
    <value>DeletingProductFeatureWithID</value>
  </data>
  <data name="ProductFeatureDeletedSuccessfully" xml:space="preserve">
    <value>ProductFeatureDeletedSuccessfully</value>
  </data>
  <data name="ProductFeatureNotFoundForDeletion" xml:space="preserve">
    <value>ProductFeatureNotFoundForDeletion</value>
  </data>
  <data name="FetchingAllProductFeatures" xml:space="preserve">
    <value>FetchingAllProductFeatures</value>
  </data>
  <data name="AllProductFeaturesFetchedSuccessfully" xml:space="preserve">
    <value>AllProductFeaturesFetchedSuccessfully</value>
  </data>
</root>