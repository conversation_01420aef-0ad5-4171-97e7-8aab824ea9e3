using Microsoft.Extensions.Configuration;
using Moq;
using PracticeBusinessLayer.QueryHandler;
using PracticeContracts;
using PracticeDataAccessLayer.Implementation;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace PracticeApiBusinessLayerTestCases
{
    [TestFixture]
    public class ProviderPatientQueryHandlerTests
    {
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<IProviderPatientRepository> _mockProviderPatientRepository;
        private ProviderPatientQueryHandler _queryHandler;

        [SetUp]
        public void Setup()
        {
            // Initialize mocks
            _mockConfiguration = new Mock<IConfiguration>();
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockProviderPatientRepository = new Mock<IProviderPatientRepository>();

            // Setup repository mock
            _mockUnitOfWork.Setup(u => u.ProviderPatientRepository).Returns(_mockProviderPatientRepository.Object);

            // Create query handler with mocked dependencies
            _queryHandler = new ProviderPatientQueryHandler(
                _mockConfiguration.Object,
                _mockUnitOfWork.Object
            );
        }

        [Test]
        public async Task GetProviderPatient_ShouldReturnAllProviderPatients()
        {
            // Arrange
            var expectedProviderPatients = new List<ProviderPatient>
            {
                new ProviderPatient { Id = Guid.NewGuid(), SSN = "***********", PCPId = Guid.NewGuid(), Username = "user1" },
                new ProviderPatient { Id = Guid.NewGuid(), SSN = "***********", PCPId = Guid.NewGuid(), Username = "user2" },
                new ProviderPatient { Id = Guid.NewGuid(), SSN = "***********", PCPId = Guid.NewGuid(), Username = "user3" }
            };

            _mockProviderPatientRepository.Setup(r => r.GetAllAsync())
                .ReturnsAsync(expectedProviderPatients);

            // Act
            var result = await _queryHandler.GetProviderPatient();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.EqualTo(expectedProviderPatients));
            _mockProviderPatientRepository.Verify(r => r.GetAllAsync(), Times.Once);
        }

        [Test]
        public async Task GetProviderPatient_WhenNoProviderPatients_ShouldReturnEmptyList()
        {
            // Arrange
            var emptyList = new List<ProviderPatient>();
            _mockProviderPatientRepository.Setup(r => r.GetAllAsync())
                .ReturnsAsync(emptyList);

            // Act
            var result = await _queryHandler.GetProviderPatient();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result, Is.Empty);
            _mockProviderPatientRepository.Verify(r => r.GetAllAsync(), Times.Once);
        }

        [Test]
        public async Task GetProviderPatientById_ShouldReturnCorrectProviderPatient()
        {
            // Arrange
            var providerPatientId = Guid.NewGuid();
            var expectedProviderPatient = new ProviderPatient {
                Id = providerPatientId,
                SSN = "***********",
                PCPId = Guid.NewGuid(),
                Username = "testuser"
            };

            _mockProviderPatientRepository.Setup(r => r.GetByIdAsync(providerPatientId))
                .ReturnsAsync(expectedProviderPatient);

            // Act
            var result = await _queryHandler.GetProviderPatientById(providerPatientId);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(providerPatientId));
            Assert.That(result, Is.EqualTo(expectedProviderPatient));
            _mockProviderPatientRepository.Verify(r => r.GetByIdAsync(providerPatientId), Times.Once);
        }

        [Test]
        public async Task GetProviderPatientById_WhenProviderPatientNotFound_ShouldReturnNull()
        {
            // Arrange
            var providerPatientId = Guid.NewGuid();

            _mockProviderPatientRepository.Setup(r => r.GetByIdAsync(providerPatientId))
                .ReturnsAsync((ProviderPatient)null);

            // Act
            var result = await _queryHandler.GetProviderPatientById(providerPatientId);

            // Assert
            Assert.That(result, Is.Null);
            _mockProviderPatientRepository.Verify(r => r.GetByIdAsync(providerPatientId), Times.Once);
        }

        [Test]
        public async Task GetProviderPatientById_WithInvalidId_ShouldStillCallRepository()
        {
            // Arrange
            var invalidId = Guid.Empty;

            _mockProviderPatientRepository.Setup(r => r.GetByIdAsync(invalidId))
                .ReturnsAsync((ProviderPatient)null);

            // Act
            var result = await _queryHandler.GetProviderPatientById(invalidId);

            // Assert
            Assert.That(result, Is.Null);
            _mockProviderPatientRepository.Verify(r => r.GetByIdAsync(invalidId), Times.Once);
        }
    }
}
