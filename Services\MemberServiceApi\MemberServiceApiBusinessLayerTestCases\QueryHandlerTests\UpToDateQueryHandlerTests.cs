using NUnit.Framework;
using Moq;
using MemberServiceBusinessLayer.QueryHandler;
using MemberServiceDataAccessLayer.Implementation;
using DataAccessLayer.Implementation;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;

namespace MemberServiceApiBusinessLayerTestCases.QueryHandlerTests
{
    [TestFixture]
    public class UpToDateQueryHandlerTests
    {
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<IUpToDateRepository> _mockUpToDateRepository;
        private UpToDateQueryHandler _upToDateQueryHandler;

        [SetUp]
        public void Setup()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockUpToDateRepository = new Mock<IUpToDateRepository>();

            _mockUnitOfWork.Setup(u => u.UpToDateRepository).Returns(_mockUpToDateRepository.Object);

            _upToDateQueryHandler = new UpToDateQueryHandler(_mockUnitOfWork.Object);
        }

        [Test]
        public async Task GetUpToDateByIdAsync_ShouldReturnUpToDate_WhenUpToDateExists()
        {
            // Arrange
            var upToDateId = Guid.NewGuid();
            var expectedUpToDate = new UpToDate
            {
                Id = upToDateId,
                ProviderName = "Provider 1",
                SearchName = "Search 1",
                URL = "https://example.com",
                UserName = "user1",
                Password = "password1"
            };

            _mockUpToDateRepository
                .Setup(r => r.GetByIdAsync(upToDateId))
                .ReturnsAsync(expectedUpToDate);

            // Act
            var result = await _upToDateQueryHandler.GetUpToDateByIdAsync(upToDateId);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(upToDateId));
            Assert.That(result.ProviderName, Is.EqualTo("Provider 1"));
            _mockUpToDateRepository.Verify(r => r.GetByIdAsync(upToDateId), Times.Once);
        }

        [Test]
        public async Task GetUpToDateByIdAsync_ShouldReturnNull_WhenUpToDateDoesNotExist()
        {
            // Arrange
            var upToDateId = Guid.NewGuid();

            _mockUpToDateRepository
                .Setup(r => r.GetByIdAsync(upToDateId))
                .ReturnsAsync((UpToDate)null);

            // Act
            var result = await _upToDateQueryHandler.GetUpToDateByIdAsync(upToDateId);

            // Assert
            Assert.That(result, Is.Null);
            _mockUpToDateRepository.Verify(r => r.GetByIdAsync(upToDateId), Times.Once);
        }

        [Test]
        public async Task GetAllUpToDatesAsync_ShouldReturnAllUpToDates()
        {
            // Arrange
            var expectedUpToDates = new List<UpToDate>
            {
                new UpToDate
                {
                    Id = Guid.NewGuid(),
                    ProviderName = "Provider 1",
                    SearchName = "Search 1",
                    URL = "https://example1.com",
                    UserName = "user1",
                    Password = "password1"
                },
                new UpToDate
                {
                    Id = Guid.NewGuid(),
                    ProviderName = "Provider 2",
                    SearchName = "Search 2",
                    URL = "https://example2.com",
                    UserName = "user2",
                    Password = "password2"
                }
            };

            _mockUpToDateRepository
                .Setup(r => r.GetAllAsync())
                .ReturnsAsync(expectedUpToDates);

            // Act
            var result = await _upToDateQueryHandler.GetAllUpToDatesAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result, Is.EquivalentTo(expectedUpToDates));
            _mockUpToDateRepository.Verify(r => r.GetAllAsync(), Times.Once);
        }

        [Test]
        public void Constructor_ShouldThrowArgumentNullException_WhenUnitOfWorkIsNull()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new UpToDateQueryHandler(null));
        }
    }
}
