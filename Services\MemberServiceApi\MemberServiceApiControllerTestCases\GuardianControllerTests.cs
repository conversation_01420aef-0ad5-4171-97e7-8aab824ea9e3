using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using MemberServiceApi.Controllers;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using MemberServiceBusinessLayer;

namespace MemberServiceApiControllerTestCases
{
    [TestFixture]
    public class GuardianControllerTests
    {
        private Mock<IGuardianQueryHandler<Guardian>> _mockQueryHandler;
        private Mock<IGuardianCommandHandler<Guardian>> _mockCommandHandler;
        private Mock<ILogger<GuardianController>> _mockLogger;
        private Mock<IStringLocalizer<GuardianController>> _mockLocalizer;
        private GuardianController _controller;

        [SetUp]
        public void Setup()
        {
            _mockQueryHandler = new Mock<IGuardianQueryHandler<Guardian>>();
            _mockCommandHandler = new Mock<IGuardianCommandHandler<Guardian>>();
            _mockLogger = new Mock<ILogger<GuardianController>>();
            _mockLocalizer = new Mock<IStringLocalizer<GuardianController>>();

            // Setup localizer to return the key when requested
            _mockLocalizer.Setup(l => l[It.IsAny<string>()]).Returns((string key) => new LocalizedString(key, key));

            _controller = new GuardianController(
                _mockQueryHandler.Object,
                _mockCommandHandler.Object,
                _mockLogger.Object,
                _mockLocalizer.Object
            );
        }

        [Test]
        public async Task GetAllGuardian_WhenGuardiansExist_ReturnsOkWithGuardians()
        {
            // Arrange
            var mockGuardians = new List<Guardian>
            {
                new Guardian { GuardianId = Guid.NewGuid(), GuardianName = "John Doe" },
                new Guardian { GuardianId = Guid.NewGuid(), GuardianName = "Jane Smith" }
            };

            _mockQueryHandler
                .Setup(q => q.GetAllGuardianAsync())
                .ReturnsAsync(mockGuardians);

            // Act
            var result = await _controller.GetAllGuardian();

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult.StatusCode, Is.EqualTo(200));
            Assert.That(okResult.Value, Is.EqualTo(mockGuardians));
        }

        [Test]
        public async Task GetAllGuardian_WhenNoGuardiansExist_ReturnsNotFound()
        {
            // Arrange
            _mockQueryHandler
                .Setup(q => q.GetAllGuardianAsync())
                .ReturnsAsync(new List<Guardian>());

            // Act
            var result = await _controller.GetAllGuardian();

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task GetAllGuardian_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            _mockQueryHandler
                .Setup(q => q.GetAllGuardianAsync())
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetAllGuardian();

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task GetGuardianById_WhenGuardianExists_ReturnsOkWithGuardian()
        {
            // Arrange
            var guardianId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var mockGuardian = new Guardian
            {
                GuardianId = guardianId,
                GuardianName = "John Doe",
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockQueryHandler
                .Setup(q => q.GetGuardianByIdAsync(guardianId, orgId, subscription))
                .ReturnsAsync(mockGuardian);

            // Act
            var result = await _controller.GetGuardianById(guardianId, orgId, subscription);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult.StatusCode, Is.EqualTo(200));
            Assert.That(okResult.Value, Is.EqualTo(mockGuardian));
        }

        [Test]
        public async Task GetGuardianById_WhenGuardianDoesNotExist_ReturnsNotFound()
        {
            // Arrange
            var guardianId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockQueryHandler
                .Setup(q => q.GetGuardianByIdAsync(guardianId, orgId, subscription))
                .ReturnsAsync((Guardian)null);

            // Act
            var result = await _controller.GetGuardianById(guardianId, orgId, subscription);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task AddGuardian_WhenValidGuardian_ReturnsCreatedAtAction()
        {
            // Arrange
            var guardianId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var guardian = new Guardian
            {
                GuardianId = guardianId,
                GuardianName = "John Doe",
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockCommandHandler
                .Setup(c => c.AddGuardianAsync(It.IsAny<List<Guardian>>(), orgId, subscription))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.AddGuardian(guardian);

            // Assert
            Assert.That(result, Is.InstanceOf<CreatedAtActionResult>());
            var createdResult = result as CreatedAtActionResult;
            Assert.That(createdResult.StatusCode, Is.EqualTo(201));
            Assert.That(createdResult.ActionName, Is.EqualTo(nameof(GuardianController.GetGuardianById)));
            Assert.That(createdResult.RouteValues["id"], Is.EqualTo(guardianId));
            Assert.That(createdResult.Value, Is.Not.Null);

            // The controller returns an anonymous object with specific fields, not the full Guardian object
            var returnedValue = createdResult.Value;
            var guardianIdProperty = returnedValue.GetType().GetProperty("GuardianId");
            var returnedGuardianId = guardianIdProperty?.GetValue(returnedValue);
            Assert.That(returnedGuardianId, Is.EqualTo(guardian.GuardianId));
        }

        [Test]
        public async Task AddGuardian_WhenNullGuardian_ReturnsBadRequest()
        {
            // Act
            var result = await _controller.AddGuardian(null);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult.StatusCode, Is.EqualTo(400));
        }

        [Test]
        public async Task AddGuardian_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var guardian = new Guardian
            {
                GuardianId = Guid.NewGuid(),
                GuardianName = "John Doe"
            };

            _mockCommandHandler
                .Setup(c => c.AddGuardianAsync(It.IsAny<List<Guardian>>(), It.IsAny<Guid>(), It.IsAny<bool>()))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.AddGuardian(guardian);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task DeleteGuardian_WhenGuardianExists_ReturnsNoContent()
        {
            // Arrange
            var guardianId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockCommandHandler
                .Setup(c => c.DeleteGuardianAsync(guardianId, orgId, subscription))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.DeleteGuardian(guardianId, orgId, subscription);

            // Assert
            Assert.That(result, Is.InstanceOf<NoContentResult>());
            var noContentResult = result as NoContentResult;
            Assert.That(noContentResult.StatusCode, Is.EqualTo(204));
        }

        [Test]
        public async Task DeleteGuardian_WhenGuardianDoesNotExist_ReturnsNotFound()
        {
            // Arrange
            var guardianId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockCommandHandler
                .Setup(c => c.DeleteGuardianAsync(guardianId, orgId, subscription))
                .ThrowsAsync(new KeyNotFoundException());

            // Act
            var result = await _controller.DeleteGuardian(guardianId, orgId, subscription);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task DeleteGuardian_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var guardianId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockCommandHandler
                .Setup(c => c.DeleteGuardianAsync(guardianId, orgId, subscription))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.DeleteGuardian(guardianId, orgId, subscription);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task UpdateGuardian_WhenValidGuardian_ReturnsNoContent()
        {
            // Arrange
            var guardianId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var guardian = new Guardian
            {
                GuardianId = guardianId,
                GuardianName = "Updated Guardian",
                GuardianRelationship = "Parent",
                GuardianSex = "Male",
                GuardianCity = "Anytown",
                GuardianState = "CA",
                GuardianCountry = "USA",
                GuardianPhone = "************",
                GuardianEmail = "<EMAIL>",
                GuardianAddress = "123 Main St",
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockCommandHandler
                .Setup(c => c.UpdateGuardianAsync(guardian, orgId, subscription))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.UpdateGuardian(guardianId, guardian);

            // Assert
            Assert.That(result, Is.InstanceOf<NoContentResult>());
            var noContentResult = result as NoContentResult;
            Assert.That(noContentResult.StatusCode, Is.EqualTo(204));
        }

        [Test]
        public async Task UpdateGuardian_WhenNullGuardian_ReturnsBadRequest()
        {
            // Arrange
            var guardianId = Guid.NewGuid();

            // Act
            var result = await _controller.UpdateGuardian(guardianId, null!);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult.StatusCode, Is.EqualTo(400));
        }

        [Test]
        public async Task UpdateGuardian_WhenIdMismatch_ReturnsBadRequest()
        {
            // Arrange
            var guardianId = Guid.NewGuid();
            var differentId = Guid.NewGuid();
            var guardian = new Guardian
            {
                GuardianId = differentId,
                GuardianName = "John Doe"
            };

            // Act
            var result = await _controller.UpdateGuardian(guardianId, guardian);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult.StatusCode, Is.EqualTo(400));
        }

        [Test]
        public async Task UpdateGuardian_WhenGuardianDoesNotExist_ReturnsNotFound()
        {
            // Arrange
            var guardianId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var guardian = new Guardian
            {
                GuardianId = guardianId,
                GuardianName = "John Doe",
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockCommandHandler
                .Setup(c => c.UpdateGuardianAsync(guardian, orgId, subscription))
                .ThrowsAsync(new KeyNotFoundException());

            // Act
            var result = await _controller.UpdateGuardian(guardianId, guardian);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task UpdateGuardian_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var guardianId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var guardian = new Guardian
            {
                GuardianId = guardianId,
                GuardianName = "John Doe",
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockCommandHandler
                .Setup(c => c.UpdateGuardianAsync(guardian, orgId, subscription))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.UpdateGuardian(guardianId, guardian);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult.StatusCode, Is.EqualTo(500));
        }
    }
}
