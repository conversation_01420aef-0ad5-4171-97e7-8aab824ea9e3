using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using MemberServiceApi.Controllers;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using MemberServiceBusinessLayer;
using System.Linq;

namespace MemberServiceApiControllerTestCases
{
    [TestFixture]
    public class PlanTypeControllerTests
    {
        private Mock<IPlanTypeQueryHandler<PlanType>> _mockQueryHandler;
        private Mock<IPlanTypeCommandHandler<PlanType>> _mockCommandHandler;
        private Mock<ILogger<PlanTypeController>> _mockLogger;
        private Mock<IStringLocalizer<PlanTypeController>> _mockLocalizer;
        private PlanTypeController _controller;

        [SetUp]
        public void Setup()
        {
            _mockQueryHandler = new Mock<IPlanTypeQueryHandler<PlanType>>();
            _mockCommandHandler = new Mock<IPlanTypeCommandHandler<PlanType>>();
            _mockLogger = new Mock<ILogger<PlanTypeController>>();
            _mockLocalizer = new Mock<IStringLocalizer<PlanTypeController>>();

            // Setup localizer to return the key when requested
            _mockLocalizer.Setup(l => l[It.IsAny<string>()]).Returns((string key) => new LocalizedString(key, key));

            _controller = new PlanTypeController(
                _mockQueryHandler.Object,
                _mockCommandHandler.Object,
                _mockLogger.Object,
                _mockLocalizer.Object
            );
        }

        [Test]
        public async Task GetAllPlanTypes_WhenPlanTypesExist_ReturnsOkWithPlanTypes()
        {
            // Arrange
            var mockPlanTypes = new List<PlanType>
            {
                new PlanType { Id = Guid.NewGuid(), PlanName = "Plan Type 1" },
                new PlanType { Id = Guid.NewGuid(), PlanName = "Plan Type 2" }
            };

            _mockQueryHandler
                .Setup(q => q.GetAllPlanTypesAsync())
                .ReturnsAsync(mockPlanTypes);

            // Act
            var result = await _controller.GetAllPlanTypes();

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult?.StatusCode, Is.EqualTo(200));
            Assert.That(okResult?.Value, Is.EqualTo(mockPlanTypes));
        }

        [Test]
        public async Task GetAllPlanTypes_WhenNoPlanTypesExist_ReturnsNotFound()
        {
            // Arrange
            _mockQueryHandler
                .Setup(q => q.GetAllPlanTypesAsync())
                .ReturnsAsync(new List<PlanType>());

            // Act
            var result = await _controller.GetAllPlanTypes();

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult?.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task GetAllPlanTypes_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            _mockQueryHandler
                .Setup(q => q.GetAllPlanTypesAsync())
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetAllPlanTypes();

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task GetPlanTypeById_WhenPlanTypeExists_ReturnsOkWithPlanType()
        {
            // Arrange
            var planTypeId = Guid.NewGuid();
            var mockPlanType = new PlanType
            {
                Id = planTypeId,
                PlanName = "Test Plan Type"
            };

            _mockQueryHandler
                .Setup(q => q.GetPlanTypeByIdAsync(planTypeId))
                .ReturnsAsync(mockPlanType);

            // Act
            var result = await _controller.GetPlanTypeById(planTypeId);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult?.StatusCode, Is.EqualTo(200));
            Assert.That(okResult?.Value, Is.EqualTo(mockPlanType));
        }

        [Test]
        public async Task GetPlanTypeById_WhenPlanTypeDoesNotExist_ReturnsNotFound()
        {
            // Arrange
            var planTypeId = Guid.NewGuid();

            _mockQueryHandler
                .Setup(q => q.GetPlanTypeByIdAsync(planTypeId))
                .ReturnsAsync((PlanType)null);

            // Act
            var result = await _controller.GetPlanTypeById(planTypeId);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult?.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task GetPlanTypeById_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var planTypeId = Guid.NewGuid();

            _mockQueryHandler
                .Setup(q => q.GetPlanTypeByIdAsync(planTypeId))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetPlanTypeById(planTypeId);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }

        // Note: The GetPlanTypeByName method doesn't exist in the controller
        // We're removing these tests as they don't match the actual controller implementation

        [Test]
        public async Task AddPlanType_WhenValidPlanType_ReturnsCreatedAtAction()
        {
            // Arrange
            var planTypeId = Guid.NewGuid();
            var planType = new PlanType
            {
                Id = planTypeId,
                PlanName = "New Plan Type"
            };

            _mockCommandHandler
                .Setup(c => c.AddPlanTypeAsync(planType))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.AddPlanType(planType);

            // Assert
            Assert.That(result, Is.InstanceOf<CreatedAtActionResult>());
            var createdResult = result as CreatedAtActionResult;
            Assert.That(createdResult?.StatusCode, Is.EqualTo(201));
            Assert.That(createdResult?.ActionName, Is.EqualTo(nameof(PlanTypeController.GetPlanTypeById)));
            Assert.That(createdResult?.RouteValues["id"], Is.EqualTo(planTypeId));
            Assert.That(createdResult?.Value, Is.EqualTo(planType));
        }

        [Test]
        public async Task AddPlanType_WhenNullPlanType_ReturnsBadRequest()
        {
            // Act
            var result = await _controller.AddPlanType(null);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult?.StatusCode, Is.EqualTo(400));
        }

        [Test]
        public async Task AddPlanType_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var planTypeId = Guid.NewGuid();
            var planType = new PlanType
            {
                Id = planTypeId,
                PlanName = "New Plan Type"
            };

            _mockCommandHandler
                .Setup(c => c.AddPlanTypeAsync(planType))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.AddPlanType(planType);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task UpdatePlanType_WhenValidPlanType_ReturnsNoContent()
        {
            // Arrange
            var planTypeId = Guid.NewGuid();
            var planType = new PlanType
            {
                Id = planTypeId,
                PlanName = "Updated Plan Type"
            };

            _mockCommandHandler
                .Setup(c => c.UpdatePlanTypeAsync(planType))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.UpdatePlanType(planTypeId, planType);

            // Assert
            Assert.That(result, Is.InstanceOf<NoContentResult>());
            var noContentResult = result as NoContentResult;
            Assert.That(noContentResult?.StatusCode, Is.EqualTo(204));
        }

        [Test]
        public async Task UpdatePlanType_WhenNullPlanType_ReturnsBadRequest()
        {
            // Arrange
            var planTypeId = Guid.NewGuid();

            // Act
            var result = await _controller.UpdatePlanType(planTypeId, null);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult?.StatusCode, Is.EqualTo(400));
        }

        [Test]
        public async Task UpdatePlanType_WhenIdMismatch_ReturnsBadRequest()
        {
            // Arrange
            var planTypeId = Guid.NewGuid();
            var differentId = Guid.NewGuid();
            var planType = new PlanType
            {
                Id = differentId,
                PlanName = "Updated Plan Type"
            };

            // Act
            var result = await _controller.UpdatePlanType(planTypeId, planType);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult?.StatusCode, Is.EqualTo(400));
        }

        [Test]
        public async Task UpdatePlanType_WhenPlanTypeDoesNotExist_ReturnsNotFound()
        {
            // Arrange
            var planTypeId = Guid.NewGuid();
            var planType = new PlanType
            {
                Id = planTypeId,
                PlanName = "Updated Plan Type"
            };

            _mockCommandHandler
                .Setup(c => c.UpdatePlanTypeAsync(planType))
                .ThrowsAsync(new KeyNotFoundException());

            // Act
            var result = await _controller.UpdatePlanType(planTypeId, planType);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult?.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task UpdatePlanType_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var planTypeId = Guid.NewGuid();
            var planType = new PlanType
            {
                Id = planTypeId,
                PlanName = "Updated Plan Type"
            };

            _mockCommandHandler
                .Setup(c => c.UpdatePlanTypeAsync(planType))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.UpdatePlanType(planTypeId, planType);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task DeletePlanType_WhenPlanTypeExists_ReturnsNoContent()
        {
            // Arrange
            var planTypeId = Guid.NewGuid();

            _mockCommandHandler
                .Setup(c => c.DeletePlanTypeAsync(planTypeId))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.DeletePlanType(planTypeId);

            // Assert
            Assert.That(result, Is.InstanceOf<NoContentResult>());
            var noContentResult = result as NoContentResult;
            Assert.That(noContentResult?.StatusCode, Is.EqualTo(204));
        }

        [Test]
        public async Task DeletePlanType_WhenPlanTypeDoesNotExist_ReturnsNotFound()
        {
            // Arrange
            var planTypeId = Guid.NewGuid();

            _mockCommandHandler
                .Setup(c => c.DeletePlanTypeAsync(planTypeId))
                .ThrowsAsync(new KeyNotFoundException());

            // Act
            var result = await _controller.DeletePlanType(planTypeId);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult?.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task DeletePlanType_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var planTypeId = Guid.NewGuid();

            _mockCommandHandler
                .Setup(c => c.DeletePlanTypeAsync(planTypeId))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.DeletePlanType(planTypeId);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }
    }
}
