﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Data.SqlClient;
using Contracts;
using Microsoft.Extensions.Localization;
using Microsoft.EntityFrameworkCore;
using System;
using DataAccessLayer.Context;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Identity.Web.Resource;
using MemberServiceBusinessLayer;

namespace MemberServiceApi.Controllers
{
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes")]
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class LicensesController : ControllerBase
    {
        private readonly ILicenseCommandHandler<ProductLicense> _licenseDataHandler;
        private readonly ILicenseQueryHandler<ProductLicense> _licenseQueryHandler;
        private readonly ILogger<LicensesController> _logger;
        private readonly IStringLocalizer<LicensesController> _localizer; 


        public LicensesController(
            ILicenseCommandHandler<ProductLicense> dataHandler,
            ILicenseQueryHandler<ProductLicense> queryHandler,
            ILogger<LicensesController> logger,
            IStringLocalizer<LicensesController> localizer
            )
        {
            _licenseDataHandler = dataHandler;
            _licenseQueryHandler = queryHandler;
            _logger = logger;
            _localizer = localizer;
        }

        [HttpGet]
        [Route("{orgId:guid}/{Subscription}")]
        public async Task<ActionResult<IEnumerable<ProductLicense>>> Get(Guid OrgId, bool Subscription)
        {
            try
            {
                var licenses = await _licenseQueryHandler.GetLicense(OrgId, Subscription);
                return Ok(licenses);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                return StatusCode(500);
            }
        }

        [HttpGet("{id:guid}/{OrgID:Guid}/{Subscription}")]
        public async Task<ActionResult<ProductLicense>> GetById(Guid id, Guid OrgID, bool Subscription)
        {
            try
            {
                var license = await _licenseQueryHandler.GetLicenseById(id, OrgID, Subscription);
                return Ok(license);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                return StatusCode(500);
            }
        }

        [HttpPut("{id:guid}")]
        public async Task<IActionResult> UpdateById(Guid id, [FromBody] ProductLicense license)
        {
            if (license == null || license.Id != id)
            {
                return BadRequest(_localizer["InvalidLicense"]);
            }

            try
            {
                await _licenseDataHandler.UpdateLicense(license, license.OrganizationID, license.Subscription);
                return Ok(_localizer["UpdateSuccessful"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["UpdateLogError"]);
                return StatusCode(500);
            }
        }

        [HttpDelete("{id:guid}/{OrgID:Guid}/{Subscription}")]

        public async Task<IActionResult> DeleteById(Guid id, Guid OrgID, bool Subscription)
        {
            try
            {
                await _licenseDataHandler.DeleteLicenseById(id, OrgID, Subscription);
                return Ok(_localizer["SuccessfulDeletion"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["DeleteLogError"]);
                return StatusCode(500);
            }
        }

        [HttpDelete("(entity)")]
        public async Task<IActionResult> DeleteByEntity([FromBody] ProductLicense license)
        {
            try
            {
                await _licenseDataHandler.DeleteLicenseByEntity(license, license.OrganizationID, license.Subscription);
                return Ok(_localizer["DeleteSuccessful"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["DeleteLogError"]);
                return StatusCode(500);
            }
        }

        [HttpPost]
        [Route("registration")]
        public async Task<IActionResult> Registration([FromBody] List<ProductLicense> registrations)
        {
            if (registrations == null || registrations.Count == 0)
            {
                return BadRequest(_localizer["NoLicense"]);
            }

            try
            {
                await _licenseDataHandler.AddLicense(registrations);
                return Ok(_localizer["SuccessfulRegistration"]);
            }
            catch (SqlException ex)
            {
                return StatusCode(500, _localizer["DatabaseError"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["PostLogError"]);
                return StatusCode(500);
            }
        }

        [HttpPut("updateAccess")]
        public async Task<IActionResult> UpdateAccess(List<ProductLicense> licenseAccessUpdates)
        {
            if (licenseAccessUpdates == null)
            {
                return BadRequest(_localizer["AccessError"]);
            }
            var success = await _licenseDataHandler.UpdateLicenseAccessAsync(licenseAccessUpdates);

            if (success)
            {
                return Ok(new { Message = _localizer["AccessUpdateSuccessful"] });
            }
            else
            {
                return NotFound(_localizer["Access record not found"]);
            }
        }

    }
}

