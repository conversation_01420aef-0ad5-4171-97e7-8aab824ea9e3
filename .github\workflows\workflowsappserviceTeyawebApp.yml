name: Build and Deploy ASP.NET App to Azure Web App

on:
  push:
    branches:
      - main
  workflow_dispatch:

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
      # ✅ Step 1: Checkout source code
      - name: Checkout code
        uses: actions/checkout@v4

      # ✅ Step 2: Setup .NET SDK
      - name: Setup .NET SDK
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: '8.x'  # Match your project's .NET version

      # ✅ Step 3: Restore dependencies
      - name: Restore dependencies
        run: dotnet restore Clients/TeyaWebClient/TeyaWebApp/TeyaWebApp/TeyaWebApp.csproj

      # ✅ Step 4: Build the app
      - name: Build project
        run: dotnet build Clients/TeyaWebClient/TeyaWebApp/TeyaWebApp/TeyaWebApp.csproj --configuration Release --no-restore

      # ✅ Step 5: Publish the app
      - name: Publish project
        run: dotnet publish Clients/TeyaWebClient/TeyaWebApp/TeyaWebApp/TeyaWebApp.csproj --configuration Release --output ./publish --no-build

      # ✅ Step 6: Deploy to Azure Web App using secret
      - name: Deploy to Azure Web App
        uses: azure/webapps-deploy@v3
        with:
          app-name: Teya-Webapp-Dev
          publish-profile: ${{ secrets.AZURE_WEBAPP_PUBLISH_PROFILE }}
          package: ./publish
