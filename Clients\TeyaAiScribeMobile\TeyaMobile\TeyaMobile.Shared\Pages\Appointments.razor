﻿@page "/appointments"
@using TeyaMobileModel.Model
@using TeyaMobileViewModel.ViewModel
@using Syncfusion.Blazor.Schedule
@using Syncfusion.Blazor.Calendars
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Popups
@inject IAppointmentService AppointmentService
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime
@implements IDisposable

<PageTitle>Today's Schedule</PageTitle>

<div class="doctor-scheduler">
    <!-- Header with Stats and Date Picker -->
    <div class="scheduler-header">
        <!-- Stats Section (Left) -->
        <div class="stats-container">
            <div class="stat-chip total @(activeFilter == FilterType.Total ? "active" : "")"
                 @onclick="() => ApplyFilter(FilterType.Total)">
                <span class="stat-count">@totalAppointments</span>
                <span class="stat-label">TOTAL</span>
            </div>
            <div class="stat-chip completed @(activeFilter == FilterType.Completed ? "active" : "")"
                 @onclick="() => ApplyFilter(FilterType.Completed)">
                <span class="stat-count">@completedAppointments</span>
                <span class="stat-label">DONE</span>
            </div>
            <div class="stat-chip pending @(activeFilter == FilterType.Pending ? "active" : "")"
                 @onclick="() => ApplyFilter(FilterType.Pending)">
                <span class="stat-count">@pendingAppointments</span>
                <span class="stat-label">PENDING</span>
            </div>
        </div>

        <!-- Date Picker Section (Right) -->
        <div class="date-picker-section" @onclick="OpenDatePicker">
            <div class="selected-date">
                <span class="month-day">@selectedDate.ToString("MMM dd")</span>
                <span class="day-name">@selectedDate.ToString("ddd")</span>
            </div>
            <i class="e-icons e-chevron-down date-dropdown-icon"></i>
        </div>
    </div>

    <!-- Loading Indicator -->
    @if (isLoading)
    {
        <div class="loading-container">
            <div class="loading-spinner"></div>
            <span>Loading appointments...</span>
        </div>
    }

    <!-- Main Scheduler -->
    <div class="scheduler-container">
        <Syncfusion.Blazor.Schedule.SfSchedule TValue="AppointmentData"
                                               @ref="ScheduleRef"
                                               SelectedDate="@selectedDate"
                                               CurrentView="View.Day"
                                               Height="@schedulerHeight"
                                               StartHour="00:00"
                                               EndHour="23:00"
                                               ShowQuickInfo="false"
                                               ShowHeaderBar="false"
                                               AllowDragAndDrop="false"
                                               AllowResizing="false">
            <ScheduleViews>
                <ScheduleView Option="View.Day">
                    <ScheduleViewTimeScale Enable="true" Interval="30" SlotCount="2"></ScheduleViewTimeScale>
                </ScheduleView>
            </ScheduleViews>
            @* <ScheduleEventSettings DataSource="@appointments"
                                   AllowAdding="false"
                                   AllowDeleting="false"
                                   AllowEditing="false"> *@
            <ScheduleEventSettings DataSource="@filteredAppointments"
                                   AllowAdding="false"
                                   AllowDeleting="false"
                                   AllowEditing="false">
                <Template>
                    @{
                        var appointment = context as AppointmentData;
                    }
                    <div class="appointment-card @GetAppointmentStatusClass(appointment)"
                         @onclick="() => OnAppointmentCardClick(appointment)">
                        <div class="appointment-header">
                            <span class="patient-name">@appointment.PatientName</span>
                            <div class="time-status">
                                <span class="appointment-time">@appointment.StartTime.ToString("HH:mm")</span>
                                <span class="status-dot @appointment.VisitStatus.ToLower().Replace(" ", "-")"></span>
                            </div>
                        </div>
                        <div class="appointment-details">
                            <div class="detail-row">
                                <span class="visit-type">@appointment.VisitType</span>
                                @if (!string.IsNullOrEmpty(appointment.RoomNumber))
                                {
                                    <span class="room-number">Room @appointment.RoomNumber</span>
                                }
                            </div>
                            @if (!string.IsNullOrEmpty(appointment.Reason))
                            {
                                <div class="reason">@appointment.Reason</div>
                            }
                        </div>
                    </div>
                </Template>
            </ScheduleEventSettings>
            <ScheduleEvents TValue="AppointmentData" OnEventClick="OnAppointmentClick"></ScheduleEvents>
        </Syncfusion.Blazor.Schedule.SfSchedule>
    </div>

    <!-- Error Message -->
    @if (!string.IsNullOrEmpty(errorMessage))
    {
        <div class="error-container">
            <div class="error-message">
                <i class="e-icons e-error"></i>
                <span>@errorMessage</span>
                <button class="retry-btn" @onclick="RetryLoadAppointments">Retry</button>
            </div>
        </div>
    }
</div>

<!-- Compact Date Picker Popup -->
<Syncfusion.Blazor.Popups.SfDialog @ref="DatePickerDialog"
                                   Width="300px"
                                   Height="auto"
                                   IsModal="true"
                                   ShowCloseIcon="true"
                                   Visible="@isDatePickerVisible"
                                   VisibleChanged="@OnDatePickerVisibilityChanged"
                                   CssClass="compact-date-picker-dialog"
                                   Position="@dialogPosition">
    <DialogTemplates>
        <Content>
            <div class="compact-calendar-container">
                <Syncfusion.Blazor.Calendars.SfCalendar TValue="DateTime"
                                                        Value="@selectedDate"
                                                        ValueChanged="@OnDateSelected"
                                                        CssClass="compact-calendar">
                </Syncfusion.Blazor.Calendars.SfCalendar>
            </div>
        </Content>
    </DialogTemplates>
</Syncfusion.Blazor.Popups.SfDialog>

<style>
    .doctor-scheduler {
        height: 100vh;
        display: flex;
        flex-direction: column;
        background: #f5f5f5;
        overflow: hidden;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    .scheduler-header {
        background: linear-gradient(135deg, #6B73FF 0%, #9D50BB 100%);
        padding: 16px 20px;
        color: white;
        display: flex;
        align-items: center;
        justify-content: space-between;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        min-height: 80px;
    }

    .stats-container {
        display: flex;
        gap: 16px;
        align-items: center;
    }

    .stat-chip {
        text-align: center;
        min-width: 60px;
    }

    .stat-count {
        display: block;
        font-size: 24px;
        font-weight: 700;
        line-height: 1;
        margin-bottom: 4px;
    }

    .stat-label {
        font-size: 11px;
        opacity: 0.8;
        font-weight: 500;
        letter-spacing: 0.5px;
    }

    /* Date Picker Section (Top Right) */
    .date-picker-section {
        display: flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;
        padding: 12px 16px;
        border-radius: 12px;
        background: rgba(255, 255, 255, 0.15);
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: all 0.2s ease;
        backdrop-filter: blur(10px);
        min-width: 100px;
    }

        .date-picker-section:hover {
            background: rgba(255, 255, 255, 0.25);
            border-color: rgba(255, 255, 255, 0.4);
            transform: translateY(-1px);
        }

    .selected-date {
        text-align: center;
    }

    .month-day {
        display: block;
        font-size: 16px;
        font-weight: 600;
        line-height: 1.2;
    }

    .day-name {
        display: block;
        font-size: 12px;
        opacity: 0.8;
        line-height: 1;
        margin-top: 2px;
    }

    .date-dropdown-icon {
        font-size: 14px;
        opacity: 0.7;
        transition: transform 0.2s ease;
    }

    .date-picker-section:hover .date-dropdown-icon {
        transform: translateY(1px);
    }

    .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 40px;
        background: #fff;
        margin: 20px;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    }

    .loading-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid #f3f3f3;
        border-top: 3px solid #6B73FF;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 16px;
    }

    @@keyframes spin {
        0%

    {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }

    }

    .scheduler-container {
        flex: 1;
        overflow: hidden;
        background: #fff;
        border-radius: 20px 20px 0 0;
        margin-top: -10px;
        box-shadow: 0 -5px 20px rgba(0,0,0,0.1);
    }

    .appointment-card {
        padding: 12px;
        border-radius: 10px;
        margin: 3px;
        border-left: 4px solid #6B73FF;
        background: linear-gradient(135deg, #fff 0%, #f8f9ff 100%);
        box-shadow: 0 2px 8px rgba(107, 115, 255, 0.1);
        cursor: pointer;
        transition: all 0.3s ease;
    }

        .appointment-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(107, 115, 255, 0.2);
        }

        .appointment-card.completed {
            border-left-color: #00C851;
            background: linear-gradient(135deg, #fff 0%, #f0fff4 100%);
        }

        .appointment-card.pending {
            border-left-color: #FF8800;
            background: linear-gradient(135deg, #fff 0%, #fff8f0 100%);
        }

        .appointment-card.confirmed {
            border-left-color: #007bff;
            background: linear-gradient(135deg, #fff 0%, #f0f8ff 100%);
        }

    .appointment-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
    }

    .patient-name {
        font-weight: 600;
        font-size: 15px;
        color: #333;
        flex: 1;
    }

    .time-status {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .appointment-time {
        font-size: 12px;
        font-weight: 600;
        color: #6B73FF;
        background: rgba(107, 115, 255, 0.1);
        padding: 4px 8px;
        border-radius: 6px;
    }

    .status-dot {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background: #6B73FF;
    }

        .status-dot.completed {
            background: #00C851;
        }

        .status-dot.pending {
            background: #FF8800;
        }

        .status-dot.confirmed {
            background: #007bff;
        }

    .appointment-details {
        font-size: 12px;
        color: #666;
    }

    .detail-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 4px;
    }

    .visit-type {
        font-weight: 500;
        color: #333;
    }

    .room-number {
        font-size: 11px;
        background: rgba(107, 115, 255, 0.1);
        color: #6B73FF;
        padding: 2px 6px;
        border-radius: 4px;
        font-weight: 500;
    }

    .reason {
        font-size: 11px;
        color: #888;
        font-style: italic;
    }

    /* Compact Date Picker Dialog */
    .compact-date-picker-dialog .e-dialog {
        border-radius: 16px !important;
        box-shadow: 0 10px 40px rgba(0,0,0,0.2) !important;
        border: none !important;
    }

    .compact-calendar-container {
        padding: 0;
    }

    .compact-calendar {
        border: none !important;
        font-family: inherit !important;
    }

        .compact-calendar .e-calendar .e-header {
            background: #6B73FF !important;
            color: white !important;
            border-radius: 12px 12px 0 0 !important;
        }

        .compact-calendar .e-calendar .e-content td.e-selected {
            background: #6B73FF !important;
            color: white !important;
        }

    .error-container {
        position: fixed;
        bottom: 20px;
        left: 20px;
        right: 20px;
        z-index: 1000;
    }

    .error-message {
        background: #ffebee;
        color: #c62828;
        padding: 12px 16px;
        border-radius: 8px;
        border-left: 4px solid #f44336;
        display: flex;
        align-items: center;
        gap: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    .retry-btn {
        background: #f44336;
        color: white;
        border: none;
        padding: 6px 12px;
        border-radius: 6px;
        cursor: pointer;
        font-size: 12px;
        margin-left: auto;
    }

    .stat-chip.active {
        transform: scale(1.05);
        box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        border: 2px solid #6B73FF;
    }

    .stat-chip {
        transition: all 0.2s ease;
        cursor: pointer;
    }

    /* Mobile Responsive */
    @@media (max-width: 768px) {
        .scheduler-header

    {
        padding: 12px 16px;
        min-height: 70px;
    }

    .stats-container {
        gap: 12px;
    }

    .stat-chip {
        min-width: 50px;
    }

    .stat-count {
        font-size: 20px;
    }

    .stat-label {
        font-size: 10px;
    }

    .date-picker-section {
        padding: 8px 12px;
        min-width: 80px;
    }

    .month-day {
        font-size: 14px;
    }

    .day-name {
        font-size: 11px;
    }

    }

    /* Syncfusion Scheduler Overrides */
    .e-schedule .e-day-view .e-time-cells-wrap {
        width: 60px !important;
    }

    .e-schedule .e-content-wrap {
        overflow-y: auto !important;
        max-height: calc(100vh - 160px) !important;
    }

    .e-schedule .e-day-view .e-appointment {
        padding: 0 !important;
        margin: 2px !important;
        border-radius: 8px !important;
    }
</style>
