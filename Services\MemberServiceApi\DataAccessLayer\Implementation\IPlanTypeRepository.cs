﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Contracts;

namespace MemberServiceDataAccessLayer.Implementation
{
    public interface IPlanTypeRepository : IGenericRepository<PlanType>
    {
        Task AddAsync(PlanType plan);
        Task UpdateAsync(PlanType plan);
        Task DeleteByIdAsync(Guid id);
        Task<PlanType> GetByIdAsync(Guid id);
        Task<List<PlanType>> GetAllAsync();
    }
}
