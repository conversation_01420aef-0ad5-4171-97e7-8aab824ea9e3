using NUnit.Framework;
using Moq;
using MemberServiceBusinessLayer.CommandHandler;
using MemberServiceDataAccessLayer.Implementation;
using MemberServiceDataAccessLayer;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq.Expressions;

namespace MemberServiceApiBusinessLayerTestCases.CommandHandlerTests
{
    [TestFixture]
    public class VisitTypeCommandHandlerTests
    {
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<IVisitTypeRepository> _mockVisitTypeRepository;
        private VisitTypeCommandHandler _visitTypeCommandHandler;

        [SetUp]
        public void Setup()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockVisitTypeRepository = new Mock<IVisitTypeRepository>();

            _mockUnitOfWork.Setup(u => u.VisitTypeRepository).Returns(_mockVisitTypeRepository.Object);

            _visitTypeCommandHandler = new VisitTypeCommandHandler(
                _mockVisitTypeRepository.Object,
                _mockUnitOfWork.Object
            );
        }

        [Test]
        public async Task AddVisitTypeAsync_WhenVisitTypeDoesNotExist_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var visitType = new VisitType
            {
                ID = Guid.NewGuid(),
                VisitName = "Visit Type 1",
                CPTCode = "12345",
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockVisitTypeRepository
                .Setup(r => r.GetFirstOrDefaultAsync(It.IsAny<Expression<Func<VisitType, bool>>>()))
                .ReturnsAsync((VisitType)null);

            _mockVisitTypeRepository
                .Setup(r => r.AddAsync(It.IsAny<IEnumerable<VisitType>>(), orgId, subscription))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _visitTypeCommandHandler.AddVisitTypeAsync(visitType, orgId, subscription);

            // Assert
            _mockVisitTypeRepository.Verify(r => r.AddAsync(It.Is<IEnumerable<VisitType>>(vts => vts.Contains(visitType)), orgId, subscription), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task AddVisitTypeAsync_WhenInactiveVisitTypeExists_ShouldReactivateAndSaveChanges()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var visitType = new VisitType
            {
                ID = Guid.NewGuid(),
                VisitName = "Visit Type 1",
                CPTCode = "12345",
                OrganizationId = orgId,
                Subscription = subscription,
                IsActive = true
            };

            var existingVisitType = new VisitType
            {
                ID = Guid.NewGuid(),
                VisitName = "Visit Type 1",
                CPTCode = "12345",
                OrganizationId = orgId,
                Subscription = subscription,
                IsActive = false
            };

            _mockVisitTypeRepository
                .Setup(r => r.GetFirstOrDefaultAsync(It.IsAny<Expression<Func<VisitType, bool>>>()))
                .ReturnsAsync(existingVisitType);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _visitTypeCommandHandler.AddVisitTypeAsync(visitType, orgId, subscription);

            // Assert
            Assert.That(existingVisitType.IsActive, Is.True);
            _mockVisitTypeRepository.Verify(r => r.AddAsync(It.IsAny<IEnumerable<VisitType>>(), orgId, subscription), Times.Never);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateCptCodeAsync_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            string visitName = "Visit Type 1";
            string newCptCode = "12345";
            bool subscription = false;

            _mockVisitTypeRepository
                .Setup(r => r.UpdateCptCodeAsync(orgId, visitName, newCptCode))
                .ReturnsAsync(true);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            var result = await _visitTypeCommandHandler.UpdateCptCodeAsync(orgId, visitName, newCptCode, subscription);

            // Assert
            Assert.That(result, Is.True);
            _mockVisitTypeRepository.Verify(r => r.UpdateCptCodeAsync(orgId, visitName, newCptCode), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteVisitTypeAsync_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            string visitName = "Visit Type 1";
            bool subscription = false;

            _mockVisitTypeRepository
                .Setup(r => r.DeleteByNameAsync(visitName, orgId, subscription))
                .ReturnsAsync(true);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            var result = await _visitTypeCommandHandler.DeleteVisitTypeAsync(orgId, visitName, subscription);

            // Assert
            Assert.That(result, Is.True);
            _mockVisitTypeRepository.Verify(r => r.DeleteByNameAsync(visitName, orgId, subscription), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public void Constructor_ShouldThrowArgumentNullException_WhenVisitTypeRepositoryIsNull()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new VisitTypeCommandHandler(null, Mock.Of<IUnitOfWork>()));
        }

        [Test]
        public void Constructor_ShouldThrowArgumentNullException_WhenUnitOfWorkIsNull()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new VisitTypeCommandHandler(Mock.Of<IVisitTypeRepository>(), null));
        }
    }
}
