﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
	<packageSources>
		<add key="nuget.org" value="https://api.nuget.org/v3/index.json" />
		<add key="AzureDevOps" value="https://pkgs.dev.azure.com/TeyaHealthDevOps/Infrastructure/_packaging/TeyaHealth/nuget/v3/index.json" />
	</packageSources>
	<packageSourceCredentials>
		<AzureDevOps>
			<add key="Username" value="<PERSON>esh Doddi" />
			<add key="ClearTextPassword" value="3lbbyGMXn4AAJDnttyDzZ4wEbOWrzhxta3gip3YniaDqdpASKyc3JQQJ99BDACAAAAASlSHjAAASAZDO1vWu" />
		</AzureDevOps>
	</packageSourceCredentials>
</configuration>
