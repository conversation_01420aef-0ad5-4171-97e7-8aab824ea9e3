using NUnit.Framework;
using Moq;
using MemberServiceBusinessLayer.CommandHandler;
using MemberServiceDataAccessLayer.Implementation;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MemberServiceApiBusinessLayerTestCases.CommandHandlerTests
{
    [TestFixture]
    public class PageRoleMappingCommandHandlerTests
    {
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<IPageRoleMappingRepository> _mockPageRoleMappingRepository;
        private PageRoleMappingCommandHandler _pageRoleMappingCommandHandler;

        [SetUp]
        public void Setup()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockPageRoleMappingRepository = new Mock<IPageRoleMappingRepository>();

            _mockUnitOfWork.Setup(u => u.PageRoleMappingRepository).Returns(_mockPageRoleMappingRepository.Object);

            _pageRoleMappingCommandHandler = new PageRoleMappingCommandHandler(_mockUnitOfWork.Object);
        }

        [Test]
        public async Task AddPageRoleMappingAsync_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var pageRoleMapping = new PageRoleMapping 
            { 
                Id = Guid.NewGuid(), 
                PagePath = "/dashboard",
                RoleId = Guid.NewGuid(),
                RoleName = "Admin",
                OrganizationId = orgId,
                CreatedBy = Guid.NewGuid(),
                CreatedDate = DateTime.Now,
                IsActive = true,
                HasAccess = true,
                Subscription = subscription
            };

            _mockPageRoleMappingRepository
                .Setup(r => r.AddAsync(It.IsAny<List<PageRoleMapping>>(), orgId, subscription))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _pageRoleMappingCommandHandler.AddPageRoleMappingAsync(pageRoleMapping, orgId, subscription);

            // Assert
            _mockPageRoleMappingRepository.Verify(r => r.AddAsync(It.Is<List<PageRoleMapping>>(list => 
                list.Count == 1 && list[0] == pageRoleMapping), orgId, subscription), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdatePageRoleMappingAsync_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var pageRoleMapping = new PageRoleMapping 
            { 
                Id = Guid.NewGuid(), 
                PagePath = "/dashboard",
                RoleId = Guid.NewGuid(),
                RoleName = "Admin",
                OrganizationId = orgId,
                CreatedBy = Guid.NewGuid(),
                CreatedDate = DateTime.Now.AddDays(-1),
                UpdatedBy = Guid.NewGuid(),
                UpdatedDate = DateTime.Now,
                IsActive = true,
                HasAccess = true,
                Subscription = subscription
            };

            _mockPageRoleMappingRepository
                .Setup(r => r.UpdateAsync(pageRoleMapping, orgId, subscription))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _pageRoleMappingCommandHandler.UpdatePageRoleMappingAsync(pageRoleMapping, orgId, subscription);

            // Assert
            _mockPageRoleMappingRepository.Verify(r => r.UpdateAsync(pageRoleMapping, orgId, subscription), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeletePageRoleMappingAsync_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var pageRoleMappingId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockPageRoleMappingRepository
                .Setup(r => r.DeleteByIdAsync(pageRoleMappingId, orgId, subscription))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _pageRoleMappingCommandHandler.DeletePageRoleMappingAsync(pageRoleMappingId, orgId, subscription);

            // Assert
            _mockPageRoleMappingRepository.Verify(r => r.DeleteByIdAsync(pageRoleMappingId, orgId, subscription), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public void AddPageRoleMappingAsync_ShouldThrowArgumentNullException_WhenPageRoleMappingIsNull()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            bool subscription = false;

            // Act & Assert
            Assert.ThrowsAsync<ArgumentNullException>(async () => 
                await _pageRoleMappingCommandHandler.AddPageRoleMappingAsync(null, orgId, subscription));
            
            _mockPageRoleMappingRepository.Verify(r => r.AddAsync(It.IsAny<List<PageRoleMapping>>(), orgId, subscription), Times.Never);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Never);
        }

        [Test]
        public void UpdatePageRoleMappingAsync_ShouldThrowArgumentNullException_WhenPageRoleMappingIsNull()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            bool subscription = false;

            // Act & Assert
            Assert.ThrowsAsync<ArgumentNullException>(async () => 
                await _pageRoleMappingCommandHandler.UpdatePageRoleMappingAsync(null, orgId, subscription));
            
            _mockPageRoleMappingRepository.Verify(r => r.UpdateAsync(It.IsAny<PageRoleMapping>(), orgId, subscription), Times.Never);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Never);
        }
    }
}
