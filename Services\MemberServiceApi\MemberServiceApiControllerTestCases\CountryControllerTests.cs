using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using MemberServiceApi.Controllers;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using MemberServiceBusinessLayer;

namespace MemberServiceApiControllerTestCases
{
    [TestFixture]
    public class CountryControllerTests
    {
        private Mock<ICountryQueryHandler<Country>> _mockQueryHandler;
        private Mock<ICountryCommandHandler<Country>> _mockCommandHandler;
        private Mock<ILogger<CountryController>> _mockLogger;
        private Mock<IStringLocalizer<CountryController>> _mockLocalizer;
        private CountryController _controller;

        [SetUp]
        public void Setup()
        {
            _mockQueryHandler = new Mock<ICountryQueryHandler<Country>>();
            _mockCommandHandler = new Mock<ICountryCommandHandler<Country>>();
            _mockLogger = new Mock<ILogger<CountryController>>();
            _mockLocalizer = new Mock<IStringLocalizer<CountryController>>();

            // Setup localizer to return the key when requested
            _mockLocalizer.Setup(l => l[It.IsAny<string>()]).Returns((string key) => new LocalizedString(key, key));

            _controller = new CountryController(
                _mockQueryHandler.Object,
                _mockCommandHandler.Object,
                _mockLogger.Object,
                _mockLocalizer.Object
            );
        }

        [Test]
        public async Task GetAllCountries_WhenCountriesExist_ReturnsOkWithCountries()
        {
            // Arrange
            var mockCountries = new List<Country>
            {
                new Country { CountryId = Guid.NewGuid(), CountryName = "United States" },
                new Country { CountryId = Guid.NewGuid(), CountryName = "Canada" }
            };

            _mockQueryHandler
                .Setup(q => q.GetAllCountriesAsync())
                .ReturnsAsync(mockCountries);

            // Act
            var result = await _controller.GetAllCountries();

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult?.StatusCode, Is.EqualTo(200));
            Assert.That(okResult?.Value, Is.EqualTo(mockCountries));
        }

        [Test]
        public async Task GetAllCountries_WhenNoCountriesExist_ReturnsNotFound()
        {
            // Arrange
            _mockQueryHandler
                .Setup(q => q.GetAllCountriesAsync())
                .ReturnsAsync(new List<Country>());

            // Act
            var result = await _controller.GetAllCountries();

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult?.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task GetAllCountries_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            _mockQueryHandler
                .Setup(q => q.GetAllCountriesAsync())
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetAllCountries();

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task GetCountryById_WhenCountryExists_ReturnsOkWithCountry()
        {
            // Arrange
            var countryId = Guid.NewGuid();
            var mockCountry = new Country
            {
                CountryId = countryId,
                CountryName = "United States"
            };

            _mockQueryHandler
                .Setup(q => q.GetCountryByIdAsync(countryId))
                .ReturnsAsync(mockCountry);

            // Act
            var result = await _controller.GetCountryById(countryId);

            // Assert
            Assert.That(result, Is.InstanceOf<OkObjectResult>());
            var okResult = result as OkObjectResult;
            Assert.That(okResult?.StatusCode, Is.EqualTo(200));
            Assert.That(okResult?.Value, Is.EqualTo(mockCountry));
        }

        [Test]
        public async Task GetCountryById_WhenCountryDoesNotExist_ReturnsNotFound()
        {
            // Arrange
            var countryId = Guid.NewGuid();

            _mockQueryHandler
                .Setup(q => q.GetCountryByIdAsync(countryId))
                .ReturnsAsync((Country)null);

            // Act
            var result = await _controller.GetCountryById(countryId);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult?.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task GetCountryById_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var countryId = Guid.NewGuid();

            _mockQueryHandler
                .Setup(q => q.GetCountryByIdAsync(countryId))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.GetCountryById(countryId);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task AddCountry_WhenValidCountry_ReturnsCreatedAtAction()
        {
            // Arrange
            var countryId = Guid.NewGuid();
            var country = new Country
            {
                CountryId = countryId,
                CountryName = "United States"
            };

            _mockCommandHandler
                .Setup(c => c.AddCountryAsync(country))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.AddCountry(country);

            // Assert
            Assert.That(result, Is.InstanceOf<CreatedAtActionResult>());
            var createdResult = result as CreatedAtActionResult;
            Assert.That(createdResult?.StatusCode, Is.EqualTo(201));
            Assert.That(createdResult?.ActionName, Is.EqualTo(nameof(CountryController.GetCountryById)));
            Assert.That(createdResult?.RouteValues["id"], Is.EqualTo(countryId));
            Assert.That(createdResult?.Value, Is.EqualTo(country));
        }

        [Test]
        public async Task AddCountry_WhenNullCountry_ReturnsBadRequest()
        {
            // Act
            var result = await _controller.AddCountry(null!);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult?.StatusCode, Is.EqualTo(400));
        }

        [Test]
        public async Task AddCountry_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var country = new Country
            {
                CountryId = Guid.NewGuid(),
                CountryName = "United States"
            };

            _mockCommandHandler
                .Setup(c => c.AddCountryAsync(country))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.AddCountry(country);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task UpdateCountry_WhenValidCountry_ReturnsNoContent()
        {
            // Arrange
            var countryId = Guid.NewGuid();
            var country = new Country
            {
                CountryId = countryId,
                CountryName = "United States"
            };

            _mockCommandHandler
                .Setup(c => c.UpdateCountryAsync(country))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.UpdateCountry(countryId, country);

            // Assert
            Assert.That(result, Is.InstanceOf<NoContentResult>());
            var noContentResult = result as NoContentResult;
            Assert.That(noContentResult?.StatusCode, Is.EqualTo(204));
        }

        [Test]
        public async Task UpdateCountry_WhenNullCountry_ReturnsBadRequest()
        {
            // Arrange
            var countryId = Guid.NewGuid();

            // Act
            var result = await _controller.UpdateCountry(countryId, null!);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult?.StatusCode, Is.EqualTo(400));
        }

        [Test]
        public async Task UpdateCountry_WhenIdMismatch_ReturnsBadRequest()
        {
            // Arrange
            var countryId = Guid.NewGuid();
            var differentId = Guid.NewGuid();
            var country = new Country
            {
                CountryId = differentId,
                CountryName = "United States"
            };

            // Act
            var result = await _controller.UpdateCountry(countryId, country);

            // Assert
            Assert.That(result, Is.InstanceOf<BadRequestObjectResult>());
            var badRequestResult = result as BadRequestObjectResult;
            Assert.That(badRequestResult?.StatusCode, Is.EqualTo(400));
        }

        [Test]
        public async Task UpdateCountry_WhenCountryDoesNotExist_ReturnsNotFound()
        {
            // Arrange
            var countryId = Guid.NewGuid();
            var country = new Country
            {
                CountryId = countryId,
                CountryName = "United States"
            };

            _mockCommandHandler
                .Setup(c => c.UpdateCountryAsync(country))
                .ThrowsAsync(new KeyNotFoundException());

            // Act
            var result = await _controller.UpdateCountry(countryId, country);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult?.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task UpdateCountry_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var countryId = Guid.NewGuid();
            var country = new Country
            {
                CountryId = countryId,
                CountryName = "United States"
            };

            _mockCommandHandler
                .Setup(c => c.UpdateCountryAsync(country))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.UpdateCountry(countryId, country);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }

        [Test]
        public async Task DeleteCountry_WhenCountryExists_ReturnsNoContent()
        {
            // Arrange
            var countryId = Guid.NewGuid();

            _mockCommandHandler
                .Setup(c => c.DeleteCountryAsync(countryId))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _controller.DeleteCountry(countryId);

            // Assert
            Assert.That(result, Is.InstanceOf<NoContentResult>());
            var noContentResult = result as NoContentResult;
            Assert.That(noContentResult?.StatusCode, Is.EqualTo(204));
        }

        [Test]
        public async Task DeleteCountry_WhenCountryDoesNotExist_ReturnsNotFound()
        {
            // Arrange
            var countryId = Guid.NewGuid();

            _mockCommandHandler
                .Setup(c => c.DeleteCountryAsync(countryId))
                .ThrowsAsync(new KeyNotFoundException());

            // Act
            var result = await _controller.DeleteCountry(countryId);

            // Assert
            Assert.That(result, Is.InstanceOf<NotFoundObjectResult>());
            var notFoundResult = result as NotFoundObjectResult;
            Assert.That(notFoundResult?.StatusCode, Is.EqualTo(404));
        }

        [Test]
        public async Task DeleteCountry_WhenExceptionOccurs_ReturnsInternalServerError()
        {
            // Arrange
            var countryId = Guid.NewGuid();

            _mockCommandHandler
                .Setup(c => c.DeleteCountryAsync(countryId))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.DeleteCountry(countryId);

            // Assert
            Assert.That(result, Is.InstanceOf<ObjectResult>());
            var statusCodeResult = result as ObjectResult;
            Assert.That(statusCodeResult?.StatusCode, Is.EqualTo(500));
        }
    }
}
