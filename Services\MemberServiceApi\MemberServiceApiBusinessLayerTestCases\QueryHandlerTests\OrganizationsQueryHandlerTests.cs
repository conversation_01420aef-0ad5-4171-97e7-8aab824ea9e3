using NUnit.Framework;
using Moq;
using MemberServiceBusinessLayer.QueryHandler;
using MemberServiceDataAccessLayer.Implementation;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;

namespace MemberServiceApiBusinessLayerTestCases.QueryHandlerTests
{
    [TestFixture]
    public class OrganizationsQueryHandlerTests
    {
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<IOrganizationRepository> _mockOrganizationRepository;
        private OrganizationsQueryHandler _organizationsQueryHandler;

        [SetUp]
        public void Setup()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockOrganizationRepository = new Mock<IOrganizationRepository>();

            _mockUnitOfWork.Setup(u => u.OrganizationRepository).Returns(_mockOrganizationRepository.Object);

            _organizationsQueryHandler = new OrganizationsQueryHandler(_mockUnitOfWork.Object);
        }

        [Test]
        public async Task GetOrganizationByIdAsync_ShouldReturnOrganization_WhenOrganizationExists()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            var expectedOrganization = new Organization 
            { 
                OrganizationId = organizationId, 
                OrganizationName = "Test Organization",
                Address = "123 Main St",
                Country = "USA",
                ContactNumber = "123-456-7890",
                Email = "<EMAIL>",
                IsActive = true,
                CreatedDate = DateTime.Now.AddDays(-30)
            };

            _mockOrganizationRepository
                .Setup(r => r.GetByIdAsync(organizationId))
                .ReturnsAsync(expectedOrganization);

            // Act
            var result = await _organizationsQueryHandler.GetOrganizationByIdAsync(organizationId);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.OrganizationId, Is.EqualTo(organizationId));
            Assert.That(result.OrganizationName, Is.EqualTo("Test Organization"));
            Assert.That(result.Address, Is.EqualTo("123 Main St"));
            _mockOrganizationRepository.Verify(r => r.GetByIdAsync(organizationId), Times.Once);
        }

        [Test]
        public async Task GetOrganizationByIdAsync_ShouldReturnNull_WhenOrganizationDoesNotExist()
        {
            // Arrange
            var organizationId = Guid.NewGuid();

            _mockOrganizationRepository
                .Setup(r => r.GetByIdAsync(organizationId))
                .ReturnsAsync((Organization)null);

            // Act
            var result = await _organizationsQueryHandler.GetOrganizationByIdAsync(organizationId);

            // Assert
            Assert.That(result, Is.Null);
            _mockOrganizationRepository.Verify(r => r.GetByIdAsync(organizationId), Times.Once);
        }

        [Test]
        public async Task GetAllOrganizationsAsync_ShouldReturnAllOrganizations()
        {
            // Arrange
            var expectedOrganizations = new List<Organization>
            {
                new Organization 
                { 
                    OrganizationId = Guid.NewGuid(), 
                    OrganizationName = "Organization 1",
                    IsActive = true
                },
                new Organization 
                { 
                    OrganizationId = Guid.NewGuid(), 
                    OrganizationName = "Organization 2",
                    IsActive = true
                }
            };

            _mockOrganizationRepository
                .Setup(r => r.GetAllAsync())
                .ReturnsAsync(expectedOrganizations);

            // Act
            var result = await _organizationsQueryHandler.GetAllOrganizationsAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2));
            Assert.That(result, Is.EquivalentTo(expectedOrganizations));
            _mockOrganizationRepository.Verify(r => r.GetAllAsync(), Times.Once);
        }

        [Test]
        public async Task GetOrganizationsByNameAsync_ShouldReturnOrganizations_WhenOrganizationsExist()
        {
            // Arrange
            var organizationName = "Test Organization";
            var expectedOrganizations = new List<Organization>
            {
                new Organization 
                { 
                    OrganizationId = Guid.NewGuid(), 
                    OrganizationName = "Test Organization",
                    IsActive = true
                }
            };

            _mockOrganizationRepository
                .Setup(r => r.GetAllAsync())
                .ReturnsAsync(expectedOrganizations);

            // Act
            var result = await _organizationsQueryHandler.GetOrganizationsByNameAsync(organizationName);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(1));
            Assert.That(result.First().OrganizationName, Is.EqualTo(organizationName));
            _mockOrganizationRepository.Verify(r => r.GetAllAsync(), Times.Once);
        }

        [Test]
        public async Task FindByNameAsync_ShouldReturnTrue_WhenOrganizationExists()
        {
            // Arrange
            var organizationName = "Test Organization";

            _mockOrganizationRepository
                .Setup(r => r.FindByNameAsync(organizationName))
                .ReturnsAsync(true);

            // Act
            var result = await _organizationsQueryHandler.FindByNameAsync(organizationName);

            // Assert
            Assert.That(result, Is.True);
            _mockOrganizationRepository.Verify(r => r.FindByNameAsync(organizationName), Times.Once);
        }

        [Test]
        public async Task FindByNameAsync_ShouldReturnFalse_WhenOrganizationDoesNotExist()
        {
            // Arrange
            var organizationName = "Nonexistent Organization";

            _mockOrganizationRepository
                .Setup(r => r.FindByNameAsync(organizationName))
                .ReturnsAsync(false);

            // Act
            var result = await _organizationsQueryHandler.FindByNameAsync(organizationName);

            // Assert
            Assert.That(result, Is.False);
            _mockOrganizationRepository.Verify(r => r.FindByNameAsync(organizationName), Times.Once);
        }

        [Test]
        public void Constructor_ShouldThrowArgumentNullException_WhenUnitOfWorkIsNull()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => new OrganizationsQueryHandler(null));
        }
    }
}
