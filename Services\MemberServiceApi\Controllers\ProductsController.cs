﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging; // For logging
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Data.SqlClient;
using Contracts;
using Microsoft.Extensions.Localization;
using MemberServiceBusinessLayer;
using Microsoft.EntityFrameworkCore;
using System;
using DataAccessLayer.Context;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Identity.Web.Resource;

namespace ProductServiceApi.Controllers
{
    [RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes")]
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class ProductsController : ControllerBase
    {
        private readonly IProductCommandHandler<ProductDTO> _productDataHandler;
        private readonly IProductQueryHandler<ProductDTO> _productQueryHandler;
        private readonly ILogger<ProductsController> _logger;
        private readonly IStringLocalizer<ProductsController> _localizer;
        private readonly IProductMembersQueryHandler<ProductUserAccess> _productMembersQueryHandler;
        private readonly IUserAccessCommandHandler<ProductUserAccess> _accessCommandHandler;


        public ProductsController(
            IProductCommandHandler<ProductDTO> dataHandler,
            IProductQueryHandler<ProductDTO> queryHandler,
            ILogger<ProductsController> logger,
            IStringLocalizer<ProductsController> localizer,
            IProductMembersQueryHandler<ProductUserAccess> productmembersqueryhandler,
            IUserAccessCommandHandler<ProductUserAccess> accesscommandhandler
            )
        {
            _productDataHandler = dataHandler;
            _productQueryHandler = queryHandler;
            _logger = logger;
            _localizer = localizer; // Assigning localizer
            _productMembersQueryHandler = productmembersqueryhandler;
            _accessCommandHandler = accesscommandhandler;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<ProductDTO>>> Get()
        {
            try
            {
                var products = await _productQueryHandler.GetProduct();
                return Ok(products);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                return StatusCode(500);
            }
        }

        [HttpGet("{id:Guid}/{OrgID:Guid}/{Subscription}")]
        public async Task<ActionResult<ProductDTO>> GetById(Guid id, Guid OrgID, bool Subscription)
        {
            try
            {
                var product = await _productQueryHandler.GetProductById(id, OrgID, Subscription);
                return Ok(product);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["GetLogError"]);
                return StatusCode(500);
            }
        }

        [HttpPut("{id:guid}")]
        public async Task<IActionResult> UpdateById(Guid id, ProductDTO product)
        {
            if (product == null || product.Id != id)
            {
                return BadRequest(_localizer["InvalidProduct"]);
            }

            try
            {
                await _productDataHandler.UpdateProduct(product, product.OrganizationId, product.Subscription);
                return Ok(_localizer["UpdateSuccessful"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["UpdateLogError"]);
                return StatusCode(500);
            }
        }

        [HttpDelete("{id:guid}/{OrgID:Guid}/{Subscription}")]

        public async Task<IActionResult> DeleteById(Guid id, Guid OrgID, bool Subscription)
        {
            try
            {
                await _productDataHandler.DeleteProductById(id, OrgID, Subscription);
                return Ok(_localizer["SuccessfulDeletion"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["DeleteLogError"]);
                return StatusCode(500);
            }
        }

        [HttpDelete("(entity)")]
        public async Task<IActionResult> DeleteByEntity([FromBody] ProductDTO product)
        {
            try
            {
                await _productDataHandler.DeleteProductByEntity(product, product.OrganizationId, product.Subscription);
                return Ok(_localizer["DeleteSuccessful"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["DeleteLogError"]);
                return StatusCode(500);
            }
        }

        [HttpPost]
        [Route("registration")]
        public async Task<IActionResult> Registration([FromBody] List<ProductRegistrationDto> registrations)
        {
            if (registrations == null || registrations.Count == 0)
            {
                return BadRequest(_localizer["NoProduct"]);
            }

            try
            {
                foreach (var reg in registrations)
                {
                    var product = new ProductDTO
                    {
                        Id = Guid.NewGuid(),
                        Name = reg.Name,
                        Description = reg.Description,
                        ByProduct = reg.ByProduct,
                        OrganizationId = reg.OrganizationId,
                        Subscription = reg.Subscription
                    };

                    await _productDataHandler.AddProduct(new List<ProductDTO> { product }, reg.OrganizationId, reg.Subscription);
                }

                return Ok(_localizer["SuccessfulRegistration"]);
            }
            catch (SqlException ex)
            {
                return StatusCode(500, _localizer["DatabaseError"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["PostLogError"]);
                return StatusCode(500);
            }
        }




        [HttpGet("{productId}/members/{OrgID:Guid}/{Subscription}")]
        public async Task<ActionResult<IEnumerable<Member>>> GetMembersForProduct(Guid productId, Guid OrgID, bool Subscription)
        {

            var members = await _productMembersQueryHandler.GetProductMembers(productId, OrgID, Subscription);


            if (members == null || !members.Any())
            {
                return NotFound();
            }

            return Ok(members);


        }

        // POST: api/Product/{productId}/updateAccess
        [HttpPut("api/Products/updateAccess/{OrgID:Guid}/{Subscription}")]
        public async Task<IActionResult> UpdateAccess([FromRoute] Guid OrgID, [FromRoute] bool Subscription, [FromQuery] Guid productId, [FromBody] MemberAccessUpdate memberAccessUpdates)
        {
            if (productId == Guid.Empty || memberAccessUpdates == null)
            {
                return BadRequest(_localizer["AccessError"]);
            }
            var success = await _accessCommandHandler.UpdateUserAccessAsync(productId, memberAccessUpdates, OrgID, Subscription);

            if (success)
            {
                return Ok(new { Message = _localizer["AccessUpdateSuccessful"] });
            }
            else
            {
                return NotFound(_localizer["Access record not found"]);
            }
        }

    }
}
