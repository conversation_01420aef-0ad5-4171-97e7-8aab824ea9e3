using NUnit.Framework;
using Moq;
using MemberServiceBusinessLayer.CommandHandler;
using MemberServiceDataAccessLayer.Implementation;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MemberServiceApiBusinessLayerTestCases.CommandHandlerTests
{
    [TestFixture]
    public class GuardianCommandHandlerTests
    {
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<IGuardianRepository> _mockGuardianRepository;
        private GuardianCommandHandler _guardianCommandHandler;

        [SetUp]
        public void Setup()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockGuardianRepository = new Mock<IGuardianRepository>();

            _mockUnitOfWork.Setup(u => u.GuardianRepository).Returns(_mockGuardianRepository.Object);

            _guardianCommandHandler = new GuardianCommandHandler(_mockUnitOfWork.Object);
        }

        [Test]
        public async Task AddGuardianAsync_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var guardians = new List<Guardian>
            {
                new Guardian 
                { 
                    GuardianId = Guid.NewGuid(), 
                    GuardianName = "John Doe",
                    GuardianRelationship = "Parent",
                    GuardianSex = "Male",
                    GuardianCity = "Anytown",
                    GuardianState = "CA",
                    GuardianCountry = "USA",
                    GuardianPhone = "************",
                    GuardianEmail = "<EMAIL>",
                    GuardianAddress = "123 Main St",
                    OrganizationId = orgId,
                    Subscription = subscription
                }
            };

            _mockGuardianRepository
                .Setup(r => r.AddAsync(guardians, orgId, subscription))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _guardianCommandHandler.AddGuardianAsync(guardians, orgId, subscription);

            // Assert
            _mockGuardianRepository.Verify(r => r.AddAsync(guardians, orgId, subscription), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task UpdateGuardianAsync_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var guardian = new Guardian 
            { 
                GuardianId = Guid.NewGuid(), 
                GuardianName = "John Doe",
                GuardianRelationship = "Parent",
                GuardianSex = "Male",
                GuardianCity = "Anytown",
                GuardianState = "CA",
                GuardianCountry = "USA",
                GuardianPhone = "************",
                GuardianEmail = "<EMAIL>",
                GuardianAddress = "123 Main St",
                OrganizationId = orgId,
                Subscription = subscription
            };

            _mockGuardianRepository
                .Setup(r => r.UpdateAsync(guardian, orgId, subscription))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _guardianCommandHandler.UpdateGuardianAsync(guardian, orgId, subscription);

            // Assert
            _mockGuardianRepository.Verify(r => r.UpdateAsync(guardian, orgId, subscription), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public async Task DeleteGuardianAsync_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var guardianId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;

            _mockGuardianRepository
                .Setup(r => r.DeleteByIdAsync(guardianId, orgId, subscription))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _guardianCommandHandler.DeleteGuardianAsync(guardianId, orgId, subscription);

            // Assert
            _mockGuardianRepository.Verify(r => r.DeleteByIdAsync(guardianId, orgId, subscription), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }
    }
}
