﻿using Contracts;
using DataAccessLayer.Context;
using Interfaces.ShardManagement;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace MemberServiceDataAccessLayer.Implementation
{
    public class VisitTypeRepository : ShardGenericRepository<VisitType>, IVisitTypeRepository
    {
        private readonly AccountDatabaseContext _context;

        /// <summary>
        /// Repository class for managing VisitType entities in the database.
        /// </summary>
        /// <param name="context">The database context used for data operations.</param>
        public VisitTypeRepository(AccountDatabaseContext context, ShardMapManagerService<AccountDatabaseContext, AccountDatabaseContext> _shardMapManagerService, IStringLocalizer<AccountDatabaseContext> localizer,
            ILogger<AccountDatabaseContext> logger) : base(context, _shardMapManagerService, localizer, logger)
        {
            _context = context;
        }

        /// <summary>
        /// Retrieves all VisitType entities associated with a specific Organization ID.
        /// </summary>
        /// <param name="orgId">The Organization ID.</param>
        /// <returns>A list of VisitType entities.</returns>
        public async Task<IEnumerable<VisitType>> GetByOrganizationIdAsync(Guid orgId)
        {
            return await _context.VisitTypes
                                 .Where(visit_type => visit_type.OrganizationId == orgId && visit_type.IsActive == true)
                                 .ToListAsync();
        }

        /// <summary>
        /// Updates the CPT code for a specific visit type by organization ID and visit name.
        /// </summary>
        /// <param name="orgId">The Organization ID.</param>
        /// <param name="visitName">The name of the visit type.</param>
        /// <param name="newCptCode">The new CPT code to update.</param>
        /// <returns>True if the update was successful, otherwise false.</returns>
        public async Task<bool> UpdateCptCodeAsync(Guid orgId, string visitName, string newCptCode)
        {
            bool isUpdated = false;
            var visitType = await _context.VisitTypes
                                          .Where(visit_type => visit_type.OrganizationId == orgId && visit_type.VisitName == visitName)
                                          .FirstOrDefaultAsync();
            if (visitType != null)
            {
                visitType.CPTCode = newCptCode;
                _context.VisitTypes.Update(visitType);
                await _context.SaveChangesAsync();
                isUpdated = true;
            }
            return isUpdated;
        }

        /// <summary>
        /// Deletes a visit type based on Organization ID and visit name.
        /// </summary>
        /// <param name="orgId">The Organization ID.</param>
        /// <param name="visitName">The name of the visit type.</param>
        /// <returns>True if the deletion was successful, otherwise false.</returns>
        public async Task<bool> DeleteVisitTypeAsync(Guid orgId, string visitName)
        {
            bool isDeleted = false;
            var visitType = await _context.VisitTypes
                                          .Where(visit_type => visit_type.OrganizationId == orgId && visit_type.VisitName == visitName)
                                          .FirstOrDefaultAsync();
            if (visitType != null)
            {
                visitType.IsActive = false;
                await _context.SaveChangesAsync();
                isDeleted = true;
            }
            return isDeleted;
        }

        /// <summary>
        /// Retrieves the first VisitType entity that matches the specified condition.
        /// </summary>
        /// <param name="predicate">The condition to filter the VisitType entities.</param>
        /// <returns>The first matching VisitType entity, or null if no match is found.</returns>
        public async Task<VisitType> GetFirstOrDefaultAsync(Expression<Func<VisitType, bool>> predicate)
        {
            return await _context.VisitTypes.FirstOrDefaultAsync(predicate);
        }
    }
}
