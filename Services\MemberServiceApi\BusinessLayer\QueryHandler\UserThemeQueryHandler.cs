using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Contracts;
using MemberServiceDataAccessLayer.Implementation;

namespace MemberServiceBusinessLayer.QueryHandler
{
    public class UserThemeQueryHandler : IUserThemeQueryHandler<UserTheme>
    {
        private readonly UserThemeRepository _userThemeRepository;

        public UserThemeQueryHandler(UserThemeRepository userThemeRepository)
        {
            _userThemeRepository = userThemeRepository;
        }

        public async Task<IEnumerable<UserTheme>> GetUserThemes()
        {
            return await _userThemeRepository.GetAllAsync();
        }

        public async Task<UserTheme> GetUserThemeById(Guid id, Guid OrgID, bool Subscription)
        {
            return await _userThemeRepository.GetByIdAsync(id, OrgID, Subscription);
        }
    }
}
