using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using PracticeContracts;
using PracticeDataAccessLayer.Context;
using PracticeDataAccessLayer.Implementation;
using PracticeDataAccessLayer.PracticeDataAccessLayerResources;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace PracticeApiDataAccessLayerTestCases
{
    /// <summary>
    /// Test implementation of IShardGenericRepository for testing purposes
    /// </summary>
    public class TestShardGenericRepository<T> : IShardGenericRepository<T> where T : class
    {
        private readonly DbContext _context;
        private readonly DbSet<T> _dbSet;
        private readonly List<T> _entities;

        public TestShardGenericRepository(List<T> testData)
        {
            _entities = testData;

            // Setup mock DbSet
            var mockDbSet = new Mock<DbSet<T>>();
            var queryable = _entities.AsQueryable();
            mockDbSet.As<IQueryable<T>>().Setup(m => m.Provider).Returns(queryable.Provider);
            mockDbSet.As<IQueryable<T>>().Setup(m => m.Expression).Returns(queryable.Expression);
            mockDbSet.As<IQueryable<T>>().Setup(m => m.ElementType).Returns(queryable.ElementType);
            mockDbSet.As<IQueryable<T>>().Setup(m => m.GetEnumerator()).Returns(() => queryable.GetEnumerator());
            mockDbSet.Setup(m => m.AddRangeAsync(It.IsAny<IEnumerable<T>>(), It.IsAny<CancellationToken>()))
                .Returns(Task.CompletedTask);

            // Setup mock context
            var mockContext = new Mock<DbContext>();
            mockContext.Setup(c => c.Set<T>()).Returns(mockDbSet.Object);
            mockContext.Setup(c => c.SaveChangesAsync(It.IsAny<CancellationToken>()))
                .ReturnsAsync(1);

            _context = mockContext.Object;
            _dbSet = mockDbSet.Object;
        }

        public async Task AddAsync(IEnumerable<T> entities, Guid orgId, bool subscription)
        {
            _entities.AddRange(entities);
            await Task.CompletedTask;
        }

        public async Task<IEnumerable<T>> GetAllAsync()
        {
            return await Task.FromResult(_entities);
        }

        public async Task<IEnumerable<T>> GetAsync(Guid orgId, bool subscription)
        {
            return await Task.FromResult(_entities);
        }

        public async Task<T> GetByIdAsync(Guid id, Guid orgId, bool subscription)
        {
            // Assuming T has an Id property of type Guid
            var entity = _entities.FirstOrDefault(e =>
                e.GetType().GetProperty("Id")?.GetValue(e)?.ToString() == id.ToString());
            return await Task.FromResult(entity);
        }

        public async Task UpdateAsync(T entity, Guid orgId, bool subscription)
        {
            // Find the entity by Id and update it
            var id = entity.GetType().GetProperty("Id")?.GetValue(entity);
            if (id != null)
            {
                var existingEntity = _entities.FirstOrDefault(e =>
                    e.GetType().GetProperty("Id")?.GetValue(e)?.ToString() == id.ToString());

                if (existingEntity != null)
                {
                    var index = _entities.IndexOf(existingEntity);
                    if (index >= 0)
                    {
                        _entities[index] = entity;
                    }
                }
            }

            await Task.CompletedTask;
        }

        public async Task UpdateRangeAsync(List<T> entities, Guid orgId, bool subscription)
        {
            foreach (var entity in entities)
            {
                await UpdateAsync(entity, orgId, subscription);
            }
        }

        public async Task DeleteByEntityAsync(T entity, Guid orgId, bool subscription)
        {
            _entities.Remove(entity);
            await Task.CompletedTask;
        }

        public async Task DeleteByIdAsync(Guid id, Guid orgId, bool subscription)
        {
            var entity = await GetByIdAsync(id, orgId, subscription);
            if (entity != null)
            {
                _entities.Remove(entity);
            }
        }
    }

    [TestFixture]
    public class ShardGenericRepositoryTests
    {
        private TestShardGenericRepository<Tasks> _repository;
        private List<Tasks> _testTasks;
        private Tasks _testTask;
        private Guid _testOrgId;
        private bool _testSubscription;

        [SetUp]
        public void Setup()
        {
            // Create test data
            _testTask = new Tasks
            {
                Id = Guid.NewGuid(),
                PatientName = "Test Patient",
                TaskType = "Appointment",
                Subject = "Annual Checkup",
                SSN = "***********"
            };

            _testTasks = new List<Tasks>
            {
                _testTask,
                new Tasks
                {
                    Id = Guid.NewGuid(),
                    PatientName = "Another Patient",
                    TaskType = "Follow-up",
                    Subject = "Medication Review",
                    SSN = "***********"
                }
            };

            _testOrgId = Guid.NewGuid();
            _testSubscription = true;

            // Create repository with test data
            _repository = new TestShardGenericRepository<Tasks>(_testTasks);
        }

        [Test]
        public async Task AddAsync_ShouldAddEntitiesToCollection()
        {
            // Arrange
            var initialCount = _testTasks.Count;
            var newTasks = new List<Tasks>
            {
                new Tasks { Id = Guid.NewGuid(), PatientName = "New Patient", TaskType = "New Appointment" }
            };

            // Act
            await _repository.AddAsync(newTasks, _testOrgId, _testSubscription);
            var allTasks = await _repository.GetAllAsync();

            // Assert
            Assert.That(allTasks.Count(), Is.EqualTo(initialCount + newTasks.Count));
        }

        [Test]
        public async Task GetAllAsync_ShouldReturnAllEntities()
        {
            // Act
            var result = await _repository.GetAllAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(_testTasks.Count));
            Assert.That(result, Is.EquivalentTo(_testTasks));
        }

        [Test]
        public async Task GetAsync_ShouldReturnEntitiesForSpecificOrgAndSubscription()
        {
            // Act
            var result = await _repository.GetAsync(_testOrgId, _testSubscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(_testTasks.Count));
            Assert.That(result, Is.EquivalentTo(_testTasks));
        }

        [Test]
        public async Task GetByIdAsync_ShouldReturnEntityWithMatchingId()
        {
            // Arrange
            var id = _testTask.Id;

            // Act
            var result = await _repository.GetByIdAsync(id, _testOrgId, _testSubscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(id));
            Assert.That(result.PatientName, Is.EqualTo(_testTask.PatientName));
        }

        [Test]
        public async Task UpdateAsync_ShouldUpdateEntity()
        {
            // Arrange
            var taskToUpdate = _testTask;
            var updatedSubject = "Updated Subject";
            taskToUpdate.Subject = updatedSubject;

            // Act
            await _repository.UpdateAsync(taskToUpdate, _testOrgId, _testSubscription);
            var result = await _repository.GetByIdAsync(taskToUpdate.Id, _testOrgId, _testSubscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Subject, Is.EqualTo(updatedSubject));
        }

        [Test]
        public async Task UpdateRangeAsync_ShouldUpdateMultipleEntities()
        {
            // Arrange
            var tasksToUpdate = _testTasks.ToList();
            var updatedPrefix = "Updated ";
            foreach (var task in tasksToUpdate)
            {
                task.Subject = updatedPrefix + task.Subject;
            }

            // Act
            await _repository.UpdateRangeAsync(tasksToUpdate, _testOrgId, _testSubscription);
            var result = await _repository.GetAllAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(_testTasks.Count));
            foreach (var task in result)
            {
                Assert.That(task.Subject, Does.StartWith(updatedPrefix));
            }
        }

        [Test]
        public async Task DeleteByEntityAsync_ShouldRemoveEntity()
        {
            // Arrange
            var initialCount = _testTasks.Count;
            var taskToDelete = _testTask;

            // Act
            await _repository.DeleteByEntityAsync(taskToDelete, _testOrgId, _testSubscription);
            var result = await _repository.GetAllAsync();

            // Assert
            Assert.That(result.Count(), Is.EqualTo(initialCount - 1));
            Assert.That(result, Does.Not.Contain(taskToDelete));
        }

        [Test]
        public async Task DeleteByIdAsync_ShouldRemoveEntityWithMatchingId()
        {
            // Arrange
            var initialCount = _testTasks.Count;
            var id = _testTask.Id;

            // Act
            await _repository.DeleteByIdAsync(id, _testOrgId, _testSubscription);
            var result = await _repository.GetAllAsync();
            var deletedTask = await _repository.GetByIdAsync(id, _testOrgId, _testSubscription);

            // Assert
            Assert.That(result.Count(), Is.EqualTo(initialCount - 1));
            Assert.That(deletedTask, Is.Null);
        }
    }
}
