# Intelligent Automated Versioning System

This document explains how the intelligent automated versioning system works in the TeyaHealth repository.

## Overview

The versioning system intelligently analyzes code changes and automatically determines the appropriate version bump strategy. It works with branch protection rules and only versions projects when actual changes are made, creating pull requests with detailed change analysis.

## How It Works

### Intelligent Version Analysis

The system analyzes both **commit messages** and **file changes** to determine the appropriate versioning strategy:

1. **Analyzes commit messages** for conventional commit patterns
2. **Examines changed files** for specific patterns
3. **Determines version type** based on change impact
4. **Targets specific projects** affected by changes
5. **Creates descriptive PRs** with change analysis

### Version Strategies

All versions use the `YYYYMMDD.HHMM` format with type suffixes for differentiation:

| Version Type | Format | Triggers | Examples |
|--------------|--------|----------|----------|
| **Major** 🚨 | `YYYYMMDD.HHMM-major` | Breaking changes, database migrations | `BREAKING CHANGE:`, Migration files, Schema changes |
| **Minor** ✨ | `YYYYMMDD.HHMM-minor` | New features, new components | `feat:`, `feature:`, new Controllers/Services |
| **Patch** 🐛 | `YYYYMMDD.HHMM-patch` | Bug fixes, hotfixes | `fix:`, `bug:`, `hotfix:` |
| **Build** ⚙️ | `YYYYMMDD.HHMM-build` | Configuration changes | `.json`, `.yml`, `.config` files |
| **Auto** 🔄 | `YYYYMMDD.HHMM-auto` | General code changes | Other code modifications |

**Note**: All versions use complete date and time format (YYYYMMDD.HHMM) with type suffixes for clear differentiation and precise timestamping.

### Trigger Conditions:
- ✅ Push to main branch
- ✅ Build and tests pass
- ✅ Project-related files were changed
- ❌ Pull requests (skipped)
- ❌ No relevant file changes (skipped)

## Branch Naming Convention

All versioning branches follow your established naming convention:
```
DEV-{timestamp}-version-bump
```

Where `{timestamp}` is in format `YYYYMMDDHHMM` (e.g., `202412151430`)

## Version Format

All versions use the unified `YYYYMMDD.HHMM` format with type suffixes:

```xml
<PropertyGroup>
  <Version>v20241215.1430-major</Version>  <!-- Major: Breaking changes -->
  <Version>v20241215.1430-minor</Version>  <!-- Minor: New features -->
  <Version>v20241215.1430-patch</Version>  <!-- Patch: Bug fixes -->
  <Version>v20241215.1430-build</Version>  <!-- Build: Config changes -->
  <Version>v20241215.1430-auto</Version>   <!-- Auto: General changes -->
</PropertyGroup>
```

**Format Breakdown:**
- `v` - Version prefix
- `20241215` - Complete date (YYYYMMDD)
- `1430` - Time (HHMM)
- `-major/-minor/-patch/-build/-auto` - Change type suffix

## Usage

### Automatic Intelligent Versioning
1. Make changes to your code using conventional commit messages
2. Create a pull request to main
3. After PR is merged, versioning automatically analyzes changes
4. System determines appropriate version type and creates PR
5. Review the detailed change analysis and merge the versioning PR

### Commit Message Conventions

To get the most accurate versioning, use conventional commit messages:

```bash
# For breaking changes (Major version)
git commit -m "feat!: redesign user authentication system"
git commit -m "BREAKING CHANGE: remove deprecated API endpoints"

# For new features (Minor version)
git commit -m "feat: add new user dashboard component"
git commit -m "feature: implement email notification service"

# For bug fixes (Patch version)
git commit -m "fix: resolve login validation issue"
git commit -m "bug: correct date formatting in reports"

# For configuration changes (Build version)
git commit -m "config: update database connection settings"
git commit -m "chore: update package dependencies"
```

## Examples

### Intelligent Versioning Scenarios

**Scenario 1: Breaking Change**
```
Commit: "BREAKING CHANGE: remove deprecated user API endpoints"
Changed files: Services/MemberServiceApi/Controllers/UserController.cs
Analysis: Breaking changes detected
Result: Major version v20241215.1430-major applied to MemberServiceApi.csproj
```

**Scenario 2: New Feature**
```
Commit: "feat: add new dashboard analytics component"
Changed files: Clients/TeyaWebApp/Components/Analytics/Dashboard.razor
Analysis: Feature changes detected
Result: Minor version v20241215.1430-minor applied to TeyaWebApp.csproj
```

**Scenario 3: Bug Fix**
```
Commit: "fix: resolve date validation in appointment booking"
Changed files: Services/AppointmentsApi/Services/BookingService.cs
Analysis: Bug fixes detected
Result: Patch version v20241215.1430-patch applied to Appointments.csproj
```

**Scenario 4: Configuration Update**
```
Commit: "config: update database connection timeout settings"
Changed files: Services/MemberServiceApi/appsettings.json
Analysis: Configuration changes detected
Result: Build version v20241215.1430-build applied to MemberServiceApi.csproj
```

**Scenario 5: General Code Changes**
```
Commit: "refactor: improve code structure in user service"
Changed files: Services/MemberServiceApi/Services/UserService.cs
Analysis: General code changes detected
Result: Auto version v20241215.1430-auto applied to MemberServiceApi.csproj
```

**Scenario 6: Documentation Only**
```
Commit: "docs: update API documentation"
Changed files: README.md, docs/api.md
Analysis: No project files changed
Result: No versioning (skipped)
```

**Scenario 7: Multiple Change Types**
```
Commit: "feat: add user preferences with breaking API changes"
Changed files: Multiple controllers and services
Analysis: Both breaking changes and features detected
Result: Major version v20241215.1430-major (breaking changes take precedence)
```

## Benefits

1. **Intelligent Analysis**: Automatically determines version type based on change impact
2. **Conventional Commits**: Supports industry-standard commit message conventions
3. **Respects Branch Protection**: Creates PRs instead of direct pushes
4. **Smart Detection**: Only versions when actual changes are made
5. **Follows Conventions**: Uses proper branch naming format
6. **Granular Control**: Versions only affected projects
7. **Detailed Reporting**: Rich PR descriptions with change analysis
8. **Audit Trail**: Clear PR history of all version changes with reasoning
9. **Team Notifications**: Enhanced MS Teams integration with change details
10. **Semantic Versioning**: Follows semantic versioning principles automatically

## Troubleshooting

### Versioning Not Running
- Check if changes were made to project-related files
- Verify build and tests passed
- Ensure push was to main branch (not PR)

### Branch Name Rejected
- The system automatically follows your naming convention
- If issues persist, check branch protection rules

### No Projects Detected
- Verify .csproj files exist in expected locations
- Check file paths in the workflow logs
- Use manual versioning to specify exact projects

## Configuration

### Environment Variables Required:
- `GITHUB_TOKEN`: For creating PRs (automatically provided)
- `TEAMS_WEBHOOK_URL`: For MS Teams notifications (secret)

### Customization:
- Modify file extensions in change detection
- Adjust project discovery patterns
- Change version format in workflow files
