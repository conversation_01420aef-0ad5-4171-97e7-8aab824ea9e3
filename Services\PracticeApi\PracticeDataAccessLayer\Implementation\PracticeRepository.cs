﻿using Microsoft.EntityFrameworkCore;
using PracticeContracts;
using PracticeDataAccessLayer.Context;
using PracticeNotesDataAccessLayer.Implementation;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Interfaces.ShardManagement;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using PracticeDataAccessLayer.PracticeDataAccessLayerResources;

namespace PracticeDataAccessLayer.Implementation
{
    public class PracticeRepository : ShardGenericRepository<Tasks>, IPracticeRepository
    {
        private readonly PracticeDatabaseContext _context;
        private readonly ShardMapManagerService<PracticeDatabaseContext, PracticeDataAccessLayerStrings> _shardMapManagerService;
        private readonly IStringLocalizer<PracticeDataAccessLayerStrings> _localizer;
        private readonly ILogger<PracticeDatabaseContext> _logger;

        public PracticeRepository(
            PracticeDatabaseContext context,
            ShardMapManagerService<PracticeDatabaseContext, PracticeDataAccessLayerStrings> shardMapManagerService,
            IStringLocalizer<PracticeDataAccessLayerStrings> localizer,
            ILogger<PracticeDatabaseContext> logger)
            : base(context, shardMapManagerService, localizer, logger)
        {
            _context = context;
            _shardMapManagerService = shardMapManagerService;
            _localizer = localizer;
            _logger = logger;
        }

        public async Task<bool> UpdateTasksByPatientIdAsync(Tasks tasks, Guid OrgId, bool Subscription)
        {
            if (tasks == null)
            {
                throw new ArgumentException("Task object cannot be null.", nameof(tasks));
            }
            var result = await _context.Tasks
                .Where(tasksList => tasksList.SSN == tasks.SSN)
                .FirstOrDefaultAsync();

            if (result == null)
            {
                throw new ArgumentException($"No task found for PatientId: {tasks.SSN}", nameof(tasks.SSN));
            }

            result.PatientName = tasks.PatientName;
            result.AssignedTo = tasks.AssignedTo;
            result.TaskType = tasks.TaskType;
            result.Subject = tasks.Subject;
            result.CreatedBy = tasks.CreatedBy;
            result.Status = tasks.Status;
            result.Frequency = tasks.Frequency;
            result.LastDueDate = tasks.LastDueDate;
            result.DueDate = tasks.DueDate;
            result.CreationDate = tasks.CreationDate;
            result.StartDate = tasks.StartDate;
            result.Priority = tasks.Priority;
            result.RecurringAction = tasks.RecurringAction;
            result.Notes = tasks.Notes;

            await _context.SaveChangesAsync();
            return true;
        }
    }
}
