﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DataAccessLayer.Context;
using Contracts;
using Microsoft.EntityFrameworkCore;
using Interfaces.ShardManagement;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;

namespace MemberServiceDataAccessLayer.Implementation
{
    public class RolesRepository : ShardGenericRepository<Role>, IRolesRepository
    {
        private readonly AccountDatabaseContext _context;

        public RolesRepository(AccountDatabaseContext context, ShardMapManagerService<AccountDatabaseContext, AccountDatabaseContext> _shardMapManagerService, IStringLocalizer<AccountDatabaseContext> localizer,
            ILogger<AccountDatabaseContext> logger) : base(context, _shardMapManagerService, localizer, logger)
        {
            _context = context;
        }
        public async Task<List<Role>> GetRolesByNameAsync(string name)
        {
            var result = await _context.Role
                .Where(role => role.RoleName.Contains(name, StringComparison.OrdinalIgnoreCase))
                .ToListAsync();
            return result;
        }

    }
}
