﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Stop" xml:space="preserve">
    <value>Stop</value>
  </data>
  <data name="mic" xml:space="preserve">
    <value>mic</value>
  </data>
  <data name="Play" xml:space="preserve">
    <value>Play</value>
  </data>
  <data name="pause" xml:space="preserve">
    <value>pause</value>
  </data>
  <data name="ErrorLoadingRecords" xml:space="preserve">
    <value>Error Loading Records</value>
  </data>
  <data name="ErrorSavingRecord" xml:space="preserve">
    <value>Error Saving Record</value>
  </data>
  <data name="Summary" xml:space="preserve">
    <value>Summary</value>
  </data>
  <data name="CurrentMedication" xml:space="preserve">
    <value>Current Medication</value>
  </data>
  <data name="MedicalHistory" xml:space="preserve">
    <value>Medical History</value>
  </data>
  <data name="SurgicalHistory" xml:space="preserve">
    <value>Surgical History</value>
  </data>
  <data name="HospitalizationHistory" xml:space="preserve">
    <value>Hospitalization History</value>
  </data>
  <data name="FamilyHistory" xml:space="preserve">
    <value>Family History</value>
  </data>
  <data name="SocialHistory" xml:space="preserve">
    <value>Social History</value>
  </data>
  <data name="Vitals" xml:space="preserve">
    <value>Vitals</value>
  </data>
  <data name="Assessment" xml:space="preserve">
    <value>Assessment</value>
  </data>
  <data name="TreatmentPlan" xml:space="preserve">
    <value>Treatment Plan</value>
  </data>
  <data name="Word" xml:space="preserve">
    <value>Word</value>
  </data>
  <data name="StartTime" xml:space="preserve">
    <value>Start Time</value>
  </data>
  <data name="EndTime" xml:space="preserve">
    <value>End Time</value>
  </data>
  <data name="Requestsuccessful" xml:space="preserve">
    <value>Request successful</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="ApiSettings:RegistrationUrl" xml:space="preserve">
    <value>ApiSettings:RegistrationUrl</value>
  </data>
  <data name="Transcription" xml:space="preserve">
    <value>Transcription</value>
  </data>
  <data name="ApiSettings:MembersUrl" xml:space="preserve">
    <value>ApiSettings:MembersUrl</value>
  </data>
  <data name="NoRecordsFound" xml:space="preserve">
    <value>No Records Found</value>
  </data>
  <data name="{productId}" xml:space="preserve">
    <value>{productId}</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="ApiSettings:ProductsUrl" xml:space="preserve">
    <value>ApiSettings:ProductsUrl</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="ApiSettings:UpdateAccessUrl" xml:space="preserve">
    <value>ApiSettings:UpdateAccessUrl</value>
  </data>
  <data name="ErrorFetchingLicenses" xml:space="preserve">
    <value>Error Fetching Licenses</value>
  </data>
  <data name="ErrorSavingLicenses" xml:space="preserve">
    <value>License Retrieval Failure</value>
  </data>
  <data name="ErrorFetchingProducts" xml:space="preserve">
    <value>ErrorFetchingProducts</value>
  </data>
  <data name="ErrorFetchingMembersForProduct" xml:space="preserve">
    <value>Error Fetching Members For Product</value>
  </data>
  <data name="ErrorSavingMemberAccessUpdates" xml:space="preserve">
    <value>ErrorSavingMemberAccessUpdates</value>
  </data>
  <data name="JsonDeserializationError" xml:space="preserve">
    <value>Json Deserialization Error</value>
  </data>
  <data name="UserAlreadyExists" xml:space="preserve">
    <value>User Already Exists</value>
  </data>
  <data name="RegistrationFailed" xml:space="preserve">
    <value>Registration Failed</value>
  </data>
  <data name="MemberRegistrationError" xml:space="preserve">
    <value>Member Registration Error</value>
  </data>
  <data name="LicenseRetrievalFailure" xml:space="preserve">
    <value>License Retrieval Failure</value>
  </data>
  <data name="ErrorDuringRegistration" xml:space="preserve">
    <value>Error During Registration</value>
  </data>
  <data name="UpdateAccessFailure" xml:space="preserve">
    <value>Update Access Failure</value>
  </data>
  <data name="ProductRetrievalFailure" xml:space="preserve">
    <value>Product Retrieval Failure</value>
  </data>
  <data name="Error Saving Licenses" xml:space="preserve">
    <value>Error Saving Licenses</value>
  </data>
  <data name="productId" xml:space="preserve">
    <value>product Id</value>
  </data>
  <data name="API Response: {ResponseData}" xml:space="preserve">
    <value> API Response: {ResponseData}</value>
  </data>
  <data name="Error deserializing response: {0}" xml:space="preserve">
    <value>Error deserializing response: {0}</value>
  </data>
  <data name="RecognitionaCanceled:{ErrorDetails}" xml:space="preserve">
    <value>RecognitionaCanceled:{ErrorDetails}</value>
  </data>
  <data name="RegistrationSuccessful" xml:space="preserve">
    <value>Registration Successful</value>
  </data>
  <data name="UsernameRequired" xml:space="preserve">
    <value>Username Required</value>
  </data>
  <data name="ReenterPasswordRequired" xml:space="preserve">
    <value>Reenter Password Required</value>
  </data>
  <data name="PasswordsDoNotMatch" xml:space="preserve">
    <value>Passwords Do Not Match</value>
  </data>
  <data name="stop_circle" xml:space="preserve">
    <value>stop_circle</value>
  </data>
  <data name="play_arrow" xml:space="preserve">
    <value>play_arrow</value>
  </data>
  <data name="BlazorAudioRecorder.Initialize" xml:space="preserve">
    <value>BlazorAudioRecorder.Initialize</value>
  </data>
  <data name="BlazorAudioRecorder.StartRecord" xml:space="preserve">
    <value>BlazorAudioRecorder.StartRecord</value>
  </data>
  <data name="BlazorAudioRecorder.StopRecord" xml:space="preserve">
    <value>BlazorAudioRecorder.StopRecord</value>
  </data>
  <data name="BlazorAudioRecorder.PauseRecord" xml:space="preserve">
    <value>BlazorAudioRecorder.PauseRecord</value>
  </data>
  <data name="BlazorAudioRecorder.ResumeRecord" xml:space="preserve">
    <value>BlazorAudioRecorder.ResumeRecord</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="Description" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="Activated" xml:space="preserve">
    <value>Activated</value>
  </data>
  <data name="Byproduct" xml:space="preserve">
    <value>Byproduct</value>
  </data>
  <data name="Has Access" xml:space="preserve">
    <value>Has Access</value>
  </data>
  <data name="Username" xml:space="preserve">
    <value>Username</value>
  </data>
  <data name="CacheHit" xml:space="preserve">
    <value>Cache hit for key: {Key}</value>
  </data>
  <data name="CacheMiss" xml:space="preserve">
    <value>Cache miss for key: {Key}</value>
  </data>
  <data name="ErrorCache" xml:space="preserve">
    <value>Error accessing cache for key: {Key}</value>
  </data>
  <data name="ErrorRemovingCache" xml:space="preserve">
    <value>Error removing cache key: {Key}</value>
  </data>
  <data name="RemovedCache" xml:space="preserve">
    <value>Removed {Count} cache keys matching pattern: {Pattern}</value>
  </data>
  <data name="RemoveError" xml:space="preserve">
    <value>Error removing cache keys with pattern: {Pattern}</value>
  </data>
  <data name="true" xml:space="preserve">
    <value>true</value>
  </data>
  <data name="Id" xml:space="preserve">
    <value>Id</value>
  </data>
  <data name="PhoneNumber" xml:space="preserve">
    <value>Phone Number</value>
  </data>
  <data name="SSN" xml:space="preserve">
    <value>SSN</value>
  </data>
  <data name="cacheKey" xml:space="preserve">
    <value>users:all</value>
  </data>
  <data name="Patient:" xml:space="preserve">
    <value>Patient:</value>
  </data>
  <data name="appointments" xml:space="preserve">
    <value>appointments</value>
  </data>
  <data name="DateOfBirth" xml:space="preserve">
    <value>Date Of Birth</value>
  </data>
  <data name="AddAppointment" xml:space="preserve">
    <value>Add Appointment</value>
  </data>
  <data name="PasswordRequired" xml:space="preserve">
    <value>PasswordRequired</value>
  </data>
  <data name="PatientName" xml:space="preserve">
    <value>Patient Name</value>
  </data>
  <data name="InvalidEmailFormat" xml:space="preserve">
    <value>InvalidEmailFormat</value>
  </data>
  <data name="TimeMismatchError" xml:space="preserve">
    <value>End time must be after start time.</value>
  </data>
  <data name="EmailRequired" xml:space="preserve">
    <value>EmailRequired</value>
  </data>
  <data name="Submit" xml:space="preserve">
    <value>Submit</value>
  </data>
  <data name="RecordDate:" xml:space="preserve">
    <value>Record Date:</value>
  </data>
  <data name="RecordID:" xml:space="preserve">
    <value>Record ID:</value>
  </data>
  <data name="SearchBy:" xml:space="preserve">
    <value>Search By:</value>
  </data>
  <data name="en-US" xml:space="preserve">
    <value>en-US</value>
  </data>
  <data name="SearchUser" xml:space="preserve">
    <value>Search User:</value>
  </data>
  <data name="NoSpeechRecognized" xml:space="preserve">
    <value>No speech could be recognized</value>
  </data>
  <data name="NoUserFound" xml:space="preserve">
    <value>No user found for selecting criteria</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="StartTime:" xml:space="preserve">
    <value>Start Time:</value>
  </data>
  <data name="EndTime:" xml:space="preserve">
    <value>End Time:</value>
  </data>
  <data name="Recognized:{Text}" xml:space="preserve">
    <value>Recognized:{Text}</value>
  </data>
  <data name="Date:" xml:space="preserve">
    <value>Date:</value>
  </data>
  <data name="SpeechNotRecognized" xml:space="preserve">
    <value>SpeechNotRecognized</value>
  </data>
  <data name="EnterSearchTerm" xml:space="preserve">
    <value>Enter search term</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="ErrorPostingData:{StatusCode}" xml:space="preserve">
    <value>ErrorPostingData:{StatusCode}</value>
  </data>
  <data name="&quot;500&quot;" xml:space="preserve">
    <value>500</value>
  </data>
  <data name="audio/wav" xml:space="preserve">
    <value>audio/wav</value>
  </data>
  <data name="wav" xml:space="preserve">
    <value>wav</value>
  </data>
  <data name="&quot;0&quot;" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&quot;1024&quot;" xml:space="preserve">
    <value>1024</value>
  </data>
  <data name="UploadError" xml:space="preserve">
    <value>UploadError</value>
  </data>
  <data name="TaskType" xml:space="preserve">
    <value>Task Type</value>
  </data>
  <data name="Subject" xml:space="preserve">
    <value>Subject</value>
  </data>
  <data name="AssignedTo" xml:space="preserve">
    <value>Assigned To</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="CreatedBy" xml:space="preserve">
    <value>Created By</value>
  </data>
  <data name="Priority" xml:space="preserve">
    <value>Priority</value>
  </data>
  <data name="Notes" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="RecurringAction" xml:space="preserve">
    <value>Recurring Action</value>
  </data>
  <data name="Frequency" xml:space="preserve">
    <value>Frequency</value>
  </data>
  <data name="CreationDate" xml:space="preserve">
    <value>Creation Date</value>
  </data>
  <data name="StartDate" xml:space="preserve">
    <value>Start Date</value>
  </data>
  <data name="DueDate" xml:space="preserve">
    <value>Due Date</value>
  </data>
  <data name="LastDueDate" xml:space="preserve">
    <value>Last Due Date</value>
  </data>
  <data name="LastVisitDate" xml:space="preserve">
    <value>Last Visit Date</value>
  </data>
  <data name="SearchProvider:" xml:space="preserve">
    <value>Search Provider :</value>
  </data>
  <data name="Add Text Box" xml:space="preserve">
    <value>Add Text Box</value>
  </data>
  <data name="Submit Form" xml:space="preserve">
    <value>Submit Form</value>
  </data>
  <data name="Add Template" xml:space="preserve">
    <value>Add Template</value>
  </data>
  <data name="TemplateName" xml:space="preserve">
    <value>TemplateName</value>
  </data>
  <data name="IsDefault" xml:space="preserve">
    <value>IsDefault</value>
  </data>
  <data name="template" xml:space="preserve">
    <value>template</value>
  </data>
  <data name="Axis" xml:space="preserve">
    <value>Axis</value>
  </data>
  <data name="Diagnosis" xml:space="preserve">
    <value>Diagnosis</value>
  </data>
  <data name="Specify" xml:space="preserve">
    <value>Specify</value>
  </data>
  <data name="Actions" xml:space="preserve">
    <value>Actions</value>
  </data>
  <data name="Search ICD By Codes or Description" xml:space="preserve">
    <value>Search ICD By Codes or Description</value>
  </data>
  <data name="User License Management" xml:space="preserve">
    <value>User License Management</value>
  </data>
  <data name="SearchBrandNames" xml:space="preserve">
    <value>Search Brand Names</value>
  </data>
  <data name="DosageAndInTake" xml:space="preserve">
    <value>Dosage&amp;InTake</value>
</data>
  <data name="ChiefComplaint" xml:space="preserve">
    <value>Chief Complaint</value>
  </data>
  <data name="GynecologyHistory" xml:space="preserve">
    <value>Gynecology History</value>
  </data>
  <data name="ObstetricHistory" xml:space="preserve">
    <value>Obstetric History</value>
  </data>
  <data name="InitializationError" xml:space="preserve">
    <value>Initialization Error</value>
  </data>
  <data name="SaveError" xml:space="preserve">
    <value>An error occurred while saving changes. Please try again</value>
  </data>
  <data name="ErrorLoading" xml:space="preserve">
    <value>Error in loading the data</value>
  </data>
  <data name="SearchICD" xml:space="preserve">
    <value>Search ICD by Code or Description</value>

  </data>
  <data name="Add" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="BrandName" xml:space="preserve">
    <value>Brand Name</value>
  </data>
  <data name="DrugDetails" xml:space="preserve">
    <value>Drug Details</value>
  </data>
  <data name="Quantity" xml:space="preserve">
    <value>Quantity</value>
</data>
  <data name="Date" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="EnterComplaintError" xml:space="preserve">
    <value>Please Enter a Complaint</value>
  </data>
  <data name="InitializationError" xml:space="preserve">
    <value>Initialization Error</value>
  </data>
  <data name="ComplaintAdded" xml:space="preserve">
    <value>Complaint Added Successfully</value>
  </data>
  <data name="ErrorAddingComplaint" xml:space="preserve">
    <value>Error  Adding Complaint</value>
  </data>
  <data name="RecordSaved" xml:space="preserve">
    <value>Changes Saved Successfully</value>
  </data>
  <data name="ErrorSavingChanges" xml:space="preserve">
    <value>Error in Saving Changes</value>
  </data>
  <data name="ChangesCanceled" xml:space="preserve">
    <value>Cancel Changes Successfully</value>
  </data>
  <data name="ErrorLoadingComplaints" xml:space="preserve">
    <value>Error in Loading Complaints</value>
  </data>
  <data name="HistoryReason" xml:space="preserve">
    <value>History/Reason</value>
  </data>
  <data name="NoDate" xml:space="preserve">
    <value>No Date</value>
  </data>
  <data name="EnterHistory" xml:space="preserve">
    <value>Enter History</value>
  </data>
  <data name="NoDescriptionAvailable" xml:space="preserve">
    <value>No Description Available</value>
  </data>
  <data name="SearchICDData" xml:space="preserve">
    <value>Search ICD Data</value>
  </data>
  <data name="SurgeryReason" xml:space="preserve">
    <value>Surgery/Reason</value>
  </data>
  <data name="Future.DateError" xml:space="preserve">
    <value>Date cannot be in the future</value>
  </data>
  <data name="ErrorRetrievingICDCodes" xml:space="preserve">
    <value>Error Retrieving ICD Codes</value>
  </data>
  <data name="AllergyCol5" xml:space="preserve">
    <value>Drug Name</value>
  </data>
  <data name="Examinations" xml:space="preserve">
    <value>Examinations</value>
  </data>
  <data name="GeneralDescription" xml:space="preserve">
    <value>General Description</value>
  </data>
  <data name="HEENT" xml:space="preserve">
    <value>HEENT</value>
  </data>
  <data name="Lungs" xml:space="preserve">
    <value>Lungs</value>
  </data>
  <data name="Abdomen" xml:space="preserve">
    <value>Abdomen</value>
  </data>
  <data name="PeripheralPulses" xml:space="preserve">
    <value>Peripheral Pulses</value>
  </data>
  <data name="Skin" xml:space="preserve">
    <value>Skin</value>
  </data>
  <data name="Others" xml:space="preserve">
    <value>Others</value>
  </data>
  <data name="Validation.StartDateFuture" xml:space="preserve">
    <value>Start Date cannot be in the future</value>
  </data>
  <data name="Validation.EndDatePast" xml:space="preserve">
    <value>End Date cannot be in the past</value>
  </data>
  <data name="Validation.EndBeforeStart" xml:space="preserve">
    <value>End Date cannot be before Start Date</value>
  </data>
  <data name="Validation.NotesAlphaOnly" xml:space="preserve">
    <value>Notes field should only contain alphabets and spaces</value>
  </data>
  <data name="Validation.SeverityAlphaOnly" xml:space="preserve">
    <value>Severity field should only contain alphabets and spaces</value>
  </data>
  <data name="Validation.LocationAlphaOnly" xml:space="preserve">
    <value>Location field should only contain alphabets and spaces</value>
  </data>
  <data name="Error.SaveFailed" xml:space="preserve">
    <value>Failed to save changes. Please try again.</value>
  </data>
  <data name="HistoryOfPresentIllness" xml:space="preserve">
    <value>History Of Present Illness</value>
  </data>
  <data name="EndDate" xml:space="preserve">
    <value>End Date</value>
  </data>
  <data name="ChiefComplaint" xml:space="preserve">
    <value>Chief Complaint</value>
  </data>
  <data name="Validation.FillBrandAndDrugDetails" xml:space="preserve">
    <value>Please fill in both Brand Name and Drug Details fields</value>
  </data>
  <data name="Validation.StartDateAfterEndDate" xml:space="preserve">
    <value>Start Date cannot be after End Date</value>
</data>
  <data name="Symptom" xml:space="preserve">
    <value>Symptom</value>
  </data>
  <data name="Location" xml:space="preserve">
    <value>Location</value>
  </data>
  <data name="Severity" xml:space="preserve">
    <value>Severity</value>
  </data>
  <data name="SymptomWarning" xml:space="preserve">
    <value>Please Fill the Symptom Field</value>
  </data>
  <data name="BloodPressure" xml:space="preserve">
    <value>Blood Pressure</value>
  </data>
  <data name="Temperature" xml:space="preserve">
    <value>Temperature</value>
  </data>
  <data name="Pulse" xml:space="preserve">
    <value>Pulse</value>
  </data>
  <data name="Weight" xml:space="preserve">
    <value>Weight</value>
  </data>
  <data name="Height" xml:space="preserve">
    <value>Height</value>
  </data>
  <data name="ConfirmDelete" xml:space="preserve">
    <value>Confirm Delete</value>
  </data>
  <data name="DeleteConfirmationMessage" xml:space="preserve">
    <value>Do you want to delete this entry?</value>
  </data>
  <data name="Validation.MedicationRequiresChiefComplaint" xml:space="preserve">
    <value>Each medication must have a Chief Complaint.</value>
  </data>
  <data name="RecordSaved" xml:space="preserve">
    <value>Changes Saved Successfully</value>
  </data>
  <data name="ChangesCancelled" xml:space="preserve">
    <value>Cancel Changes Successfully</value>
  </data>
  <data name="Validation.GeneralDescriptionAlphaOnly" xml:space="preserve">
    <value>General Description field should only contain alphabets and spaces</value>
  </data>
  <data name="Validation.HEENTAlphaOnly" xml:space="preserve">
    <value>HEENT field should only contain alphabets and spaces</value>
  </data>
  <data name="Validation.LungsAlphaOnly" xml:space="preserve">
    <value>Lungs field should only contain alphabets and spaces</value>
  </data>
  <data name="Validation.AbdomenAlphaOnly" xml:space="preserve">
    <value>Abdomen field should only contain alphabets and spaces</value>
  </data>
  <data name="Validation.PeripheralPulsesAlphaOnly" xml:space="preserve">
    <value>Peripheral Pulses field should only contain alphabets and spaces</value>
  </data>
  <data name="Validation.SkinAlphaOnly" xml:space="preserve">
    <value>Skin field should only contain alphabets and spaces</value>
  </data>
  <data name="CreatedDate" xml:space="preserve">
    <value>Date</value>
  </data>

  <data name="Yes" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>No</value>
  </data>

  <data name="AllSeverities" xml:space="preserve">
    <value>All Severities</value>
  </data>
  <data name="High" xml:space="preserve">
    <value>High</value>
  </data>
  <data name="Medium" xml:space="preserve">
    <value>Medium</value>
  </data>
  <data name="Low" xml:space="preserve">
    <value>Low</value>
  </data>
  <data name="AlertType" xml:space="preserve">
    <value>Alert Type</value>
  </data>
  <data name="Reason" xml:space="preserve">
    <value>Reason</value>
  </data>
  <data name="PatientLabel" xml:space="preserve">
    <value>PatientLabel</value>
  </data>
  <data name="AlertTypeLabel" xml:space="preserve">
    <value>AlertTypeLabel</value>
  </data>
  <data name="SeverityLabel" xml:space="preserve">
    <value>SeverityLabel</value>
  </data>
  <data name="InteractingMedicationsLabel" xml:space="preserve">
    <value>InteractingMedicationsLabel</value>
  </data>
  <data name="MarkAsReviewed" xml:space="preserve">
    <value>MarkAsReviewed</value>
  </data>
  <data name="Custom Lab Alerts" xml:space="preserve">
    <value>Custom Lab Alerts</value>
  </data>
  <data name="DatabaseError" xml:space="preserve">
    <value>DatabaseError</value>
  </data>
  <data name="Dismiss" xml:space="preserve">
    <value>Dismiss</value>
  </data>
  <data name="ViewDetails" xml:space="preserve">
    <value>View Details</value>
  </data>
  <data name="Something went wrong" xml:space="preserve">
    <value>Something went wrong</value>
  </data>
  <data name="Sorry, an error occurred while rendering this page." xml:space="preserve">
    <value>Sorry, an error occurred while rendering this page.</value>
  </data>
  <data name="Try Again" xml:space="preserve">
    <value>Try Again</value>
  </data>
  <data name="ReferralFrom" xml:space="preserve">
    <value>Referral From</value>
  </data>
  <data name="ReferralTo" xml:space="preserve">
    <value>Referral To</value>
  </data>
  <data name="ReferralReason" xml:space="preserve">
    <value>Referral Reason</value>
  </data>
  <data name="TherapeuticInterventions" xml:space="preserve">
    <value>Therapeutic Interventions</value>
  </data>
  <data name="SearchTherapy" xml:space="preserve">
    <value>Search Therapy</value>
  </data>
  <data name="TherapyType" xml:space="preserve">
    <value>Therapy Type</value>
  </data>
  <data name="UpdatedDate" xml:space="preserve">
    <value>Updated Date</value>
  </data>
  <data name="ReferralOutgoing" xml:space="preserve">
    <value>Referral Outgoing</value>
  </data>

	<data name="Validation.TemperatureNumericOnly" xml:space="preserve">
  <value>Temperature must be a numeric value.</value>
</data>
	<data name="Validation.BPNumericOnly" xml:space="preserve">
  <value>Blood Pressure must be a numeric value.</value>
</data>
	<data name="Validation.PulseNumericOnly" xml:space="preserve">
  <value>Pulse must be a numeric value.</value>
</data>
	<data name="Validation.WeightNumericOnly" xml:space="preserve">
  <value>Weight must be a numeric value.</value>
</data>
	<data name="Validation.HeightNumericOnly" xml:space="preserve">
  <value>Height must be a numeric value.</value>
</data>

  <data name="Blue" xml:space="preserve">
    <value>#4285F4</value>
  </data>
  <data name="Red" xml:space="preserve">
    <value>#EA4335</value>
  </data>
  <data name="Green" xml:space="preserve">
    <value>#34A853</value>
  </data>
  <data name="Yellow" xml:space="preserve">
    <value>#FBBC05</value>
  </data>
  <data name="Purple" xml:space="preserve">
    <value>#8E24AA</value>
  </data>
  <data name="Cyan" xml:space="preserve">
    <value>#00ACC1</value>
  </data>
  <data name="Orange" xml:space="preserve">
    <value>#FB8C00</value>
  </data>
  <data name="Blue Gray" xml:space="preserve">
    <value>#607D8B</value>
  </data>
  <data name="Pink" xml:space="preserve">
    <value>#D81B60</value>
  </data>
  <data name="Light Blue" xml:space="preserve">
    <value>#1E88E5</value>
  </data>
  <data name="Light Green" xml:space="preserve">
    <value>#43A047</value>
  </data>
  <data name="Brown" xml:space="preserve">
    <value>#6D4C41</value>
  </data>
  <data name="Validate.CreateDate" xml:space="preserve">
    <value>Future dates are not allowed.</value>
  </data>
</root>

