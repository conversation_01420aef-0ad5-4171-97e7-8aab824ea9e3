﻿@inherits LayoutComponentBase
@inject INavigationService NavigationService
@inject NavigationManager Navigation

<div class="main-layout">
    <NavMenu />

    <main class="main-content">
        @Body
    </main>
</div>

<style>
    :root {
        --nav-height: 60px;
    }

    .main-layout {
        display: flex;
        flex-direction: column;
        min-height: 100vh;
        background: linear-gradient(180deg, #ffffff 0%, #d0f0f9 100%);
        color: #111;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .main-content {
        flex-grow: 1;
        padding: calc(var(--nav-height) + 20px) 24px 40px;
        width: 100%;
        max-width: var(--content-max-width);
        background: linear-gradient(180deg, #ffffff 0%, #d0f0f9 100%);
        box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        min-height: calc(100vh - var(--nav-height));
        -webkit-backdrop-filter: blur(10px);
        color: #111;
    }

    /* Responsive adjustments */
    @@media (max-width: 768px) {
        .main-content {
            padding: calc(var(--nav-height) + 40px) 16px 30px;
        }
    }
</style>

@code {
    protected override void OnInitialized()
    {
        NavigationService.Initialize(Navigation);
    }
}