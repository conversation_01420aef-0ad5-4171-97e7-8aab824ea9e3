using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Configuration;
using MemberServiceBusinessLayer.CommandHandler;
using MemberServiceDataAccessLayer.Implementation;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Interfaces.ShardManagement;
using DataAccessLayer.Context;
using ShardModels;

namespace MemberServiceApiBusinessLayerTestCases.CommandHandlerTests
{
    [TestFixture]
    public class MemberCommandHandlerTests
    {
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<IMemberRepository> _mockMemberRepository;
        private Mock<IMigration<AccountDatabaseContext, Member, AccountDatabaseContext>> _mockMigration;
        private MemberCommandHandler _memberCommandHandler;

        [SetUp]
        public void Setup()
        {
            _mockConfiguration = new Mock<IConfiguration>();
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockMemberRepository = new Mock<IMemberRepository>();
            _mockMigration = new Mock<IMigration<AccountDatabaseContext, Member, AccountDatabaseContext>>();

            _mockUnitOfWork.Setup(u => u.MemberRepository).Returns(_mockMemberRepository.Object);

            _memberCommandHandler = new MemberCommandHandler(
                _mockConfiguration.Object,
                _mockUnitOfWork.Object,
                _mockMigration.Object
            );
        }

        [Test]
        public async Task AddMember_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var members = new List<Member>
            {
                new Member { Id = Guid.NewGuid(), FirstName = "John", LastName = "Doe", OrganizationID = orgId, Subscription = subscription }
            };

            _mockMemberRepository
                .Setup(r => r.AddAsync(members, orgId, subscription))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _memberCommandHandler.AddMember(members);

            // Assert
            _mockMemberRepository.Verify(r => r.AddAsync(members, orgId, subscription), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }

        [Test]
        public void AddMember_WhenOrganizationIdIsNull_ShouldThrowInvalidOperationException()
        {
            // Arrange
            var members = new List<Member>
            {
                new Member { Id = Guid.NewGuid(), FirstName = "John", LastName = "Doe", OrganizationID = null }
            };

            // Act & Assert
            var exception = Assert.ThrowsAsync<InvalidOperationException>(async () =>
                await _memberCommandHandler.AddMember(members));

            Assert.That(exception.Message, Is.EqualTo("OrganizationID cannot be null"));
            _mockMemberRepository.Verify(r => r.AddAsync(It.IsAny<List<Member>>(), It.IsAny<Guid>(), It.IsAny<bool>()), Times.Never);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Never);
        }

        [Test]
        public async Task UpdateMember_ShouldCallRepositoryAndSaveChanges()
        {
            // Arrange
            var memberId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var member = new Member
            {
                Id = memberId,
                FirstName = "John",
                LastName = "Doe",
                OrganizationID = orgId,
                Subscription = subscription,
                PCPName = "Dr. Smith",
                PCPId = Guid.NewGuid(),
                UserName = "johndoe"
            };

            _mockMemberRepository
                .Setup(r => r.UpdateAsync(member, orgId, subscription))
                .Returns(Task.CompletedTask);

            _mockUnitOfWork
                .Setup(u => u.SaveAsync())
                .ReturnsAsync(1);

            // Act
            await _memberCommandHandler.UpdateMember(member, orgId, subscription);

            // Assert
            _mockMemberRepository.Verify(r => r.UpdateAsync(member, orgId, subscription), Times.Once);
            _mockUnitOfWork.Verify(u => u.SaveAsync(), Times.Once);
        }


    }
}
