﻿using Contracts;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MemberServiceBusinessLayer;
using System.Security.Cryptography;

namespace MemberServiceApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class EmployerController : ControllerBase
    {
        private readonly IEmployerQueryHandler<Employer> _EmployerQueryHandler;
        private readonly IEmployerCommandHandler<Employer> _EmployerCommandHandler;
        private readonly ILogger<EmployerController> _logger;
        private readonly IStringLocalizer<EmployerController> _localizer;

        public EmployerController(
            IEmployerQueryHandler<Employer> EmployerQueryHandler,
            IEmployerCommandHandler<Employer> EmployerCommandHandler,
            ILogger<EmployerController> logger,
            IStringLocalizer<EmployerController> localizer)
        {
            _EmployerQueryHandler = EmployerQueryHandler ?? throw new ArgumentNullException(nameof(EmployerQueryHandler));
            _EmployerCommandHandler = EmployerCommandHandler ?? throw new ArgumentNullException(nameof(EmployerCommandHandler));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _localizer = localizer ?? throw new ArgumentNullException(nameof(localizer));
        }

        [HttpGet("{id:Guid}/{OrgID:Guid}/{Subscription}")]

        public async Task<IActionResult> GetEmployerById(bool Subscription, Guid id, Guid OrgID)
        {
            IActionResult response;
            _logger.LogInformation(_localizer["FetchingAddressWithID"], id);
            var Employer = await _EmployerQueryHandler.GetEmployerByIdAsync(id, OrgID, Subscription);
            if (Employer == null)
            {
                _logger.LogWarning(_localizer["AddressNotFound"], id);
                response = NotFound(_localizer["AddressNotFoundMessage"]);
            }
            else
            {
                _logger.LogInformation(_localizer["AddressFetchedSuccessfully"], id);
                response = Ok(Employer);
            }
            return response;
        }

        [HttpPost]
        public async Task<IActionResult> AddEmployer([FromBody] Employer Employers)
        {
            IActionResult response;
            if (Employers == null)
            {
                _logger.LogWarning(_localizer["InvalidAddressData"]);
                response = BadRequest(_localizer["AddressInvalidMessage"]);
            }
            else
            {
                try
                {
                    _logger.LogInformation(_localizer["AddingNewAddress"]);
                    await _EmployerCommandHandler.AddEmployerAsync(new List<Employer> { Employers }, Employers.OrganizationId, Employers.Subscription);
                    _logger.LogInformation(_localizer["AddressAddedSuccessfully"], Employers.EmployerId);
                    //response = CreatedAtAction(nameof(GetEmployerById), new { id = Employers.EmployerId }, Employers);
                    response = CreatedAtAction(nameof(GetEmployerById), new
                    {
                        id = Employers.EmployerId,
                        OrgID = Employers.OrganizationId,
                        Employers.Subscription
                    }, new
                    {
                        Employers.EmployerId,
                        Employers.OrganizationId,
                        Employers.Subscription
                    });

                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["ErrorAddingAddress"]);
                    response = StatusCode(500, _localizer["InternalServerErrorMessage"]);
                }
            }
            return response;
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateEmployer(Guid id, [FromBody] Employer Employer)
        {
            IActionResult response;
            if (Employer == null || id != Employer.EmployerId)
            {
                _logger.LogWarning(_localizer["InvalidDataForUpdate"], id);
                response = BadRequest(_localizer["AddressInvalidMessage"]);
            }
            else
            {
                try
                {
                    _logger.LogInformation(_localizer["UpdatingAddressWithID"], id);
                    await _EmployerCommandHandler.UpdateEmployerAsync(Employer, Employer.OrganizationId, Employer.Subscription);
                    _logger.LogInformation(_localizer["AddressUpdatedSuccessfully"], id);
                    response = NoContent();
                }
                catch (KeyNotFoundException)
                {
                    _logger.LogWarning(_localizer["AddressNotFoundForUpdate"], id);
                    response = NotFound(_localizer["AddressNotFoundMessage"]);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, _localizer["ErrorUpdatingAddress"]);
                    response = StatusCode(500, _localizer["InternalServerErrorMessage"]);
                }
            }
            return response;
        }

        [HttpDelete("{id:guid}/{OrgID:Guid}/{Subscription}")]
        public async Task<IActionResult> DeleteEmployer(bool Subscription, Guid id, Guid OrgID)
        {
            IActionResult response;
            try
            {
                _logger.LogInformation(_localizer["DeletingAddressWithID"], id);
                await _EmployerCommandHandler.DeleteEmployerAsync(id, OrgID, Subscription);
                _logger.LogInformation(_localizer["AddressDeletedSuccessfully"], id);
                response = NoContent();
            }
            catch (KeyNotFoundException)
            {
                _logger.LogWarning(_localizer["AddressNotFoundForDeletion"], id);
                response = NotFound(_localizer["AddressNotFoundMessage"]);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorDeletingAddress"]);
                response = StatusCode(500, _localizer["InternalServerErrorMessage"]);
            }
            return response;
        }

        [HttpGet]
        [Route("{orgId:guid}/{Subscription}")]
        public async Task<IActionResult> GetAllEmployer(Guid orgId, bool Subscription)
        {
            IActionResult response;
            _logger.LogInformation(_localizer["FetchingAllAddresses"]);
            try
            {
                var Employer = await _EmployerQueryHandler.GetAllEmployerAsync(orgId, Subscription);

                if (Employer == null || Employer.Count == 0)
                {
                    _logger.LogWarning(_localizer["NoAddressesFound"]);
                    response = NotFound(_localizer["AddressesNotFoundMessage"]);
                }
                else
                {
                    _logger.LogInformation(_localizer["AllAddressesFetchedSuccessfully"]);
                    response = Ok(Employer);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, _localizer["ErrorFetchingAddresses"]);
                response = StatusCode(500, _localizer["InternalServerErrorMessage"]);
            }
            return response;
        }
    }
}
