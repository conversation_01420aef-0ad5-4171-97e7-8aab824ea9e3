﻿
namespace Contracts
{
    public class Product:IContract
    {
        public Guid Id { get; set; }
        public string? Name { get; set; }

        public string? Description { get; set; }

        public string? ByProduct { get; set; }
        public bool Subscription { get; set; }
        public Guid OrganizationId { get; set; }
        public ICollection<ProductUserAccess> ProductUserAccess { get; set; }

    }
}
