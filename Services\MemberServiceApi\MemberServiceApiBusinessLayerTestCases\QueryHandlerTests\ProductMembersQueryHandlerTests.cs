using NUnit.Framework;
using Moq;
using Microsoft.Extensions.Configuration;
using MemberServiceBusinessLayer.QueryHandler;
using MemberServiceDataAccessLayer.Implementation;
using Contracts;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;

namespace MemberServiceApiBusinessLayerTestCases.QueryHandlerTests
{
    [TestFixture]
    public class ProductMembersQueryHandlerTests
    {
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IUnitOfWork> _mockUnitOfWork;
        private Mock<IProductUserAccessRepository> _mockProductUserAccessRepository;
        private ProductMembersQueryHandler _productMembersQueryHandler;

        [SetUp]
        public void Setup()
        {
            _mockConfiguration = new Mock<IConfiguration>();
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockProductUserAccessRepository = new Mock<IProductUserAccessRepository>();

            _mockUnitOfWork.Setup(u => u.ProductUserAccessRepository).Returns(_mockProductUserAccessRepository.Object);

            _productMembersQueryHandler = new ProductMembersQueryHandler(_mockConfiguration.Object, _mockUnitOfWork.Object);
        }

        [Test]
        public async Task GetProductMembers_ShouldReturnMembers_WhenMembersExist()
        {
            // Arrange
            var productId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var expectedMembers = new List<Member>
            {
                new Member 
                { 
                    Id = Guid.NewGuid(), 
                    FirstName = "John",
                    LastName = "Doe",
                    Email = "<EMAIL>"
                },
                new Member 
                { 
                    Id = Guid.NewGuid(), 
                    FirstName = "Jane",
                    LastName = "Smith",
                    Email = "<EMAIL>"
                }
            };

            _mockProductUserAccessRepository
                .Setup(r => r.GetMembersForProduct(productId, orgId, subscription))
                .ReturnsAsync(expectedMembers);

            // Act
            var result = await _productMembersQueryHandler.GetProductMembers(productId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(2));
            Assert.That(result, Is.EquivalentTo(expectedMembers));
            _mockProductUserAccessRepository.Verify(r => r.GetMembersForProduct(productId, orgId, subscription), Times.Once);
        }

        [Test]
        public async Task GetProductMembers_ShouldReturnEmptyList_WhenNoMembersExist()
        {
            // Arrange
            var productId = Guid.NewGuid();
            var orgId = Guid.NewGuid();
            bool subscription = false;
            var emptyList = new List<Member>();

            _mockProductUserAccessRepository
                .Setup(r => r.GetMembersForProduct(productId, orgId, subscription))
                .ReturnsAsync(emptyList);

            // Act
            var result = await _productMembersQueryHandler.GetProductMembers(productId, orgId, subscription);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(0));
            _mockProductUserAccessRepository.Verify(r => r.GetMembersForProduct(productId, orgId, subscription), Times.Once);
        }
    }
}
