using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using PracticeContracts;
using PracticeDataAccessLayer.Context;
using PracticeDataAccessLayer.Implementation;
using PracticeDataAccessLayer.PracticeDataAccessLayerResources;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace PracticeApiDataAccessLayerTestCases
{
    [TestFixture]
    public class ProviderPatientRepositoryTests
    {
        private Mock<PracticeDatabaseContext> _mockContext;
        private Mock<DbSet<ProviderPatient>> _mockDbSet;
        private ProviderPatientRepository _repository;
        private List<ProviderPatient> _testProviderPatients;
        private ProviderPatient _testProviderPatient;

        [SetUp]
        public void Setup()
        {
            // Create test data
            _testProviderPatient = new ProviderPatient
            {
                Id = Guid.NewGuid(),
                PCPId = Guid.NewGuid(),
                SSN = "***********",
                Username = "testuser"
            };

            _testProviderPatients = new List<ProviderPatient>
            {
                _testProviderPatient,
                new ProviderPatient
                {
                    Id = Guid.NewGuid(),
                    PCPId = Guid.NewGuid(),
                    SSN = "***********",
                    Username = "anotheruser"
                }
            };

            // Setup mock DbSet
            _mockDbSet = new Mock<DbSet<ProviderPatient>>();
            var queryable = _testProviderPatients.AsQueryable();

            _mockDbSet.As<IQueryable<ProviderPatient>>().Setup(m => m.Provider).Returns(queryable.Provider);
            _mockDbSet.As<IQueryable<ProviderPatient>>().Setup(m => m.Expression).Returns(queryable.Expression);
            _mockDbSet.As<IQueryable<ProviderPatient>>().Setup(m => m.ElementType).Returns(queryable.ElementType);
            _mockDbSet.As<IQueryable<ProviderPatient>>().Setup(m => m.GetEnumerator()).Returns(() => queryable.GetEnumerator());
            _mockDbSet.Setup(m => m.FindAsync(It.IsAny<object[]>())).ReturnsAsync((object[] ids) => _testProviderPatients.FirstOrDefault(p => p.Id.Equals((Guid)ids[0])));
            _mockDbSet.Setup(m => m.AddRangeAsync(It.IsAny<IEnumerable<ProviderPatient>>(), default))
                .Returns(Task.CompletedTask);

            // Setup mock context
            _mockContext = new Mock<PracticeDatabaseContext>(
                new DbContextOptions<PracticeDatabaseContext>(),
                Mock.Of<IStringLocalizer<PracticeDataAccessLayerStrings>>(),
                Mock.Of<ILogger<PracticeDatabaseContext>>());
            _mockContext.Setup(c => c.Set<ProviderPatient>()).Returns(_mockDbSet.Object);
            _mockContext.Setup(c => c.SaveChangesAsync(default)).ReturnsAsync(1);

            // Create repository with mock context
            _repository = new ProviderPatientRepository(_mockContext.Object);
        }

        [Test]
        public async Task GetAllAsync_ShouldReturnAllProviderPatients()
        {
            // Create a new mock DbSet specifically for this test
            var mockDbSet = new Mock<DbSet<ProviderPatient>>();
            var queryable = _testProviderPatients.AsQueryable();

            // Setup IQueryable
            mockDbSet.As<IQueryable<ProviderPatient>>().Setup(m => m.Provider).Returns(queryable.Provider);
            mockDbSet.As<IQueryable<ProviderPatient>>().Setup(m => m.Expression).Returns(queryable.Expression);
            mockDbSet.As<IQueryable<ProviderPatient>>().Setup(m => m.ElementType).Returns(queryable.ElementType);
            mockDbSet.As<IQueryable<ProviderPatient>>().Setup(m => m.GetEnumerator()).Returns(queryable.GetEnumerator);

            // Setup IAsyncEnumerable
            mockDbSet.As<IAsyncEnumerable<ProviderPatient>>()
                .Setup(m => m.GetAsyncEnumerator(It.IsAny<CancellationToken>()))
                .Returns(GetMockAsyncEnumerator(_testProviderPatients));

            // Setup a new context with this DbSet
            var mockContext = new Mock<PracticeDatabaseContext>(
                new DbContextOptions<PracticeDatabaseContext>(),
                Mock.Of<IStringLocalizer<PracticeDataAccessLayerStrings>>(),
                Mock.Of<ILogger<PracticeDatabaseContext>>());
            mockContext.Setup(c => c.Set<ProviderPatient>()).Returns(mockDbSet.Object);

            // Create a new repository with this context
            var repository = new ProviderPatientRepository(mockContext.Object);

            // Act
            var result = await repository.GetAllAsync();

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count(), Is.EqualTo(_testProviderPatients.Count));
        }

        // Helper method to create a mock async enumerator
        private static IAsyncEnumerator<T> GetMockAsyncEnumerator<T>(IEnumerable<T> data)
        {
            var enumerator = new Mock<IAsyncEnumerator<T>>();
            var enumeration = data.GetEnumerator();

            enumerator.Setup(e => e.MoveNextAsync())
                .ReturnsAsync(enumeration.MoveNext);

            enumerator.Setup(e => e.Current)
                .Returns(() => enumeration.Current);

            return enumerator.Object;
        }

        [Test]
        public async Task GetByIdAsync_WithValidId_ShouldReturnProviderPatient()
        {
            // Arrange
            var id = _testProviderPatient.Id;

            // Act
            var result = await _repository.GetByIdAsync(id);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.EqualTo(id));
            Assert.That(result.SSN, Is.EqualTo(_testProviderPatient.SSN));
            Assert.That(result.PCPId, Is.EqualTo(_testProviderPatient.PCPId));
            Assert.That(result.Username, Is.EqualTo(_testProviderPatient.Username));
            _mockDbSet.Verify(m => m.FindAsync(new object[] { id }), Times.Once);
        }

        [Test]
        public async Task GetByIdAsync_WithInvalidId_ShouldReturnNull()
        {
            // Arrange
            var invalidId = Guid.NewGuid();

            // Act
            var result = await _repository.GetByIdAsync(invalidId);

            // Assert
            Assert.That(result, Is.Null);
            _mockDbSet.Verify(m => m.FindAsync(new object[] { invalidId }), Times.Once);
        }

        [Test]
        public async Task AddAsync_ShouldAddProviderPatientsToDbSet()
        {
            // Arrange
            var newProviderPatients = new List<ProviderPatient>
            {
                new ProviderPatient
                {
                    Id = Guid.NewGuid(),
                    PCPId = Guid.NewGuid(),
                    SSN = "***********",
                    Username = "newuser1"
                },
                new ProviderPatient
                {
                    Id = Guid.NewGuid(),
                    PCPId = Guid.NewGuid(),
                    SSN = "***********",
                    Username = "newuser2"
                }
            };

            // Act
            await _repository.AddAsync(newProviderPatients);

            // Assert
            _mockDbSet.Verify(m => m.AddRangeAsync(newProviderPatients, default), Times.Once);
        }

        [Test]
        public async Task UpdateAsync_ShouldUpdateProviderPatientAndSaveChanges()
        {
            // Arrange
            var providerPatientToUpdate = new ProviderPatient
            {
                Id = Guid.NewGuid(),
                PCPId = Guid.NewGuid(),
                SSN = "***********",
                Username = "updateduser"
            };

            // Act
            await _repository.UpdateAsync(providerPatientToUpdate);

            // Assert
            _mockDbSet.Verify(m => m.Update(providerPatientToUpdate), Times.Once);
            _mockContext.Verify(m => m.SaveChangesAsync(default), Times.Once);
        }

        [Test]
        public async Task DeleteByEntityAsync_ShouldRemoveProviderPatientAndSaveChanges()
        {
            // Arrange
            var providerPatientToDelete = new ProviderPatient
            {
                Id = Guid.NewGuid(),
                PCPId = Guid.NewGuid(),
                SSN = "***********",
                Username = "deleteuser"
            };

            // Act
            await _repository.DeleteByEntityAsync(providerPatientToDelete);

            // Assert
            _mockDbSet.Verify(m => m.Remove(providerPatientToDelete), Times.Once);
            _mockContext.Verify(m => m.SaveChangesAsync(default), Times.Once);
        }

        [Test]
        public async Task DeleteByIdAsync_WithExistingProviderPatient_ShouldDeleteProviderPatientAndSaveChanges()
        {
            // Arrange
            var id = _testProviderPatient.Id;

            // Act
            await _repository.DeleteByIdAsync(id);

            // Assert
            _mockDbSet.Verify(m => m.FindAsync(new object[] { id }), Times.Once);
            _mockDbSet.Verify(m => m.Remove(_testProviderPatient), Times.Once);
            _mockContext.Verify(m => m.SaveChangesAsync(default), Times.Once);
        }

        [Test]
        public async Task DeleteByIdAsync_WithNonExistingProviderPatient_ShouldNotDeleteAnything()
        {
            // Arrange
            var nonExistingId = Guid.NewGuid();

            // Act
            await _repository.DeleteByIdAsync(nonExistingId);

            // Assert
            _mockDbSet.Verify(m => m.FindAsync(new object[] { nonExistingId }), Times.Once);
            _mockDbSet.Verify(m => m.Remove(It.IsAny<ProviderPatient>()), Times.Never);
            _mockContext.Verify(m => m.SaveChangesAsync(default), Times.Never);
        }
    }
}
