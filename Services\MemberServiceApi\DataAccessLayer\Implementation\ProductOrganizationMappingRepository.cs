﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Contracts;
using DataAccessLayer.Context;
using Interfaces.ShardManagement;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Sprache;

namespace MemberServiceDataAccessLayer.Implementation
{
    public class ProductOrganizationMappingRepository : GenericRepository<ProductOrganizationMapping>, IProductOrganizationMappingRepository
    {
        private readonly AccountDatabaseContext _context;

        public ProductOrganizationMappingRepository(AccountDatabaseContext context) : base(context)
        {
            _context = context;
        }
    }
}
