﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore.Metadata;
using System;
using System.ComponentModel.DataAnnotations;

namespace Contracts
{
    public class ProductFeature : IContract
    {
        [Key]
        public Guid Id { get; set; }
        public string FeatureName { get; set; }
        public Guid ProdId { get; set; }
        public string ProdName { get; set; }
        public bool Status { get; set; } = true;
        public DateTime Created { get; set; } = DateTime.Now;
        public DateTime? Updated { get; set; }
    }
}
